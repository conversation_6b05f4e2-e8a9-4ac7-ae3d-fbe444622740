<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Models\SchoolSetting;
use App\Models\SecurityLog;

class EnhancedSecurityControl
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get school settings for maintenance mode check
        $schoolSetting = SchoolSetting::first();
        $isMaintenanceMode = $schoolSetting && $schoolSetting->maintenance_mode;

        // Log all access attempts for security monitoring
        $this->logAccessAttempt($request);

        // 1. MAINTENANCE MODE SECURITY
        if ($isMaintenanceMode) {
            return $this->handleMaintenanceMode($request, $next);
        }

        // 2. ROLE-BASED ACCESS CONTROL
        return $this->handleRoleBasedAccess($request, $next);
    }

    /**
     * Handle maintenance mode with strict security
     */
    private function handleMaintenanceMode(Request $request, Closure $next): Response
    {
        $currentRoute = $request->route() ? $request->route()->getName() : null;
        $currentPath = $request->path();

        // Super Admin Exception Routes - ONLY these routes are accessible during maintenance
        $superAdminRoutes = [
            'maintenance',
            'super-admin.login',
            'super-admin.logout',
            'super-admin.dashboard',
        ];

        // Check if user is authenticated super admin
        $isSuperAdmin = Auth::check() && Auth::user()->user_type === 'super_admin';

        // Allow super admin access to special routes
        if ($isSuperAdmin && (
            $currentRoute === 'super-admin.dashboard' ||
            $currentPath === 'super-admin' ||
            $currentPath === 'super-admin/dashboard' ||
            str_starts_with($currentPath, 'super-admin/')
        )) {
            return $next($request);
        }

        // Allow super admin login route (but redirect if already logged in)
        if ($currentRoute === 'super-admin.login' || $currentPath === 'super-admin/login') {
            // If super admin is already logged in, redirect to dashboard
            if ($isSuperAdmin) {
                return redirect()->route('super-admin.dashboard');
            }
            return $next($request);
        }

        // Allow maintenance page
        if ($currentRoute === 'maintenance' || $currentPath === 'maintenance') {
            return $next($request);
        }

        // Block ALL other access during maintenance
        $this->logBlockedAccess($request, 'maintenance_mode_block');
        return redirect()->route('maintenance');
    }

    /**
     * Handle role-based access control
     */
    private function handleRoleBasedAccess(Request $request, Closure $next): Response
    {
        $currentRoute = $request->route() ? $request->route()->getName() : null;
        $currentPath = $request->path();

        // Define protected routes and their required roles
        $protectedRoutes = [
            // Admin routes
            'admin.*' => ['super_admin', 'admin'],

            // Super admin only routes
            'admin.users.*' => ['super_admin'],
            'admin.settings.*' => ['super_admin'],
            'admin.security.*' => ['super_admin'],

            // Dashboard routes
            'dashboard' => ['super_admin', 'admin', 'guru', 'siswa', 'orang_tua', 'calon_siswa'],

            // Profile routes
            'profile.*' => ['super_admin', 'admin', 'guru', 'siswa', 'orang_tua', 'calon_siswa'],
        ];

        // Check if current route is protected
        $requiredRoles = $this->getRequiredRoles($currentRoute, $protectedRoutes);

        if ($requiredRoles) {
            // Check authentication
            if (!Auth::check()) {
                $this->logBlockedAccess($request, 'unauthenticated_access');
                return redirect()->route('login');
            }

            // Check role authorization
            $userRole = Auth::user()->user_type;
            if (!in_array($userRole, $requiredRoles)) {
                $this->logBlockedAccess($request, 'unauthorized_role_access', [
                    'user_role' => $userRole,
                    'required_roles' => $requiredRoles
                ]);
                abort(403, 'Akses tidak diizinkan untuk role Anda.');
            }
        }

        // Prevent direct URL access for sensitive routes
        if ($this->isDirectUrlAccess($request) && $this->isSensitiveRoute($currentRoute, $currentPath)) {
            $this->logBlockedAccess($request, 'direct_url_access_blocked');
            
            // Redirect to appropriate dashboard based on role
            if (Auth::check()) {
                return $this->redirectToDashboard(Auth::user()->user_type);
            }
            
            return redirect()->route('landing');
        }

        return $next($request);
    }

    /**
     * Get required roles for a route
     */
    private function getRequiredRoles(?string $routeName, array $protectedRoutes): ?array
    {
        if (!$routeName) {
            return null;
        }

        foreach ($protectedRoutes as $pattern => $roles) {
            if (fnmatch($pattern, $routeName)) {
                return $roles;
            }
        }

        return null;
    }

    /**
     * Check if this is a direct URL access (not from navigation)
     */
    private function isDirectUrlAccess(Request $request): bool
    {
        $referer = $request->header('referer');
        $currentDomain = $request->getSchemeAndHttpHost();

        // If no referer or referer is from external domain, it's likely direct access
        if (!$referer || !str_starts_with($referer, $currentDomain)) {
            return true;
        }

        return false;
    }

    /**
     * Check if route is sensitive and should not allow direct access
     */
    private function isSensitiveRoute(?string $routeName, string $currentPath): bool
    {
        $sensitivePatterns = [
            'admin.*',
            'users.*',
            'settings.*',
            'security.*',
            'ppdb.register',
            'profile.edit',
        ];

        $sensitivePaths = [
            'admin',
            'dashboard',
            'profile',
            'users',
            'settings',
        ];

        // Check route patterns
        if ($routeName) {
            foreach ($sensitivePatterns as $pattern) {
                if (fnmatch($pattern, $routeName)) {
                    return true;
                }
            }
        }

        // Check path patterns
        foreach ($sensitivePaths as $path) {
            if (str_starts_with($currentPath, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Redirect to appropriate dashboard based on user role
     */
    private function redirectToDashboard(string $userType): Response
    {
        switch ($userType) {
            case 'super_admin':
            case 'admin':
                return redirect()->route('admin.dashboard');
            case 'guru':
                return redirect()->route('dashboard');
            case 'siswa':
                return redirect()->route('dashboard');
            case 'orang_tua':
                return redirect()->route('dashboard');
            case 'calon_siswa':
                return redirect()->route('dashboard');
            default:
                return redirect()->route('landing');
        }
    }

    /**
     * Log access attempts for security monitoring
     */
    private function logAccessAttempt(Request $request): void
    {
        $route = $request->route();
        
        SecurityLog::logEvent('access_attempt', Auth::id(), [
            'route' => $route ? $route->getName() : 'unknown',
            'path' => $request->path(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
        ]);
    }

    /**
     * Log blocked access attempts
     */
    private function logBlockedAccess(Request $request, string $reason, array $additionalData = []): void
    {
        $route = $request->route();
        
        SecurityLog::logEvent('access_blocked', Auth::id(), array_merge([
            'reason' => $reason,
            'route' => $route ? $route->getName() : 'unknown',
            'path' => $request->path(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
        ], $additionalData));
    }
}
