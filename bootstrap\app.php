<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Global middleware
        $middleware->web(append: [
            \App\Http\Middleware\SecurityHeaders::class,
            \App\Http\Middleware\SessionSecurity::class,
            \App\Http\Middleware\SecurityLogger::class, // Re-enabled for security monitoring
            \App\Http\Middleware\EnhancedSecurityControl::class, // Enhanced security with maintenance mode
            // \App\Http\Middleware\RequestDebugger::class, // Keep disabled for production
        ]);

        // Middleware aliases
        $middleware->alias([
            'check.account.lock' => \App\Http\Middleware\CheckAccountLock::class,
            'prevent.double.login' => \App\Http\Middleware\PreventDoubleLogin::class,
            'security.logger' => \App\Http\Middleware\SecurityLogger::class,
            'security.headers' => \App\Http\Middleware\SecurityHeaders::class,
            'secure.upload' => \App\Http\Middleware\SecureFileUpload::class,
            'prevent.double.login' => \App\Http\Middleware\PreventDoubleLogin::class,
            'rate.limit' => \App\Http\Middleware\RateLimitMiddleware::class,
            'user.type' => \App\Http\Middleware\CheckUserType::class,
            'enhanced.security' => \App\Http\Middleware\EnhancedSecurityControl::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'request.debug' => \App\Http\Middleware\RequestDebugger::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
