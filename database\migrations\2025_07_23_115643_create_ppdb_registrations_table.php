<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ppdb_registrations', function (Blueprint $table) {
            $table->id();
            $table->string('registration_number')->unique(); // Nomor pendaftaran
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('ppdb_setting_id')->constrained()->onDelete('cascade');
            $table->foreignId('program_id')->constrained()->onDelete('cascade');

            // Data Pribadi
            $table->string('full_name');
            $table->string('nik')->unique();
            $table->string('nisn')->nullable();
            $table->enum('gender', ['L', 'P']);
            $table->date('birth_date');
            $table->string('birth_place');
            $table->string('religion');
            $table->text('address');
            $table->string('phone');
            $table->string('email');

            // Data Orang Tua
            $table->string('father_name');
            $table->string('father_occupation')->nullable();
            $table->string('father_phone')->nullable();
            $table->string('mother_name');
            $table->string('mother_occupation')->nullable();
            $table->string('mother_phone')->nullable();

            // Data Sekolah Asal
            $table->string('previous_school');
            $table->string('previous_school_address')->nullable();
            $table->decimal('final_grade', 5, 2)->nullable(); // Nilai rata-rata

            // Status Pendaftaran
            $table->enum('status', ['pending', 'verified', 'approved', 'rejected', 'enrolled'])->default('pending');
            $table->text('notes')->nullable(); // Catatan admin
            $table->decimal('test_score', 5, 2)->nullable(); // Nilai tes
            $table->boolean('payment_verified')->default(false);
            $table->datetime('verified_at')->nullable();
            $table->datetime('approved_at')->nullable();

            $table->timestamps();

            $table->index(['status', 'program_id']);
            $table->index('registration_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ppdb_registrations');
    }
};
