@extends('layouts.landing')

@section('title', 'Beranda')
@section('description', $schoolSettings->school_description ?? 'Website resmi sekolah terbaik untuk masa depan yang cerah')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/placeholder.css') }}">
<style>
    /* Hero Section */
    .hero-section {
        height: 100vh;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8)), url('https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .hero-slider {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }

    .hero-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 1s ease-in-out;
    }

    .hero-slide.active {
        opacity: 1;
    }

    .hero-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .hero-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .hero-video-embed {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80%;
        max-width: 800px;
        aspect-ratio: 16/9;
        z-index: 5;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .hero-video-embed iframe {
        width: 100%;
        height: 100%;
        border: none;
    }

    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.7), rgba(118, 75, 162, 0.7));
        z-index: 2;
    }

    .hero-content {
        color: white;
        text-align: center;
        z-index: 10;
        position: relative;
    }

    .hero-content-slide {
        display: none;
        animation: fadeInUp 1s ease-out;
    }

    .hero-content-slide.active {
        display: block;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        animation: fadeInUp 1s ease-out;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.3s both;
    }

    .hero-buttons {
        animation: fadeInUp 1s ease-out 0.6s both;
    }

    .hero-buttons .btn {
        margin: 0 10px;
        padding: 15px 35px;
        font-size: 1.1rem;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        opacity: 0.8;
        animation: fadeInUp 1s ease-out 0.4s both;
    }

    /* Hero Navigation */
    .hero-navigation {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        z-index: 15;
        pointer-events: none;
    }

    .hero-nav-btn {
        position: absolute;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        pointer-events: all;
        backdrop-filter: blur(10px);
    }

    .hero-nav-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: scale(1.1);
    }

    .hero-prev {
        left: 30px;
    }

    .hero-next {
        right: 30px;
    }

    /* Hero Indicators */
    .hero-indicators {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 10px;
        z-index: 15;
    }

    .hero-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.5);
        background: transparent;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .hero-indicator.active,
    .hero-indicator:hover {
        background: white;
        border-color: white;
    }

    /* Stats Section */
    .stats-section {
        background: white;
        padding: 60px 0;
        margin-top: -50px;
        position: relative;
        z-index: 3;
    }

    .stat-card {
        text-align: center;
        padding: 2rem;
        border-radius: 15px;
        background: white;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-weight: 500;
    }

    /* About Section */
    .about-section {
        background: #f8f9fa;
    }

    .about-image {
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .about-image img {
        width: 100%;
        height: 400px;
        object-fit: cover;
    }

    /* Programs Section */
    .program-card,
    .facility-card,
    .gallery-card {
        height: 100%;
        border: none;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .program-card:hover,
    .facility-card:hover,
    .gallery-card:hover {
        transform: translateY(-10px);
    }

    .program-icon,
    .facility-icon,
    .gallery-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    /* News Section */
    .news-card {
        height: 100%;
        transition: all 0.3s ease;
    }

    .news-card:hover {
        transform: translateY(-5px);
    }

    .news-image {
        height: 200px;
        overflow: hidden;
    }

    .news-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .news-card:hover .news-image img {
        transform: scale(1.1);
    }

    .news-meta {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }





    /* Placeholder Images Styles */
    .placeholder-image,
    .placeholder-facility,
    .placeholder-news,
    .placeholder-gallery {
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .placeholder-image:hover,
    .placeholder-facility:hover,
    .placeholder-news:hover,
    .placeholder-gallery:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }

    .facility-bg.placeholder-facility {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 15px;
    }

    .ppdb-placeholder {
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
        color: #000 !important;
        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
    }

    /* Contact Cards */
    .contact-card {
        background: #fff;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        height: 100%;
    }

    .contact-card:hover {
        transform: translateY(-5px);
    }

    .contact-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 2rem;
    }

    .placeholder-news,
    .placeholder-gallery {
        width: 100%;
    }

    @media (max-width: 768px) {
        .placeholder-image {
            width: 100% !important;
            height: 250px !important;
        }

        .placeholder-facility,
        .placeholder-news,
        .placeholder-gallery {
            font-size: 14px;
        }
    }

    /* CTA Section */
    .cta-section {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        text-align: center;
    }

    .cta-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .cta-subtitle {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .hero-subtitle {
            font-size: 1.1rem;
        }
        
        .hero-buttons .btn {
            display: block;
            margin: 10px auto;
            width: 80%;
        }
        
        .stat-number {
            font-size: 2rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-section" id="home">
    @if($heroSections->isNotEmpty())
        <!-- Hero Slides -->
        <div class="hero-slider">
            @foreach($heroSections as $index => $hero)
                <div class="hero-slide {{ $index === 0 ? 'active' : '' }}" data-slide="{{ $index }}">
                    @if($hero->video_url)
                        @php
                            // Parse video URL to get embed URL
                            $embedUrl = '';
                            if (strpos($hero->video_url, 'youtube.com') !== false || strpos($hero->video_url, 'youtu.be') !== false) {
                                // YouTube URL
                                preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $hero->video_url, $matches);
                                if (isset($matches[1])) {
                                    $embedUrl = 'https://www.youtube.com/embed/' . $matches[1] . '?autoplay=1&mute=1&loop=1&playlist=' . $matches[1] . '&controls=0&showinfo=0&rel=0';
                                }
                            } elseif (strpos($hero->video_url, 'vimeo.com') !== false) {
                                // Vimeo URL
                                preg_match('/vimeo\.com\/(\d+)/', $hero->video_url, $matches);
                                if (isset($matches[1])) {
                                    $embedUrl = 'https://player.vimeo.com/video/' . $matches[1] . '?autoplay=1&muted=1&loop=1&background=1';
                                }
                            }
                        @endphp

                        @if($embedUrl)
                            <div class="hero-video-embed">
                                <iframe src="{{ $embedUrl }}" allow="autoplay; fullscreen; picture-in-picture"></iframe>
                            </div>
                        @endif
                    @elseif($hero->image)
                        <img src="{{ asset('storage/' . $hero->image) }}" alt="{{ $hero->title }}" class="hero-bg">
                    @endif
                    <div class="hero-overlay"></div>
                </div>
            @endforeach
        </div>

        <!-- Hero Content -->
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="hero-content">
                        @foreach($heroSections as $index => $hero)
                            <div class="hero-content-slide {{ $index === 0 ? 'active' : '' }}" data-content="{{ $index }}">
                                <h1 class="hero-title">{{ $hero->title }}</h1>
                                @if($hero->subtitle)
                                    <p class="hero-subtitle">{{ $hero->subtitle }}</p>
                                @endif
                                @if($hero->description)
                                    <p class="hero-description">{{ $hero->description }}</p>
                                @endif

                                <div class="hero-buttons">
                                    @if($hero->button_text && $hero->button_link)
                                        <a href="{{ $hero->button_link }}" class="btn btn-light btn-lg me-3">
                                            <i class="fas fa-arrow-right me-2"></i>{{ $hero->button_text }}
                                        </a>
                                    @endif
                                    <a href="#about" class="btn btn-outline-light btn-lg">
                                        <i class="fas fa-info-circle me-2"></i>Tentang Kami
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Hero Navigation -->
        @if($heroSections->count() > 1)
            <div class="hero-navigation">
                <button class="hero-nav-btn hero-prev" onclick="changeHeroSlide(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="hero-nav-btn hero-next" onclick="changeHeroSlide(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- Hero Indicators -->
            <div class="hero-indicators">
                @foreach($heroSections as $index => $hero)
                    <button class="hero-indicator {{ $index === 0 ? 'active' : '' }}"
                            onclick="goToHeroSlide({{ $index }})"
                            data-slide="{{ $index }}"></button>
                @endforeach
            </div>
        @endif
    @else
        <!-- Default Hero Content -->
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="hero-content">
                        <h1 class="hero-title">Selamat Datang di {{ $schoolSettings->school_name ?? 'Sekolahku' }}</h1>
                        <p class="hero-subtitle">Membangun generasi unggul dengan pendidikan berkualitas dan berkarakter</p>

                        <div class="hero-buttons">
                            <a href="#about" class="btn btn-light btn-lg">
                                <i class="fas fa-info-circle me-2"></i>Tentang Kami
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number">{{ $schoolSettings->total_students ?? '1000' }}+</div>
                    <div class="stat-label">Siswa Aktif</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="stat-number">{{ $schoolSettings->total_teachers ?? '50' }}+</div>
                    <div class="stat-label">Tenaga Pendidik</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-number">{{ $schoolSettings->established_year ?? '2000' }}</div>
                    <div class="stat-label">Tahun Berdiri</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-number">100+</div>
                    <div class="stat-label">Prestasi</div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- About Section -->
<section class="section about-section" id="about">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="about-image">
                    @if($schoolSettings && $schoolSettings->principal_photo)
                        <img src="{{ asset('storage/' . $schoolSettings->principal_photo) }}" alt="Kepala Sekolah">
                    @else
                        <div class="placeholder-container placeholder-principal">
                            <div class="text-center">
                                <i class="fas fa-user-tie fa-3x placeholder-icon"></i>
                                <div>Kepala Sekolah</div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="about-content">
                    <h2 class="mb-4">Sambutan Kepala Sekolah</h2>
                    @if($schoolSettings && $schoolSettings->principal_name)
                        <h5 class="text-primary mb-3">{{ $schoolSettings->principal_name }}</h5>
                    @endif

                    @if($schoolSettings && $schoolSettings->principal_message)
                        <p class="mb-4">{{ $schoolSettings->principal_message }}</p>
                    @else
                        <p class="mb-4">Selamat datang di website resmi sekolah kami. Kami berkomitmen untuk memberikan pendidikan terbaik bagi putra-putri Anda dengan mengembangkan potensi akademik dan karakter yang kuat.</p>
                        <p class="mb-4">Dengan fasilitas modern dan tenaga pendidik yang berkualitas, kami siap membantu siswa meraih prestasi terbaik dan mempersiapkan mereka untuk masa depan yang cerah.</p>
                    @endif

                    <a href="#programs" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>Lihat Program Kami
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Vision Mission Section -->
<section class="section" id="vision-mission">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Visi & Misi</h2>
            <p>Landasan filosofi yang mengarahkan setiap langkah pendidikan kami</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="card h-100">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-eye fa-3x text-primary"></i>
                        </div>
                        <h4 class="mb-3 text-dark">Visi</h4>
                        <p class="lead text-dark">
                            @if($schoolSettings && $schoolSettings->vision)
                                {{ $schoolSettings->vision }}
                            @else
                                Menjadi sekolah unggul yang menghasilkan generasi berkarakter, berprestasi, dan siap menghadapi tantangan global.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="card h-100">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-bullseye fa-3x text-primary"></i>
                        </div>
                        <h4 class="mb-3 text-dark">Misi</h4>
                        <div class="text-start">
                            @if($schoolSettings && $schoolSettings->mission)
                                <p>{{ $schoolSettings->mission }}</p>
                            @else
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Menyelenggarakan pendidikan berkualitas tinggi</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Mengembangkan karakter dan akhlak mulia</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Memfasilitasi pengembangan bakat dan minat siswa</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Menciptakan lingkungan belajar yang kondusif</li>
                                </ul>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Programs Section -->
<section class="section" id="programs" style="background: #f8f9fa;">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Program Pendidikan</h2>
            <p>Berbagai program unggulan yang dirancang untuk mengembangkan potensi siswa secara optimal</p>
        </div>

        <div class="row g-4">
            @forelse($programs as $program)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="card program-card">
                        @if($program->image)
                            <img src="{{ asset('storage/' . $program->image) }}" class="card-img-top" alt="{{ $program->name }}" style="height: 200px; object-fit: cover;">
                        @endif
                        <div class="card-body">
                            <div class="program-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <h5 class="card-title text-dark">{{ $program->name }}</h5>
                            <p class="card-text text-dark">{{ Str::limit($program->description, 100) }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary">{{ $program->level }}</span>
                                <a href="{{ route('landing.program', $program->slug) }}" class="btn btn-outline-primary btn-sm">
                                    Selengkapnya
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <p class="text-muted">Program pendidikan akan segera diumumkan.</p>
                </div>
            @endforelse
        </div>

        <!-- Lihat Selengkapnya Button -->
        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
            <a href="{{ route('landing.programs') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-graduation-cap me-2"></i>Lihat Semua Program
            </a>
        </div>
    </div>
</section>

<!-- Facilities Section -->
<section class="section" id="facilities">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Fasilitas Sekolah</h2>
            <p>Fasilitas modern dan lengkap untuk mendukung proses pembelajaran yang optimal</p>
        </div>

        <div class="row g-4">
            @forelse($facilities as $facility)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="card facility-card">
                        @if($facility->image)
                            <img src="{{ asset('storage/' . $facility->image) }}" class="card-img-top" alt="{{ $facility->name }}" style="height: 200px; object-fit: cover;">
                        @else
                            <div class="placeholder-container placeholder-facility" style="height: 200px;">
                                <div class="text-center">
                                    <i class="fas fa-building fa-2x placeholder-icon"></i>
                                    <div>{{ $facility->name }}</div>
                                </div>
                            </div>
                        @endif
                        <div class="card-body">
                            <div class="facility-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <h5 class="card-title text-dark">{{ $facility->name }}</h5>
                            <p class="card-text text-dark">{{ Str::limit($facility->description, 100) }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary">Fasilitas</span>
                                <a href="{{ route('landing.facility', $facility->slug) }}" class="btn btn-outline-primary btn-sm">
                                    Selengkapnya
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <p class="text-muted">Informasi fasilitas akan segera diperbarui.</p>
                </div>
            @endforelse
        </div>

        <!-- Lihat Selengkapnya Button -->
        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
            <a href="{{ route('landing.facilities') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-building me-2"></i>Lihat Semua Fasilitas
            </a>
        </div>
    </div>
</section>

<!-- News Section -->
<section class="section" id="news" style="background: #f8f9fa;">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Berita & Pengumuman</h2>
            <p>Informasi terkini seputar kegiatan dan pencapaian sekolah</p>
        </div>

        <div class="row g-4">
            @forelse($latestNews as $news)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    @php
                        // Check if this is PPDB related news for special styling
                        $isPPDBNews = str_contains(strtolower($news->title), 'ppdb') || str_contains(strtolower($news->title), 'penerimaan peserta didik baru');
                    @endphp

                    {{-- Featured articles get special styling and badge --}}
                    <div class="card news-card {{ $news->is_featured ? 'border-warning shadow-lg' : '' }}">
                        @if($news->is_featured)
                            <div class="position-absolute top-0 start-0 m-2">
                                {{-- PPDB news get special PPDB ONLINE badge --}}
                                @if($isPPDBNews)
                                    <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                        <i class="fas fa-star me-1"></i>PPDB ONLINE
                                    </span>
                                {{-- Other featured news get generic FEATURED badge --}}
                                @else
                                    <span class="badge bg-primary text-white fs-6 px-3 py-2">
                                        <i class="fas fa-star me-1"></i>FEATURED
                                    </span>
                                @endif
                            </div>
                        @endif

                        <div class="news-image">
                            @if($news->featured_image)
                                <img src="{{ asset('storage/' . $news->featured_image) }}" alt="{{ $news->title }}">
                            @elseif($isPPDBNews)
                                <div class="placeholder-container placeholder-news ppdb">
                                    <div class="text-center">
                                        <i class="fas fa-graduation-cap fa-2x placeholder-icon"></i>
                                        <div>PPDB ONLINE</div>
                                    </div>
                                </div>
                            @else
                                <div class="placeholder-container placeholder-news">
                                    <div class="text-center">
                                        <i class="fas fa-newspaper fa-2x placeholder-icon"></i>
                                        <div>Berita</div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="card-body">
                            <div class="news-meta">
                                <i class="fas fa-calendar me-1"></i>{{ $news->published_at->format('d M Y') }}
                                <span class="ms-3">
                                    <i class="fas fa-eye me-1"></i>{{ $news->views }} views
                                </span>
                                @if($news->type == 'announcement')
                                    <span class="badge bg-warning text-dark ms-2">Pengumuman</span>
                                @endif
                            </div>
                            <h5 class="card-title {{ $news->is_featured ? 'fw-bold' : '' }} {{ $isPPDBNews ? 'text-warning' : '' }}">{{ Str::limit($news->title, 60) }}</h5>
                            <p class="card-text">{{ Str::limit($news->excerpt, 100) }}</p>

                            @if($isPPDBNews && $ppdbSetting && $ppdbSetting->isOpen())
                                <div class="d-flex gap-2">
                                    <a href="{{ route('ppdb.index') }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-user-plus me-1"></i>Daftar Sekarang
                                    </a>
                                    <a href="{{ route('landing.news', $news->slug) }}" class="btn btn-outline-primary btn-sm">
                                        Baca Selengkapnya
                                    </a>
                                </div>
                            @else
                                <a href="{{ route('landing.news', $news->slug) }}" class="btn btn-primary btn-sm">
                                    Baca Selengkapnya
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <p class="text-muted">Berita akan segera diperbarui.</p>
                </div>
            @endforelse
        </div>

        @if($latestNews->count() > 0)
            <div class="text-center mt-5" data-aos="fade-up">
                <a href="{{ route('landing.news.index') }}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-newspaper me-2"></i>Lihat Semua Berita
                </a>
            </div>
        @endif
    </div>
</section>



<!-- Gallery Preview Section -->
<section class="section" id="gallery" style="background: #f8f9fa;">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Galeri Sekolah</h2>
            <p>Dokumentasi kegiatan, fasilitas, dan momen berharga di sekolah kami</p>
        </div>

        <div class="row g-4">
            @forelse($galleryImages->take(6) as $image)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="card gallery-card">
                        @if($image->file_path && file_exists(storage_path('app/public/' . $image->file_path)))
                            <img src="{{ asset('storage/' . $image->file_path) }}" class="card-img-top" alt="{{ $image->title }}" style="height: 200px; object-fit: cover;">
                        @else
                            <div class="placeholder-container placeholder-gallery" style="height: 200px;">
                                <div class="text-center">
                                    <i class="fas fa-image fa-2x placeholder-icon"></i>
                                    <div>{{ Str::limit($image->title, 20) }}</div>
                                </div>
                            </div>
                        @endif
                        <div class="card-body">
                            <div class="gallery-icon">
                                <i class="fas fa-images"></i>
                            </div>
                            <h5 class="card-title text-dark">{{ $image->title }}</h5>
                            <p class="card-text text-dark">{{ Str::limit($image->description ?? 'Dokumentasi kegiatan sekolah', 100) }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary">{{ ucfirst($image->category) }}</span>
                                <a href="{{ route('landing.gallery') }}" class="btn btn-outline-primary btn-sm">
                                    Selengkapnya
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <p class="text-muted">Galeri foto akan segera diperbarui.</p>
                </div>
            @endforelse
        </div>

        @if($galleryImages->count() > 0)
            <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
                <a href="{{ route('landing.gallery') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-images me-2"></i>Lihat Semua Galeri
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Staff Section -->
<section class="section" id="staff">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Tenaga Pendidik</h2>
            <p>Tim pengajar berpengalaman dan berkualitas yang siap membimbing siswa meraih prestasi terbaik</p>
        </div>

        <div class="row g-4">
            @forelse($featuredStaff as $staff)
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                @if($staff->user->avatar)
                                    <img src="{{ asset('storage/' . $staff->user->avatar) }}" alt="{{ $staff->user->name }}" class="rounded-circle border-3 border-primary" width="100" height="100" style="object-fit: cover;">
                                @else
                                    <div class="rounded-circle bg-gradient d-inline-flex align-items-center justify-content-center border-3 border-primary" style="width: 100px; height: 100px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                @endif
                            </div>

                            <!-- Nama Lengkap -->
                            <h5 class="card-title mb-1 fw-bold">{{ $staff->user->name }}</h5>

                            <!-- Gelar -->
                            @if($staff->degree)
                                <p class="text-primary mb-2 fw-semibold">{{ $staff->degree }}</p>
                            @endif

                            <!-- Posisi -->
                            <p class="text-muted mb-2">
                                @if($staff->position == 'kepala_sekolah')
                                    Kepala Sekolah
                                @elseif($staff->position == 'wakil_kepala_sekolah')
                                    Wakil Kepala Sekolah
                                @elseif($staff->position == 'guru')
                                    Guru {{ $staff->subject ?? '' }}
                                @else
                                    {{ ucfirst(str_replace('_', ' ', $staff->position)) }}
                                @endif
                            </p>

                            <!-- Subject untuk Guru -->
                            @if($staff->subject && $staff->position == 'guru')
                                <span class="badge bg-light text-primary mb-2">{{ $staff->subject }}</span>
                            @endif

                            <!-- Bio singkat -->
                            @if($staff->bio)
                                <p class="card-text small text-muted">{{ Str::limit($staff->bio, 60) }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Informasi tenaga pendidik akan segera diperbarui.
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</section>

<!-- PPDB Section -->
<section class="section bg-warning" id="ppdb">
    <div class="container">
        <div class="section-title text-dark" data-aos="fade-up">
            <h2>PPDB Online</h2>
            <p>Penerimaan Peserta Didik Baru - Daftar sekarang untuk masa depan yang cerah</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5 text-center">
                        @if($ppdbSetting)
                            @php
                                $ppdbStatus = $ppdbSetting->getRegistrationStatus();
                            @endphp

                            @if($ppdbStatus === 'open')
                                <div class="mb-4">
                                    <i class="fas fa-graduation-cap fa-4x text-warning mb-3"></i>
                                    <h3 class="text-dark">Pendaftaran Dibuka!</h3>
                                    <p class="text-muted">
                                    Periode pendaftaran:
                                    {{ $ppdbSetting->registration_start ? $ppdbSetting->registration_start->format('d M Y') : '-' }} -
                                    {{ $ppdbSetting->registration_end ? $ppdbSetting->registration_end->format('d M Y') : '-' }}
                                </p>
                                </div>

                                <div class="d-flex justify-content-center gap-3">
                                    <a href="{{ route('ppdb.index') }}" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-eye me-2"></i>Lihat Selengkapnya
                                    </a>
                                </div>
                            @else
                                <div class="mb-4">
                                    <i class="fas fa-clock fa-4x text-muted mb-3"></i>
                                    <h3 class="text-dark">
                                        @if($ppdbStatus === 'maintenance')
                                            Pendaftaran Sedang Maintenance
                                        @elseif($ppdbStatus === 'not_started')
                                            Pendaftaran Belum Dimulai
                                        @elseif($ppdbStatus === 'ended')
                                            Pendaftaran Telah Berakhir
                                        @elseif($ppdbStatus === 'closed')
                                            Pendaftaran Ditutup
                                        @else
                                            Pendaftaran Belum Dibuka
                                        @endif
                                    </h3>
                                    <p class="text-muted">
                                        @if($ppdbStatus === 'maintenance')
                                            Sistem PPDB sedang dalam perbaikan. Mohon tunggu pengumuman selanjutnya.
                                        @elseif($ppdbStatus === 'not_started')
                                            Pendaftaran akan dimulai pada {{ $ppdbSetting->registration_start ? $ppdbSetting->registration_start->format('d M Y') : 'tanggal yang akan ditentukan' }}.
                                        @elseif($ppdbStatus === 'ended')
                                            Periode pendaftaran telah berakhir pada {{ $ppdbSetting->registration_end ? $ppdbSetting->registration_end->format('d M Y') : '-' }}.
                                        @else
                                            Pantau terus website kami untuk informasi pembukaan pendaftaran
                                        @endif
                                    </p>
                                </div>

                                <div class="d-flex justify-content-center gap-3">
                                    <a href="#contact" class="btn btn-primary btn-lg">
                                        <i class="fas fa-bell me-2"></i>Hubungi Kami untuk Info
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="mb-4">
                                <i class="fas fa-exclamation-triangle fa-4x text-muted mb-3"></i>
                                <h3 class="text-dark">PPDB Belum Dikonfigurasi</h3>
                                <p class="text-muted">Informasi PPDB akan segera tersedia.</p>
                            </div>

                            <div class="d-flex justify-content-center gap-3">
                                <a href="#contact" class="btn btn-primary btn-lg">
                                    <i class="fas fa-bell me-2"></i>Hubungi Kami untuk Info
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="section" id="contact">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>Hubungi Kami</h2>
            <p>Kami siap membantu menjawab pertanyaan dan memberikan informasi yang Anda butuhkan</p>
        </div>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="row g-4">
                    <!-- Contact Info Cards -->
                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                        <div class="contact-card text-center">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <h5>Alamat</h5>
                            <p>Jl. Pendidikan No. 123<br>Jakarta Selatan, 12345</p>
                        </div>
                    </div>

                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                        <div class="contact-card text-center">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <h5>Telepon</h5>
                            <p>(*************<br>(*************</p>
                        </div>
                    </div>

                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                        <div class="contact-card text-center">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h5>Email</h5>
                            <p><EMAIL><br><EMAIL></p>
                        </div>
                    </div>
                </div>

                <!-- Quick Contact Buttons -->
                <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <a href="tel:+6221234567" class="btn btn-primary">
                            <i class="fas fa-phone me-2"></i>Telepon Langsung
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Kirim Email
                        </a>
                        <a href="https://wa.me/6281234567890" target="_blank" class="btn btn-success">
                            <i class="fab fa-whatsapp me-2"></i>WhatsApp
                        </a>
                        <a href="{{ route('landing.contact') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-map me-2"></i>Halaman Kontak
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section cta-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center" data-aos="fade-up">
                <h2 class="cta-title">Bergabunglah dengan Kami</h2>
                <p class="cta-subtitle">Wujudkan impian pendidikan terbaik untuk masa depan yang cerah</p>
                <div class="cta-buttons">
                    @if($ppdbSetting && $ppdbSetting->isOpen())
                        <a href="{{ route('ppdb.index') }}" class="btn btn-warning btn-lg me-3">
                            <i class="fas fa-user-graduate me-2"></i>Daftar PPDB
                        </a>
                    @endif
                    <a href="#contact" class="btn btn-light btn-lg">
                        <i class="fas fa-phone me-2"></i>Hubungi Kami
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>


@endsection

@push('scripts')
<script>
    // Initialize AOS with fallback
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    }

    // Handle image loading errors and add placeholder functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Handle external image errors (fallback for any remaining external images)
        const images = document.querySelectorAll('img[src*="via.placeholder.com"], img[src*="placeholder"]');
        images.forEach(function(img) {
            img.onerror = function() {
                // Replace with local placeholder
                const parent = this.parentElement;
                const alt = this.alt || 'Image';
                const placeholder = document.createElement('div');
                placeholder.className = 'placeholder-container placeholder-news';
                placeholder.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-image fa-2x placeholder-icon"></i>
                        <div>${alt}</div>
                    </div>
                `;
                parent.replaceChild(placeholder, this);
            };
        });

        // Add loading animation to placeholders
        const placeholders = document.querySelectorAll('.placeholder-container');
        placeholders.forEach(function(placeholder, index) {
            placeholder.classList.add('placeholder-loading');

            // Remove loading after animation with staggered timing
            setTimeout(function() {
                placeholder.classList.remove('placeholder-loading');
            }, 1000 + (index * 200));
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add hover effects to cards
        const cards = document.querySelectorAll('.news-card, .program-card, .facility-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });

    // Performance optimization: Lazy load images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Hero Slider Functionality
    let currentHeroSlide = 0;
    const heroSlides = document.querySelectorAll('.hero-slide');
    const heroContentSlides = document.querySelectorAll('.hero-content-slide');
    const heroIndicators = document.querySelectorAll('.hero-indicator');
    const totalHeroSlides = heroSlides.length;

    function showHeroSlide(index) {
        // Hide all slides
        heroSlides.forEach(slide => slide.classList.remove('active'));
        heroContentSlides.forEach(content => content.classList.remove('active'));
        heroIndicators.forEach(indicator => indicator.classList.remove('active'));

        // Show current slide
        if (heroSlides[index]) {
            heroSlides[index].classList.add('active');
        }
        if (heroContentSlides[index]) {
            heroContentSlides[index].classList.add('active');
        }
        if (heroIndicators[index]) {
            heroIndicators[index].classList.add('active');
        }

        currentHeroSlide = index;
    }

    function changeHeroSlide(direction) {
        let newIndex = currentHeroSlide + direction;

        if (newIndex >= totalHeroSlides) {
            newIndex = 0;
        } else if (newIndex < 0) {
            newIndex = totalHeroSlides - 1;
        }

        showHeroSlide(newIndex);
    }

    function goToHeroSlide(index) {
        showHeroSlide(index);
    }

    // Auto-advance hero slides
    if (totalHeroSlides > 1) {
        setInterval(() => {
            changeHeroSlide(1);
        }, 5000); // Change slide every 5 seconds
    }

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (totalHeroSlides > 1) {
            if (e.key === 'ArrowLeft') {
                changeHeroSlide(-1);
            } else if (e.key === 'ArrowRight') {
                changeHeroSlide(1);
            }
        }
    });
</script>
@endpush


