<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\StudentProfile;
use App\Models\ParentProfile;
use Illuminate\Support\Facades\Hash;

class StudentParentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Parent User
        $parentUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Budi Hartono',
                'user_type' => 'orang_tua',
                'phone' => '08123456700',
                'gender' => 'L',
                'password' => Hash::make('parent123'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        if (!$parentUser->hasRole('orang_tua')) {
            $parentUser->assignRole('orang_tua');
        }

        // Create Parent Profile
        $parentProfile = ParentProfile::firstOrCreate(
            ['user_id' => $parentUser->id],
            [
                'occupation' => 'Wiraswasta',
                'education' => 'S1',
                'monthly_income' => 'Rp 5.000.000 - Rp 10.000.000',
                'address' => 'Jl. Merdeka No. 123, Jakarta',
                'emergency_contact' => '08123456701',
            ]
        );

        // Create Student User
        $studentUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Andi Hartono',
                'nisn' => '1234567890',
                'user_type' => 'siswa',
                'phone' => '08123456702',
                'gender' => 'L',
                'birth_date' => '2008-05-15',
                'password' => Hash::make('student123'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        if (!$studentUser->hasRole('siswa')) {
            $studentUser->assignRole('siswa');
        }

        // Create Student Profile
        StudentProfile::firstOrCreate(
            ['user_id' => $studentUser->id],
            [
                'student_id' => 'STD001',
                'class' => 'X IPA 1',
                'admission_year' => 2024,
                'parent_id' => $parentProfile->id,
                'previous_school' => 'SMP Negeri 1 Jakarta',
                'address' => 'Jl. Merdeka No. 123, Jakarta',
                'blood_type' => 'A',
                'health_info' => 'Sehat',
                'emergency_contact' => $parentUser->phone,
            ]
        );

        // Create another Student
        $studentUser2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sari Hartono',
                'nisn' => '**********',
                'user_type' => 'siswa',
                'phone' => '08123456703',
                'gender' => 'P',
                'birth_date' => '2009-08-20',
                'password' => Hash::make('student123'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        if (!$studentUser2->hasRole('siswa')) {
            $studentUser2->assignRole('siswa');
        }

        StudentProfile::firstOrCreate(
            ['user_id' => $studentUser2->id],
            [
                'student_id' => 'STD002',
                'class' => 'IX A',
                'admission_year' => 2023,
                'parent_id' => $parentProfile->id,
                'previous_school' => 'SMP Negeri 1 Jakarta',
                'address' => 'Jl. Merdeka No. 123, Jakarta',
                'blood_type' => 'B',
                'health_info' => 'Sehat',
                'emergency_contact' => $parentUser->phone,
            ]
        );

        // Create Calon Siswa
        $calonSiswaUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Maya Putri',
                'user_type' => 'calon_siswa',
                'phone' => '08123456704',
                'gender' => 'P',
                'birth_date' => '2009-12-10',
                'password' => Hash::make('calon123'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        if (!$calonSiswaUser->hasRole('calon_siswa')) {
            $calonSiswaUser->assignRole('calon_siswa');
        }
    }
}
