<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance Mode - {{ config('app.name', '<PERSON><PERSON>lahku') }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .maintenance-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .maintenance-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .maintenance-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
        }

        .maintenance-icon i {
            font-size: 3rem;
            color: white;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 20px rgba(102, 126, 234, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .maintenance-subtitle {
            font-size: 1.2rem;
            color: #718096;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .maintenance-message {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: left;
        }

        .maintenance-message h5 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .maintenance-message p {
            color: #4a5568;
            margin: 0;
        }

        .contact-info {
            background: #edf2f7;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .contact-info h6 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: #4a5568;
        }

        .contact-item i {
            width: 20px;
            margin-right: 10px;
            color: #667eea;
        }

        .admin-login {
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .admin-login a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .admin-login a:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .progress-bar {
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin: 2rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            animation: progress 3s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 0%; }
        }

        @media (max-width: 768px) {
            .maintenance-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <!-- Maintenance Icon -->
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>

        <!-- Main Content -->
        <h1 class="maintenance-title">Website Sedang Maintenance</h1>
        <p class="maintenance-subtitle">
            Kami sedang melakukan pemeliharaan sistem untuk memberikan pengalaman yang lebih baik. 
            Mohon maaf atas ketidaknyamanan ini.
        </p>

        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>

        <!-- Maintenance Message -->
        <div class="maintenance-message">
            <h5><i class="fas fa-info-circle me-2"></i>Informasi Maintenance</h5>
            <p>
                @php
                    $schoolSetting = \App\Models\SchoolSetting::first();
                    $customMessage = $schoolSetting && $schoolSetting->maintenance_message
                        ? $schoolSetting->maintenance_message
                        : 'Tim teknis kami sedang bekerja untuk meningkatkan performa dan keamanan sistem. Proses maintenance ini diperkirakan akan selesai dalam beberapa saat.';
                @endphp
                {{ $customMessage }}
            </p>
        </div>

        <!-- Contact Information -->
        <div class="contact-info">
            <h6><i class="fas fa-phone me-2"></i>Butuh Bantuan?</h6>
            <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
            </div>
            <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>(021) 1234-5678</span>
            </div>
            <div class="contact-item">
                <i class="fab fa-whatsapp"></i>
                <span>+62 812-3456-7890</span>
            </div>
        </div>

        <!-- Super Admin Emergency Access -->
       <!--<div class="mt-4 pt-3 border-top">
           <small class="text-muted d-block mb-2">Emergency Access</small>
            <a href="{{ route('super-admin.login') }}" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-shield-alt me-1"></i>
                Super Admin Login
            </a>
        </div>-->

        <!-- Footer -->
        <div class="mt-4">
            <small class="text-muted">
                © {{ date('Y') }} {{ config('app.name', 'Sekolahku') }}. Semua hak dilindungi.
            </small>
        </div>
    </div>

    <!-- Auto Refresh Script -->
    <script>
        // Auto refresh every 30 seconds to check if maintenance is over
        setTimeout(function() {
            window.location.reload();
        }, 30000);

        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add click effect to maintenance icon
            const icon = document.querySelector('.maintenance-icon');
            icon.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
