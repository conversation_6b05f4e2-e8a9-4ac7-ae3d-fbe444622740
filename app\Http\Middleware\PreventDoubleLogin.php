<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\SecurityLog;

class PreventDoubleLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();
            $currentSessionId = session()->getId();

            // Check for other active sessions for this user
            $activeSessions = DB::table('sessions')
                ->where('user_id', $user->id)
                ->where('id', '!=', $currentSessionId)
                ->where('last_activity', '>', now()->subMinutes(config('session.lifetime', 120))->timestamp)
                ->count();

            if ($activeSessions > 0) {
                // Log security event
                SecurityLog::logEvent('multiple_sessions_detected', $user->id, [
                    'reason' => 'Multiple active sessions detected after login',
                    'current_session' => $currentSessionId,
                    'active_sessions_count' => $activeSessions,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);

                // Automatically terminate other sessions for security
                DB::table('sessions')
                    ->where('user_id', $user->id)
                    ->where('id', '!=', $currentSessionId)
                    ->delete();

                // Show informational message
                session()->flash('info', 'Untuk keamanan, sesi login lain telah diakhiri secara otomatis.');
            }
        }

        return $next($request);
    }
}
