@extends('layouts.landing')

@section('title', 'Kontak')
@section('description', 'Hubungi kami untuk informasi lebih lanjut tentang sekolah')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 120px 0 80px;
        text-align: center;
    }

    .page-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .contact-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }

    .contact-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .contact-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 2rem;
    }

    .contact-form {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-control {
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        padding: 12px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .form-control.is-invalid {
        border-color: var(--danger-color);
    }

    .map-container {
        height: 400px;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .map-container iframe {
        width: 100%;
        height: 100%;
        border: none;
    }

    .social-links {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
    }

    .social-link {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .social-link:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .info-item:hover {
        background: #e9ecef;
    }

    .info-icon {
        width: 40px;
        height: 40px;
        background: var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .contact-form {
            padding: 2rem;
        }
        
        .contact-card {
            padding: 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1 class="page-title" data-aos="fade-up">Hubungi Kami</h1>
        <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            Kami siap membantu Anda dengan informasi yang dibutuhkan
        </p>
    </div>
</section>

<!-- Contact Content -->
<section class="section">
    <div class="container">
        <div class="row g-4 mb-5">
            <!-- Contact Info Cards -->
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                <div class="contact-card text-center">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4>Alamat</h4>
                    <p class="text-muted">
                        @if($schoolSettings && $schoolSettings->school_address)
                            {{ $schoolSettings->school_address }}
                        @else
                            Jl. Pendidikan No. 123<br>Jakarta Selatan, DKI Jakarta 12345
                        @endif
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                <div class="contact-card text-center">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4>Telepon</h4>
                    <p class="text-muted">
                        @if($schoolSettings && $schoolSettings->school_phone)
                            {{ $schoolSettings->school_phone }}
                        @else
                            (021) 1234-5678
                        @endif
                    </p>
                    @if($schoolSettings && $schoolSettings->contact_info && isset(json_decode($schoolSettings->contact_info, true)['whatsapp']))
                        <p class="text-success">
                            <i class="fab fa-whatsapp me-1"></i>
                            {{ json_decode($schoolSettings->contact_info, true)['whatsapp'] }}
                        </p>
                    @endif
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                <div class="contact-card text-center">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4>Email</h4>
                    <p class="text-muted">
                        @if($schoolSettings && $schoolSettings->school_email)
                            {{ $schoolSettings->school_email }}
                        @else
                            <EMAIL>
                        @endif
                    </p>
                    @if($schoolSettings && $schoolSettings->school_website)
                        <p class="text-primary">
                            <i class="fas fa-globe me-1"></i>
                            <a href="{{ $schoolSettings->school_website }}" target="_blank" class="text-decoration-none">
                                {{ $schoolSettings->school_website }}
                            </a>
                        </p>
                    @endif
                </div>
            </div>
        </div>

        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-8" data-aos="fade-right">
                <div class="contact-form">
                    <h3 class="mb-4">Kirim Pesan</h3>
                    
                    <form id="contactForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Nama Lengkap *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">Nomor Telepon</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="subject" class="form-label">Subjek *</label>
                                    <select class="form-control" id="subject" name="subject" required>
                                        <option value="">Pilih Subjek</option>
                                        <option value="informasi_umum">Informasi Umum</option>
                                        <option value="pendaftaran">Pendaftaran Siswa Baru</option>
                                        <option value="program">Program Pendidikan</option>
                                        <option value="fasilitas">Fasilitas Sekolah</option>
                                        <option value="lainnya">Lainnya</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="message" class="form-label">Pesan *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required placeholder="Tulis pesan Anda di sini..."></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>Kirim Pesan
                        </button>
                    </form>
                </div>
            </div>

            <!-- Additional Info -->
            <div class="col-lg-4" data-aos="fade-left">
                <div class="contact-card">
                    <h4 class="mb-4">Informasi Tambahan</h4>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <strong>Jam Operasional</strong><br>
                            <small class="text-muted">
                                Senin - Jumat: 07:00 - 16:00<br>
                                Sabtu: 07:00 - 12:00
                            </small>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div>
                            <strong>Tahun Ajaran</strong><br>
                            <small class="text-muted">2024/2025</small>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <strong>Kapasitas Siswa</strong><br>
                            <small class="text-muted">{{ $schoolSettings->total_students ?? '1200' }} siswa aktif</small>
                        </div>
                    </div>

                    @if($schoolSettings && $schoolSettings->social_media)
                        <div class="social-links">
                            @foreach(json_decode($schoolSettings->social_media, true) as $platform => $url)
                                <a href="{{ $url }}" class="social-link" target="_blank" title="{{ ucfirst($platform) }}">
                                    <i class="fab fa-{{ $platform }}"></i>
                                </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Map -->
        <div class="row mt-5">
            <div class="col-12" data-aos="fade-up">
                <h3 class="text-center mb-4">Lokasi Sekolah</h3>
                <div class="map-container">
                    <!-- Google Maps Embed - Replace with actual coordinates -->
                    <iframe 
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.521260322283!2d106.8195613507864!3d-6.194741395493371!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69f5390917b759%3A0x6b45e67356080477!2sJakarta%2C%20Daerah%20Khusus%20Ibukota%20Jakarta!5e0!3m2!1sen!2sid!4v1635123456789!5m2!1sen!2sid"
                        allowfullscreen="" 
                        loading="lazy" 
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mengirim...';
        submitBtn.disabled = true;
        
        // Simulate form submission (replace with actual AJAX call)
        setTimeout(() => {
            Swal.fire({
                icon: 'success',
                title: 'Pesan Terkirim!',
                text: 'Terima kasih atas pesan Anda. Kami akan segera menghubungi Anda kembali.',
                confirmButtonColor: '#667eea'
            });
            
            // Reset form
            this.reset();
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
</script>
@endpush
