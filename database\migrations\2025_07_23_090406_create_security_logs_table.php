<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('ip_address', 45);
            $table->string('user_agent')->nullable();
            $table->enum('event_type', ['login_success', 'login_failed', 'logout', 'password_reset', 'account_locked', 'suspicious_activity', 'rate_limit_exceeded', 'suspicious_file_upload', 'data_access', 'data_modification'])->default('login_success');
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Store additional data as JSON
            $table->enum('risk_level', ['low', 'medium', 'high', 'critical'])->default('low');
            $table->boolean('is_resolved')->default(false);
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'event_type']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['risk_level', 'is_resolved']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_logs');
    }
};
