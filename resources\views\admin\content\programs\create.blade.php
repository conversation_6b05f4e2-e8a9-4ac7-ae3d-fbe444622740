@extends('layouts.dashboard')

@section('title', 'Tambah Program')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Tambah Program Pendidikan</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.content.programs.index') }}">Program Pendidikan</a></li>
                <li class="breadcrumb-item active">Tambah Program</li>
            </ol>
        </nav>
    </div>
    <a href="{{ route('admin.content.programs.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Kembali
    </a>
</div>

<form action="{{ route('admin.content.programs.store') }}" method="POST" enctype="multipart/form-data">
    @csrf
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi Program</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Nama Program <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name') }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="level" class="form-label">Level <span class="text-danger">*</span></label>
                            <select class="form-select @error('level') is-invalid @enderror" 
                                    id="level" name="level" required>
                                <option value="">Pilih Level</option>
                                <option value="TK" {{ old('level') == 'TK' ? 'selected' : '' }}>TK</option>
                                <option value="SD" {{ old('level') == 'SD' ? 'selected' : '' }}>SD</option>
                                <option value="SMP" {{ old('level') == 'SMP' ? 'selected' : '' }}>SMP</option>
                                <option value="SMA" {{ old('level') == 'SMA' ? 'selected' : '' }}>SMA</option>
                                <option value="SMK" {{ old('level') == 'SMK' ? 'selected' : '' }}>SMK</option>
                            </select>
                            @error('level')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">Tipe Program <span class="text-danger">*</span></label>
                            <select class="form-select @error('type') is-invalid @enderror" 
                                    id="type" name="type" required>
                                <option value="">Pilih Tipe</option>
                                <option value="regular" {{ old('type') == 'regular' ? 'selected' : '' }}>Regular</option>
                                <option value="unggulan" {{ old('type') == 'unggulan' ? 'selected' : '' }}>Unggulan</option>
                                <option value="internasional" {{ old('type') == 'internasional' ? 'selected' : '' }}>Internasional</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Deskripsi Program <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="5" required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="curriculum" class="form-label">Kurikulum</label>
                        <textarea class="form-control @error('curriculum') is-invalid @enderror" 
                                  id="curriculum" name="curriculum" rows="3">{{ old('curriculum') }}</textarea>
                        @error('curriculum')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="requirements" class="form-label">Persyaratan</label>
                        <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                  id="requirements" name="requirements" rows="3">{{ old('requirements') }}</textarea>
                        @error('requirements')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="duration_years" class="form-label">Durasi (Tahun) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('duration_years') is-invalid @enderror" 
                                   id="duration_years" name="duration_years" value="{{ old('duration_years') }}" 
                                   min="1" max="6" required>
                            @error('duration_years')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="capacity" class="form-label">Kapasitas</label>
                            <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                   id="capacity" name="capacity" value="{{ old('capacity') }}" min="1">
                            @error('capacity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="current_students" class="form-label">Siswa Saat Ini</label>
                            <input type="number" class="form-control @error('current_students') is-invalid @enderror" 
                                   id="current_students" name="current_students" value="{{ old('current_students') }}" min="0">
                            @error('current_students')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="fee" class="form-label">Biaya Program</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control @error('fee') is-invalid @enderror" 
                                   id="fee" name="fee" value="{{ old('fee') }}" min="0">
                        </div>
                        @error('fee')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Program Image -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Gambar Program</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB.</small>
                    </div>
                    
                    <div id="image-preview" style="display: none;">
                        <img id="preview-img" src="" alt="Preview" class="img-fluid rounded">
                    </div>
                </div>
            </div>

            <!-- Program Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pengaturan</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Program Aktif
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="order" class="form-label">Urutan Tampil</label>
                        <input type="number" class="form-control @error('order') is-invalid @enderror" 
                               id="order" name="order" value="{{ old('order', 0) }}" min="0">
                        @error('order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Semakin kecil angka, semakin atas urutannya</small>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Simpan Program
                        </button>
                        <a href="{{ route('admin.content.programs.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Batal
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Image preview
    $('#image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').hide();
        }
    });

    // Auto-generate slug from name (if needed)
    $('#name').on('input', function() {
        // You can add slug generation logic here if needed
    });
});
</script>
@endpush
