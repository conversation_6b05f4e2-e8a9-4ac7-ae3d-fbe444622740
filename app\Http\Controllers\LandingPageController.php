<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SchoolSetting;
use App\Models\HeroSection;
use App\Models\News;
use App\Models\Program;
use App\Models\Facility;
use App\Models\Gallery;
use App\Models\StaffProfile;
use App\Models\PPDBSetting;

class LandingPageController extends Controller
{
    public function index()
    {
        // Get hero sections
        $heroSections = HeroSection::where('is_active', true)
            ->orderBy('order')
            ->get();

        // Get latest news
        $latestNews = News::where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->take(6)
            ->get();

        // Get featured news
        $featuredNews = News::where('status', 'published')
            ->where('is_featured', true)
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // Get programs
        $programs = Program::where('is_active', true)
            ->orderBy('order')
            ->get();

        // Get facilities (prioritize featured ones)
        $facilities = Facility::where('is_available', true)
            ->orderBy('is_featured', 'desc')
            ->orderBy('order')
            ->orderBy('created_at', 'desc')
            ->take(6)
            ->get();

        // Get gallery images
        $galleryImages = Gallery::where('file_type', 'image')
            ->where('is_featured', true)
            ->orderBy('order')
            ->take(12)
            ->get();

        // Get featured staff
        $featuredStaff = StaffProfile::with('user')
            ->where('is_featured', true)
            ->orderBy('order')
            ->take(8)
            ->get();

        // Get PPDB settings
        $ppdbSetting = PPDBSetting::getActive();

        return view('landing.index', compact(
            'heroSections',
            'latestNews',
            'featuredNews',
            'programs',
            'facilities',
            'galleryImages',
            'featuredStaff',
            'ppdbSetting'
        ));
    }

    public function showNews($slug)
    {
        $news = News::where('slug', $slug)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        // Increment views
        $news->increment('views');

        // Get previous news
        $previousNews = News::where('status', 'published')
            ->where('published_at', '<', $news->published_at)
            ->orderBy('published_at', 'desc')
            ->first();

        // Get next news
        $nextNews = News::where('status', 'published')
            ->where('published_at', '>', $news->published_at)
            ->orderBy('published_at', 'asc')
            ->first();

        // Get related news
        $relatedNews = News::where('status', 'published')
            ->where('id', '!=', $news->id)
            ->where('type', $news->type)
            ->orderBy('published_at', 'desc')
            ->take(4)
            ->get();

        return view('landing.news-detail', compact('news', 'relatedNews', 'previousNews', 'nextNews'));
    }

    public function showProgram($slug)
    {
        $program = Program::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        return view('landing.program-detail', compact('program'));
    }

    public function showFacility($slug)
    {
        $facility = Facility::where('slug', $slug)
            ->where('is_available', true)
            ->firstOrFail();

        return view('landing.facility-detail', compact('facility'));
    }

    public function gallery()
    {
        $images = Gallery::where('file_type', 'image')
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        $albums = Gallery::select('album')
            ->whereNotNull('album')
            ->distinct()
            ->pluck('album');

        return view('landing.gallery', compact('images', 'albums'));
    }

    public function news()
    {
        $news = News::where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('is_featured', 'desc')
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        return view('landing.news', compact('news'));
    }

    public function programs()
    {
        $programs = Program::where('is_active', true)
            ->orderBy('order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('landing.programs', compact('programs'));
    }

    public function facilities()
    {
        $facilities = Facility::where('is_available', true)
            ->orderBy('order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('landing.facilities', compact('facilities'));
    }

    public function contact()
    {
        return view('landing.contact');
    }
}
