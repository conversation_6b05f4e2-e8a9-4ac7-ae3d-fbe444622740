@extends('layouts.dashboard')

@section('title', 'Tambah Hero Section')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Tambah Hero Section</h1>
        <p class="text-muted">Buat banner utama baru untuk halaman depan website</p>
    </div>
    <div>
        <a href="{{ route('admin.settings.hero-sections') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Kembali
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>Form Tambah Hero Section
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.settings.hero-sections.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Judul <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" 
                                   value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="order" class="form-label">Urutan</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                   id="order" name="order" 
                                   value="{{ old('order', 0) }}" min="0">
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Urutan tampil (0 = paling atas)</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subjudul</label>
                        <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" name="subtitle" 
                               value="{{ old('subtitle') }}" 
                               placeholder="Subjudul opsional">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Deskripsi</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" 
                                  placeholder="Deskripsi hero section...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="image" class="form-label">Gambar Background <span class="text-danger">*</span></label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*" required>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Format: JPG, PNG, GIF. Maksimal 2MB. Resolusi yang disarankan: 1920x1080px</small>
                    </div>

                    <div class="mb-3">
                        <label for="video_url" class="form-label">URL Video</label>
                        <input type="url" class="form-control @error('video_url') is-invalid @enderror" 
                               id="video_url" name="video_url" 
                               value="{{ old('video_url') }}" 
                               placeholder="https://youtube.com/watch?v=...">
                        @error('video_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">URL video YouTube atau platform lainnya (opsional)</small>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">Tombol Call-to-Action</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="button_text" class="form-label">Teks Tombol</label>
                            <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                   id="button_text" name="button_text" 
                                   value="{{ old('button_text') }}" 
                                   placeholder="Pelajari Lebih Lanjut">
                            @error('button_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="button_link" class="form-label">Link Tombol</label>
                            <input type="text" class="form-control @error('button_link') is-invalid @enderror" 
                                   id="button_link" name="button_link" 
                                   value="{{ old('button_link') }}" 
                                   placeholder="/tentang-kami atau https://...">
                            @error('button_link')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Aktifkan hero section ini
                            </label>
                        </div>
                        <small class="form-text text-muted">Hero section yang tidak aktif tidak akan ditampilkan di website</small>
                    </div>

                    <div class="d-flex justify-content-end">
                        <a href="{{ route('admin.settings.hero-sections') }}" class="btn btn-secondary me-2">
                            <i class="fas fa-times me-2"></i>Batal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Simpan Hero Section
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Preview -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Preview Gambar</h5>
            </div>
            <div class="card-body text-center">
                <div id="imagePreview" class="mb-3" style="display: none;">
                    <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                </div>
                <div id="noImagePreview" class="bg-light p-4 rounded">
                    <i class="fas fa-image fa-3x text-muted"></i>
                    <p class="text-muted mt-2 mb-0">Pilih gambar untuk melihat preview</p>
                </div>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Tips</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <small>Gunakan gambar dengan resolusi tinggi untuk hasil terbaik</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-palette text-info me-2"></i>
                        <small>Pastikan teks tetap terbaca dengan baik di atas gambar</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-mobile-alt text-success me-2"></i>
                        <small>Hero section akan otomatis responsif di semua perangkat</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-sort text-primary me-2"></i>
                        <small>Atur urutan untuk menentukan prioritas tampilan</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Preview image on file select
    $('#image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImagePreview').hide();
            };
            reader.readAsDataURL(file);
        } else {
            $('#imagePreview').hide();
            $('#noImagePreview').show();
        }
    });
    
    // Auto-fill button link based on button text
    $('#button_text').on('input', function() {
        const buttonText = $(this).val().toLowerCase();
        const buttonLink = $('#button_link');
        
        if (buttonLink.val() === '') {
            if (buttonText.includes('tentang') || buttonText.includes('about')) {
                buttonLink.val('/tentang-kami');
            } else if (buttonText.includes('kontak') || buttonText.includes('contact')) {
                buttonLink.val('/kontak');
            } else if (buttonText.includes('program') || buttonText.includes('pendidikan')) {
                buttonLink.val('/program-pendidikan');
            } else if (buttonText.includes('fasilitas') || buttonText.includes('facility')) {
                buttonLink.val('/fasilitas');
            } else if (buttonText.includes('ppdb') || buttonText.includes('daftar')) {
                buttonLink.val('/ppdb');
            }
        }
    });
});
</script>
@endpush
