<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\CustomLoginController;
use App\Http\Controllers\LandingPageController;

// Maintenance Route
Route::get('/maintenance', function () {
    return view('maintenance');
})->name('maintenance');

// Super Admin Emergency Routes (only accessible during maintenance)
Route::prefix('super-admin')->name('super-admin.')->group(function () {
    Route::get('/login', [App\Http\Controllers\SuperAdminController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [App\Http\Controllers\SuperAdminController::class, 'login']);
    Route::get('/dashboard', [App\Http\Controllers\SuperAdminController::class, 'dashboard'])->name('dashboard');
    Route::post('/toggle-maintenance', [App\Http\Controllers\SuperAdminController::class, 'toggleMaintenance'])->name('toggle-maintenance');
    Route::get('/logout', [App\Http\Controllers\SuperAdminController::class, 'logout'])->name('logout');
    Route::get('/emergency-access', [App\Http\Controllers\SuperAdminController::class, 'emergencyAccess'])->name('emergency-access');
});

// Landing Page Routes
Route::get('/', [LandingPageController::class, 'index'])->name('landing');
Route::get('/news', [LandingPageController::class, 'news'])->name('landing.news.index');
Route::get('/news/{slug}', [LandingPageController::class, 'showNews'])->name('landing.news');
Route::get('/programs', [LandingPageController::class, 'programs'])->name('landing.programs');
Route::get('/program/{slug}', [LandingPageController::class, 'showProgram'])->name('landing.program');
Route::get('/facility/{slug}', [LandingPageController::class, 'showFacility'])->name('landing.facility');
Route::get('/facilities', [LandingPageController::class, 'facilities'])->name('landing.facilities');
Route::get('/gallery', [LandingPageController::class, 'gallery'])->name('landing.gallery');
Route::get('/contact', [LandingPageController::class, 'contact'])->name('landing.contact');

// Custom Authentication Routes with Rate Limiting
Route::get('login', [CustomLoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [CustomLoginController::class, 'login'])->middleware('rate.limit:login');
Route::post('login/force', [CustomLoginController::class, 'forceLogin'])->name('login.force')->middleware('rate.limit:login');
Route::post('logout', [CustomLoginController::class, 'logout'])->name('logout');

// Email Verification Routes with Rate Limiting
Route::get('/email/verify/{id}/{token}', [App\Http\Controllers\EmailVerificationController::class, 'verify'])->name('email.verify');
Route::post('/email/verification/resend', [App\Http\Controllers\EmailVerificationController::class, 'resend'])->name('email.verification.resend')->middleware('rate.limit:password-reset');
Route::get('/email/verify', [App\Http\Controllers\EmailVerificationController::class, 'notice'])->name('verification.notice');

// Secure Password Reset Routes with Rate Limiting
Route::get('/password/reset', [App\Http\Controllers\Auth\SecurePasswordResetController::class, 'showLinkRequestForm'])->name('password.request');
Route::post('/password/email', [App\Http\Controllers\Auth\SecurePasswordResetController::class, 'sendResetLinkEmail'])->name('password.email')->middleware('rate.limit:password-reset');
Route::get('/password/reset/{token}', [App\Http\Controllers\Auth\SecurePasswordResetController::class, 'showResetForm'])->name('password.reset');
Route::post('/password/reset', [App\Http\Controllers\Auth\SecurePasswordResetController::class, 'reset'])->name('password.update')->middleware('rate.limit:password-reset');

// Keep other auth routes (no login, logout, register, password reset)
Auth::routes(['login' => false, 'logout' => false, 'register' => false, 'reset' => false]);

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// PPDB Routes (Public) with Rate Limiting
Route::prefix('ppdb-online')->name('ppdb.')->group(function () {
    Route::get('/', [App\Http\Controllers\PPDBController::class, 'index'])->name('index');
    Route::post('/daftar', [App\Http\Controllers\PPDBController::class, 'register'])->name('register')->middleware('rate.limit:register');
    Route::get('/sukses/{registrationNumber}', [App\Http\Controllers\PPDBController::class, 'success'])->name('success');
    Route::get('/status/{registrationNumber}', [App\Http\Controllers\PPDBController::class, 'status'])->name('status');
    Route::get('/cek-status', function() {
        return view('ppdb.check-status');
    })->name('check-status');
    Route::post('/cek-status', [App\Http\Controllers\PPDBController::class, 'checkStatus'])->name('check-status.post')->middleware('rate.limit:general');
});

// Parent Registration Routes (Public) with Rate Limiting
Route::prefix('parent-registration')->name('parent-registration.')->group(function () {
    Route::get('/', [App\Http\Controllers\ParentRegistrationController::class, 'index'])->name('index');
    Route::post('/register', [App\Http\Controllers\ParentRegistrationController::class, 'register'])->name('register')->middleware('rate.limit:register');
    Route::get('/success/{registrationNumber}', [App\Http\Controllers\ParentRegistrationController::class, 'success'])->name('success');
    Route::post('/check-code', [App\Http\Controllers\ParentRegistrationController::class, 'checkStudentCode'])->name('check-code')->middleware('rate.limit:general');
});

// Dashboard routes (protected by auth middleware)
Route::middleware(['auth', 'prevent.double.login'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Profile Management Routes
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update')->middleware('secure.upload');
    Route::put('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password.update');
    Route::delete('/profile/avatar', [App\Http\Controllers\ProfileController::class, 'deleteAvatar'])->name('profile.avatar.delete');

    // Super Admin Routes
    Route::middleware(['user.type:super_admin,admin'])->prefix('admin')->name('admin.')->group(function () {
        // Admin Dashboard
        Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'adminDashboard'])->name('dashboard');

        // User Management
        Route::resource('users', App\Http\Controllers\Admin\UserManagementController::class)->middleware('secure.upload');
        Route::post('users/{user}/toggle-status', [App\Http\Controllers\Admin\UserManagementController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::post('users/{user}/reset-password', [App\Http\Controllers\Admin\UserManagementController::class, 'resetPassword'])->name('users.reset-password');
        Route::post('users/{user}/lock-account', [App\Http\Controllers\Admin\UserManagementController::class, 'lockAccount'])->name('users.lock-account');
        Route::post('users/{user}/unlock-account', [App\Http\Controllers\Admin\UserManagementController::class, 'unlockAccount'])->name('users.unlock-account');

        // Test route for debugging
        Route::get('test-toggle/{user}', function(\App\Models\User $user) {
            return response()->json([
                'user_id' => $user->id,
                'current_status' => $user->is_active,
                'can_toggle' => true
            ]);
        })->name('test.toggle');

        // Test POST toggle
        Route::post('test-toggle-post/{user}', function(\App\Models\User $user) {
            $oldStatus = $user->is_active;
            $user->is_active = !$user->is_active;
            $user->save();

            return response()->json([
                'success' => true,
                'user_id' => $user->id,
                'old_status' => $oldStatus,
                'new_status' => $user->is_active,
                'message' => 'Status berhasil diubah'
            ]);
        })->name('test.toggle.post');

        // Test DELETE user
        Route::delete('test-delete/{user}', function(\App\Models\User $user) {
            return response()->json([
                'success' => true,
                'user_id' => $user->id,
                'user_name' => $user->name,
                'message' => 'Test delete berhasil (user tidak benar-benar dihapus)'
            ]);
        })->name('test.delete');

        // Test CSRF
        Route::get('test-csrf', function() {
            return response()->json([
                'csrf_token' => csrf_token(),
                'session_token' => session()->token()
            ]);
        })->name('test.csrf');

        // Content Management - News (NEW CLEAN VERSION)
        Route::prefix('content')->name('content.')->group(function () {
            Route::get('news', [App\Http\Controllers\Admin\NewsManagementController::class, 'index'])->name('news.index');
            Route::get('news/create', [App\Http\Controllers\Admin\NewsManagementController::class, 'create'])->name('news.create');
            Route::post('news', [App\Http\Controllers\Admin\NewsManagementController::class, 'store'])->name('news.store')->middleware('secure.upload');
            Route::get('news/{news}', [App\Http\Controllers\Admin\NewsManagementController::class, 'show'])->name('news.show');
            Route::get('news/{news}/edit', [App\Http\Controllers\Admin\NewsManagementController::class, 'edit'])->name('news.edit');
            Route::put('news/{news}', [App\Http\Controllers\Admin\NewsManagementController::class, 'update'])->name('news.update')->middleware('secure.upload');
            Route::delete('news/{news}', [App\Http\Controllers\Admin\NewsManagementController::class, 'destroy'])->name('news.destroy');
            Route::delete('news/{news}/remove-image', [App\Http\Controllers\Admin\NewsManagementController::class, 'removeImage'])->name('news.remove-image');


            Route::post('news/check-slug', [App\Http\Controllers\Admin\ContentManagementController::class, 'checkSlug'])->name('news.check-slug');

            // Debug route for testing
            Route::post('news/test-submit', function(\Illuminate\Http\Request $request) {
                \Log::info('=== TEST SUBMIT ROUTE ===', [
                    'method' => $request->method(),
                    'url' => $request->fullUrl(),
                    'headers' => $request->headers->all(),
                    'data' => $request->except(['_token', 'featured_image']),
                    'has_csrf' => $request->hasHeader('X-CSRF-TOKEN') || $request->has('_token'),
                    'csrf_token' => $request->header('X-CSRF-TOKEN') ?: $request->input('_token'),
                    'session_id' => session()->getId(),
                    'user_id' => auth()->id(),
                    'user_name' => auth()->user()->name ?? 'guest'
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Test submit berhasil',
                    'redirect' => route('admin.content.news.index')
                ]);
            })->name('news.test-submit');

            // Test form route
            Route::get('news/test-form', function() {
                return view('admin.content.news.test-form');
            })->name('news.test-form');

            // Simple test route without middleware
            Route::post('news/simple-test', function(\Illuminate\Http\Request $request) {
                return response()->json([
                    'success' => true,
                    'message' => 'Simple test berhasil',
                    'data' => $request->all(),
                    'timestamp' => now()->toDateTimeString()
                ]);
            })->name('news.simple-test')->withoutMiddleware(['auth', 'verified']);

            // Simple test form route
            Route::get('news/simple-test-form', function() {
                return view('admin.content.news.simple-test');
            })->name('news.simple-test-form')->withoutMiddleware(['auth', 'verified']);

            // Ultra simple test route
            Route::post('news/ultra-simple', function(\Illuminate\Http\Request $request) {
                \Log::info('=== ULTRA SIMPLE TEST ===', [
                    'data' => $request->all(),
                    'timestamp' => now()->toDateTimeString()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Ultra simple test berhasil',
                    'data' => $request->all(),
                    'timestamp' => now()->toDateTimeString()
                ]);
            })->name('news.ultra-simple')->withoutMiddleware(['auth', 'verified']);

            // Isolated test route
            Route::get('news/isolated-test', function() {
                return view('test-isolated');
            })->name('news.isolated-test');



            // Debug route
            Route::get('news/{id}/debug', function($id) {
                $news = \App\Models\News::find($id);
                return response()->json([
                    'id' => $id,
                    'news_found' => $news ? true : false,
                    'news_data' => $news,
                    'route_key_name' => (new \App\Models\News())->getRouteKeyName()
                ]);
            })->where('id', '[0-9]+');



            // Programs Management
            Route::get('programs', [App\Http\Controllers\Admin\ContentManagementController::class, 'programIndex'])->name('programs.index');
            Route::get('programs/create', [App\Http\Controllers\Admin\ContentManagementController::class, 'programCreate'])->name('programs.create');
            Route::post('programs', [App\Http\Controllers\Admin\ContentManagementController::class, 'programStore'])->name('programs.store')->middleware('secure.upload');
            Route::get('programs/{program}', [App\Http\Controllers\Admin\ContentManagementController::class, 'programShow'])->name('programs.show');
            Route::get('programs/{program}/edit', [App\Http\Controllers\Admin\ContentManagementController::class, 'programEdit'])->name('programs.edit');
            Route::put('programs/{program}', [App\Http\Controllers\Admin\ContentManagementController::class, 'programUpdate'])->name('programs.update')->middleware('secure.upload');
            Route::delete('programs/{program}', [App\Http\Controllers\Admin\ContentManagementController::class, 'programDestroy'])->name('programs.destroy');
            Route::delete('programs/{id}/remove-image', [App\Http\Controllers\Admin\ContentManagementController::class, 'programRemoveImage'])->name('programs.remove-image');

            // Facilities Management
            Route::get('facilities', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityIndex'])->name('facilities.index');
            Route::get('facilities/create', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityCreate'])->name('facilities.create');
            Route::post('facilities', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityStore'])->name('facilities.store');
            Route::get('facilities/{facility}', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityShow'])->name('facilities.show');
            Route::get('facilities/{facility}/edit', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityEdit'])->name('facilities.edit');
            Route::put('facilities/{facility}', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityUpdate'])->name('facilities.update');
            Route::delete('facilities/{facility}', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityDestroy'])->name('facilities.destroy');
            Route::delete('facilities/{facility}/remove-image', [App\Http\Controllers\Admin\ContentManagementController::class, 'facilityRemoveImage'])->name('facilities.remove-image');

            // Gallery Management
            Route::get('gallery', [App\Http\Controllers\Admin\ContentManagementController::class, 'galleryIndex'])->name('gallery.index');
            Route::get('gallery/create', [App\Http\Controllers\Admin\ContentManagementController::class, 'galleryCreate'])->name('gallery.create');
            Route::post('gallery', [App\Http\Controllers\Admin\ContentManagementController::class, 'galleryStore'])->name('gallery.store')->middleware('secure.upload');
            Route::get('gallery/{gallery}', [App\Http\Controllers\Admin\ContentManagementController::class, 'galleryShow'])->name('gallery.show');
            Route::get('gallery/{gallery}/edit', [App\Http\Controllers\Admin\ContentManagementController::class, 'galleryEdit'])->name('gallery.edit');
            Route::put('gallery/{gallery}', [App\Http\Controllers\Admin\ContentManagementController::class, 'galleryUpdate'])->name('gallery.update')->middleware('secure.upload');
            Route::delete('gallery/{gallery}', [App\Http\Controllers\Admin\ContentManagementController::class, 'galleryDestroy'])->name('gallery.destroy');
        });

        // School Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'index'])->name('index');
            Route::post('/school-info', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'updateSchoolInfo'])->name('school-info.update')->middleware('secure.upload');

            // Hero Sections
            Route::get('/hero-sections', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'heroSections'])->name('hero-sections');
            Route::get('/hero-sections/create', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'createHeroSection'])->name('hero-sections.create');
            Route::post('/hero-sections', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'storeHeroSection'])->name('hero-sections.store')->middleware('secure.upload');
            Route::get('/hero-sections/{heroSection}/edit', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'editHeroSection'])->name('hero-sections.edit');
            Route::put('/hero-sections/{heroSection}', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'updateHeroSection'])->name('hero-sections.update')->middleware('secure.upload');
            Route::delete('/hero-sections/{heroSection}', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'destroyHeroSection'])->name('hero-sections.destroy');
            Route::post('/hero-sections/order', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'updateHeroOrder'])->name('hero-sections.order');
            Route::post('/hero-sections/{heroSection}/toggle', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'toggleHeroStatus'])->name('hero-sections.toggle');
            Route::delete('/hero-sections/{heroSection}/remove-image', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'removeHeroImage'])->name('hero-sections.remove-image');

            // System Settings
            Route::get('/system', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'systemSettings'])->name('system');
            Route::post('/system', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'updateSystemSettings'])->name('system.update');
            Route::post('/toggle-maintenance', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'toggleMaintenance'])->name('toggle-maintenance');
            Route::post('/clear-cache', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'clearCache'])->name('clear-cache');
            Route::post('/optimize', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'optimizeApplication'])->name('optimize');
            Route::post('/create-backup', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'createBackup'])->name('create-backup');
            Route::post('/run-maintenance', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'runMaintenance'])->name('run-maintenance');
            Route::get('/backup-list', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'getBackupList'])->name('backup-list');
            Route::get('/download-backup', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'downloadBackup'])->name('download-backup');
        });

        // Security & Logs
        Route::prefix('security')->name('security.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\SecurityController::class, 'index'])->name('index');
            Route::get('/dashboard', [App\Http\Controllers\Admin\SecurityController::class, 'dashboard'])->name('dashboard');
            Route::get('/logs/{securityLog}', [App\Http\Controllers\Admin\SecurityController::class, 'show'])->name('show');
            Route::post('/logs/{securityLog}/resolve', [App\Http\Controllers\Admin\SecurityController::class, 'resolve'])->name('resolve');
            Route::delete('/logs/{securityLog}', [App\Http\Controllers\Admin\SecurityController::class, 'destroy'])->name('destroy');
            Route::post('/logs/bulk-resolve', [App\Http\Controllers\Admin\SecurityController::class, 'bulkResolve'])->name('bulk-resolve');
            Route::delete('/logs/delete-all', [App\Http\Controllers\Admin\SecurityController::class, 'deleteAll'])->name('delete-all');
            Route::get('/user-activity', [App\Http\Controllers\Admin\SecurityController::class, 'userActivity'])->name('user-activity');
            Route::get('/user-activity/{user}', [App\Http\Controllers\Admin\SecurityController::class, 'userActivityDetail'])->name('user-activity.detail');
            Route::get('/export', [App\Http\Controllers\Admin\SecurityController::class, 'export'])->name('export');
        });
    });

    // Admin PPDB Management Routes
    Route::prefix('admin/ppdb')->name('admin.ppdb.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Admin\PPDBManagementController::class, 'dashboard'])->name('dashboard');
        Route::get('/settings', [App\Http\Controllers\Admin\PPDBManagementController::class, 'settings'])->name('settings');
        Route::post('/settings', [App\Http\Controllers\Admin\PPDBManagementController::class, 'updateSettings'])->name('settings.update');
        Route::get('/registrations', [App\Http\Controllers\Admin\PPDBManagementController::class, 'registrations'])->name('registrations');
        Route::get('/registrations/{id}', [App\Http\Controllers\Admin\PPDBManagementController::class, 'showRegistration'])->name('registrations.show');
        Route::post('/registrations/{id}/status', [App\Http\Controllers\Admin\PPDBManagementController::class, 'updateRegistrationStatus'])->name('registrations.status');
    });
});
