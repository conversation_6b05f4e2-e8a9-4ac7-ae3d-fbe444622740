@extends('layouts.dashboard')

@section('title', 'Pengaturan PPDB')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Pengaturan PPDB Online</h1>
    <div>
        <a href="{{ route('admin.ppdb.dashboard') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-2"></i>Kembali ke Dashboard
        </a>
        <a href="{{ route('ppdb.index') }}" class="btn btn-outline-success" target="_blank">
            <i class="fas fa-external-link-alt me-2"></i>Lihat Halaman PPDB
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>Pengaturan PPDB
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.ppdb.settings.update') }}" method="POST">
                    @csrf
                    
                    <!-- Basic Settings -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="academic_year" class="form-label">Tahun Ajaran <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('academic_year') is-invalid @enderror" 
                                       id="academic_year" name="academic_year" 
                                       value="{{ old('academic_year', $ppdbSetting->academic_year ?? '') }}" 
                                       placeholder="2024/2025" required>
                                @error('academic_year')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status PPDB <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="closed" {{ old('status', $ppdbSetting->status ?? '') == 'closed' ? 'selected' : '' }}>Ditutup</option>
                                    <option value="open" {{ old('status', $ppdbSetting->status ?? '') == 'open' ? 'selected' : '' }}>Dibuka</option>
                                    <option value="maintenance" {{ old('status', $ppdbSetting->status ?? '') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Registration Period -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_start" class="form-label">Mulai Pendaftaran</label>
                                <input type="datetime-local" class="form-control @error('registration_start') is-invalid @enderror" 
                                       id="registration_start" name="registration_start" 
                                       value="{{ old('registration_start', $ppdbSetting && $ppdbSetting->registration_start ? $ppdbSetting->registration_start->format('Y-m-d\TH:i') : '') }}">
                                @error('registration_start')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_end" class="form-label">Akhir Pendaftaran</label>
                                <input type="datetime-local" class="form-control @error('registration_end') is-invalid @enderror" 
                                       id="registration_end" name="registration_end" 
                                       value="{{ old('registration_end', $ppdbSetting && $ppdbSetting->registration_end ? $ppdbSetting->registration_end->format('Y-m-d\TH:i') : '') }}">
                                @error('registration_end')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Document Completion Period -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_start" class="form-label">Mulai Pelengkapan Berkas</label>
                                <input type="datetime-local" class="form-control @error('document_start') is-invalid @enderror"
                                       id="document_start" name="document_start"
                                       value="{{ old('document_start', $ppdbSetting && $ppdbSetting->document_start ? $ppdbSetting->document_start->format('Y-m-d\TH:i') : '') }}">
                                @error('document_start')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Periode dimulainya pelengkapan berkas oleh calon siswa</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_end" class="form-label">Akhir Pelengkapan Berkas</label>
                                <input type="datetime-local" class="form-control @error('document_end') is-invalid @enderror"
                                       id="document_end" name="document_end"
                                       value="{{ old('document_end', $ppdbSetting && $ppdbSetting->document_end ? $ppdbSetting->document_end->format('Y-m-d\TH:i') : '') }}">
                                @error('document_end')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Batas akhir pelengkapan berkas oleh calon siswa</small>
                            </div>
                        </div>
                    </div>

                    <!-- Announcement Date -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="announcement_date" class="form-label">Tanggal Pengumuman</label>
                                <input type="datetime-local" class="form-control @error('announcement_date') is-invalid @enderror"
                                       id="announcement_date" name="announcement_date"
                                       value="{{ old('announcement_date', $ppdbSetting && $ppdbSetting->announcement_date ? $ppdbSetting->announcement_date->format('Y-m-d\TH:i') : '') }}">
                                @error('announcement_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Registration Fee -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_fee" class="form-label">Biaya Pendaftaran (Rp)</label>
                                <input type="number" class="form-control @error('registration_fee') is-invalid @enderror" 
                                       id="registration_fee" name="registration_fee" 
                                       value="{{ old('registration_fee', $ppdbSetting->registration_fee ?? 0) }}" 
                                       min="0" step="1000">
                                @error('registration_fee')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Masukkan 0 jika gratis</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status Aktif</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', $ppdbSetting->is_active ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Aktifkan pengaturan PPDB ini
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Requirements -->
                    <div class="mb-4">
                        <label for="requirements" class="form-label">Persyaratan Pendaftaran</label>
                        <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                  id="requirements" name="requirements" rows="4" 
                                  placeholder="Masukkan persyaratan pendaftaran...">{{ old('requirements', $ppdbSetting->requirements ?? '') }}</textarea>
                        @error('requirements')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Announcement Text -->
                    <div class="mb-4">
                        <label for="announcement_text" class="form-label">Teks Pengumuman</label>
                        <textarea class="form-control @error('announcement_text') is-invalid @enderror"
                                  id="announcement_text" name="announcement_text" rows="3"
                                  placeholder="Teks yang akan ditampilkan di halaman PPDB...">{{ old('announcement_text', $ppdbSetting->announcement_text ?? '') }}</textarea>
                        @error('announcement_text')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Required Documents -->
                    <div class="mb-4">
                        <label for="required_documents" class="form-label">Dokumen yang Diperlukan</label>
                        <div id="required-documents-container">
                            @php
                                $documents = old('required_documents', $ppdbSetting->required_documents ?? []);
                                if (empty($documents)) {
                                    $documents = [''];
                                }
                            @endphp
                            @foreach($documents as $index => $document)
                            <div class="input-group mb-2 document-input-group">
                                <input type="text" class="form-control @error('required_documents.'.$index) is-invalid @enderror"
                                       name="required_documents[]" value="{{ $document }}"
                                       placeholder="Contoh: Ijazah/SKHUN, Kartu Keluarga, dll...">
                                <button type="button" class="btn btn-outline-danger remove-document"
                                        onclick="removeDocumentField(this)" {{ $index === 0 ? 'style=display:none;' : '' }}>
                                    <i class="fas fa-times"></i>
                                </button>
                                @error('required_documents.'.$index)
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            @endforeach
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addDocumentField()">
                            <i class="fas fa-plus me-1"></i>Tambah Dokumen
                        </button>
                        <small class="form-text text-muted d-block mt-2">
                            Masukkan dokumen yang diperlukan untuk pendaftaran. Kosongkan jika tidak ada dokumen khusus.
                        </small>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Simpan Pengaturan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Program Quotas Section -->
@if($programs->isNotEmpty())
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Kuota Program
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Program</th>
                                <th>Level</th>
                                <th>Status</th>
                                <th>Kuota</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($programs as $program)
                            <tr>
                                <td>{{ $program->name }}</td>
                                <td>
                                    <span class="badge bg-info">{{ ucfirst($program->level) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $program->is_active ? 'success' : 'secondary' }}">
                                        {{ $program->is_active ? 'Aktif' : 'Tidak Aktif' }}
                                    </span>
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" 
                                           style="width: 100px;" min="0" 
                                           placeholder="Kuota" disabled>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Fitur pengaturan kuota akan diimplementasikan pada versi selanjutnya.
                </small>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate academic year
    $('#academic_year').on('blur', function() {
        let value = $(this).val();
        if (value && !value.includes('/')) {
            let year = parseInt(value);
            if (year > 2000 && year < 3000) {
                $(this).val(year + '/' + (year + 1));
            }
        }
    });
    
    // Status change handler
    $('#status').change(function() {
        const status = $(this).val();
        const dateFields = $('#registration_start, #registration_end, #document_start, #document_end, #announcement_date');

        if (status === 'closed') {
            dateFields.prop('required', false);
        } else {
            $('#registration_start, #registration_end').prop('required', true);
        }
    });
});

// Function to add new document field
function addDocumentField() {
    const container = document.getElementById('required-documents-container');
    const newField = document.createElement('div');
    newField.className = 'input-group mb-2 document-input-group';
    newField.innerHTML = `
        <input type="text" class="form-control" name="required_documents[]"
               placeholder="Contoh: Ijazah/SKHUN, Kartu Keluarga, dll...">
        <button type="button" class="btn btn-outline-danger remove-document" onclick="removeDocumentField(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newField);
    updateRemoveButtons();
}

// Function to remove document field
function removeDocumentField(button) {
    button.closest('.document-input-group').remove();
    updateRemoveButtons();
}

// Function to update remove button visibility
function updateRemoveButtons() {
    const groups = document.querySelectorAll('.document-input-group');
    groups.forEach((group, index) => {
        const removeBtn = group.querySelector('.remove-document');
        if (groups.length === 1) {
            removeBtn.style.display = 'none';
        } else {
            removeBtn.style.display = 'block';
        }
    });
}
</script>
@endpush
