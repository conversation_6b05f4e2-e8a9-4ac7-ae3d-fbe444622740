<?php $__env->startSection('title', 'Log <PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Log <PERSON>amanan</h1>
    <div>
        <a href="<?php echo e(route('admin.security.dashboard')); ?>" class="btn btn-outline-info me-2">
            <i class="fas fa-chart-line me-2"></i>Dashboard Keamanan
        </a>
        <a href="<?php echo e(route('admin.security.export')); ?>" class="btn btn-outline-success">
            <i class="fas fa-download me-2"></i>Export Data
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stats-number"><?php echo e(number_format($stats['total_logs'])); ?></div>
            <div class="stats-label">Total Log</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stats-number"><?php echo e(number_format($stats['today_logs'])); ?></div>
            <div class="stats-label">Log Hari Ini</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-number"><?php echo e(number_format($stats['high_risk'])); ?></div>
            <div class="stats-label">High Risk</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number"><?php echo e(number_format($stats['unresolved'])); ?></div>
            <div class="stats-label">Belum Resolved</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.security.index')); ?>" class="row g-3">
            <div class="col-md-2">
                <label for="event_type" class="form-label">Tipe Event</label>
                <select name="event_type" id="event_type" class="form-select">
                    <option value="">Semua Tipe</option>
                    <?php $__currentLoopData = $eventTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($type); ?>" <?php echo e(request('event_type') == $type ? 'selected' : ''); ?>>
                            <?php echo e(ucfirst(str_replace('_', ' ', $type))); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="risk_level" class="form-label">Risk Level</label>
                <select name="risk_level" id="risk_level" class="form-select">
                    <option value="">Semua Level</option>
                    <option value="low" <?php echo e(request('risk_level') == 'low' ? 'selected' : ''); ?>>Low</option>
                    <option value="medium" <?php echo e(request('risk_level') == 'medium' ? 'selected' : ''); ?>>Medium</option>
                    <option value="high" <?php echo e(request('risk_level') == 'high' ? 'selected' : ''); ?>>High</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="user_id" class="form-label">User</label>
                <select name="user_id" id="user_id" class="form-select">
                    <option value="">Semua User</option>
                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($user->id); ?>" <?php echo e(request('user_id') == $user->id ? 'selected' : ''); ?>>
                            <?php echo e($user->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">Dari Tanggal</label>
                <input type="date" name="date_from" id="date_from" class="form-control" 
                       value="<?php echo e(request('date_from')); ?>">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">Sampai Tanggal</label>
                <input type="date" name="date_to" id="date_to" class="form-control" 
                       value="<?php echo e(request('date_to')); ?>">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="<?php echo e(route('admin.security.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Security Logs Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Daftar Log Keamanan</h5>
            <div class="btn-group">
                <?php if($logs->where('is_resolved', false)->count() > 0): ?>
                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#bulkResolveModal">
                        <i class="fas fa-check-double me-1"></i>Resolve Selected
                    </button>
                <?php endif; ?>
                <?php if($logs->count() > 0): ?>
                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAllModal">
                        <i class="fas fa-trash-alt me-1"></i>Delete All
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if($logs->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Waktu</th>
                            <th>User</th>
                            <th>Event Type</th>
                            <th>Deskripsi</th>
                            <th>IP Address</th>
                            <th>Risk Level</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="<?php echo e($log->risk_level == 'high' ? 'table-danger' : ($log->risk_level == 'medium' ? 'table-warning' : '')); ?>">
                            <td>
                                <?php if(!$log->is_resolved): ?>
                                    <input type="checkbox" name="log_ids[]" value="<?php echo e($log->id); ?>" class="form-check-input log-checkbox">
                                <?php endif; ?>
                            </td>
                            <td>
                                <small><?php echo e($log->created_at->format('d M Y H:i:s')); ?></small>
                            </td>
                            <td>
                                <?php if($log->user): ?>
                                    <div>
                                        <strong><?php echo e($log->user->name); ?></strong>
                                        <br><small class="text-muted"><?php echo e($log->user->email); ?></small>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Unknown</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($log->event_type == 'login' ? 'success' : ($log->event_type == 'failed_login' ? 'danger' : 'info')); ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $log->event_type))); ?>

                                </span>
                            </td>
                            <td>
                                <small><?php echo e(Str::limit($log->description, 50)); ?></small>
                            </td>
                            <td>
                                <small class="font-monospace"><?php echo e($log->ip_address); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($log->risk_level == 'high' ? 'danger' : ($log->risk_level == 'medium' ? 'warning' : 'success')); ?>">
                                    <?php echo e(ucfirst($log->risk_level)); ?>

                                </span>
                            </td>
                            <td>
                                <?php if($log->is_resolved): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Resolved
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>Pending
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.security.show', $log)); ?>" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if(!$log->is_resolved): ?>
                                        <form action="<?php echo e(route('admin.security.resolve', $log)); ?>" method="POST" style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-success" title="Resolve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteLog(<?php echo e($log->id); ?>)" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan <?php echo e($logs->firstItem()); ?> - <?php echo e($logs->lastItem()); ?> dari <?php echo e($logs->total()); ?> log
                </div>
                <?php echo e($logs->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada log ditemukan</h5>
                <p class="text-muted">Belum ada log keamanan yang sesuai dengan filter yang dipilih.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bulk Resolve Modal -->
<div class="modal fade" id="bulkResolveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resolve Multiple Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menandai log yang dipilih sebagai resolved?</p>
                <p class="text-info"><small>Log yang sudah resolved tidak dapat dikembalikan ke status pending.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form id="bulkResolveForm" action="<?php echo e(route('admin.security.bulk-resolve')); ?>" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="log_ids" id="selectedLogIds">
                    <button type="submit" class="btn btn-warning">Resolve Selected</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete All Modal -->
<div class="modal fade" id="deleteAllModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Hapus Semua Log Keamanan
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="deleteAllForm" method="POST" action="<?php echo e(route('admin.security.delete-all-post')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>PERINGATAN!</strong> Tindakan ini akan menghapus SEMUA log keamanan secara permanen dan tidak dapat dibatalkan.
                    </div>
                    <p>Untuk melanjutkan, ketik <strong>DELETE_ALL_LOGS</strong> di bawah ini:</p>
                    <input type="text" id="deleteConfirmInput" name="confirm" class="form-control" placeholder="Ketik DELETE_ALL_LOGS">
                    <div class="mt-3">
                        <small class="text-muted">
                            Total log yang akan dihapus: <strong><?php echo e($logs->total()); ?></strong>
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteAllBtn" onclick="submitDeleteAllForm()" disabled>
                    <i class="fas fa-trash-alt me-1"></i>Hapus Semua Log
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.bg-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.font-monospace {
    font-family: 'Courier New', monospace;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Select all checkbox
    $('#selectAll').change(function() {
        $('.log-checkbox').prop('checked', $(this).is(':checked'));
    });

    // Update select all when individual checkboxes change
    $('.log-checkbox').change(function() {
        const totalCheckboxes = $('.log-checkbox').length;
        const checkedCheckboxes = $('.log-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Bulk resolve
    $('#bulkResolveModal').on('show.bs.modal', function() {
        const selectedIds = $('.log-checkbox:checked').map(function() {
            return $(this).val();
        }).get();
        
        if (selectedIds.length === 0) {
            toastr.warning('Pilih minimal satu log untuk di-resolve');
            return false;
        }
        
        $('#selectedLogIds').val(JSON.stringify(selectedIds));
    });

    // Delete confirmation input validation
    $('#deleteConfirmInput').on('input', function() {
        const confirmBtn = $('#confirmDeleteAllBtn');
        if ($(this).val() === 'DELETE_ALL_LOGS') {
            confirmBtn.prop('disabled', false);
        } else {
            confirmBtn.prop('disabled', true);
        }
    });

    // Auto-refresh every 30 seconds for real-time updates
    setInterval(function() {
        if (!$('.modal').hasClass('show')) { // Don't refresh if modal is open
            location.reload();
        }
    }, 30000);
});

// Delete individual log
function deleteLog(logId) {
    Swal.fire({
        title: 'Hapus Log Keamanan?',
        text: 'Log yang dihapus tidak dapat dikembalikan!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/security/logs/${logId}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    }
                },
                error: function(xhr) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus log keamanan.',
                        icon: 'error'
                    });
                }
            });
        }
    });
}

// Delete all logs using form submission
function submitDeleteAllForm() {
    console.log('submitDeleteAllForm called');

    const form = document.getElementById('deleteAllForm');
    const formData = new FormData(form);

    // Show loading state
    const btn = document.getElementById('confirmDeleteAllBtn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Menghapus...';
    btn.disabled = true;

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Success Response:', data);
        $('#deleteAllModal').modal('hide');

        if (data.success) {
            Swal.fire({
                title: 'Berhasil!',
                text: data.message,
                icon: 'success',
                timer: 3000,
                showConfirmButton: false
            }).then(() => {
                location.reload();
            });
        } else {
            throw new Error(data.message || 'Terjadi kesalahan');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        $('#deleteAllModal').modal('hide');

        let message = 'Gagal menghapus semua log keamanan.';
        if (error.message.includes('404')) {
            message = 'Route tidak ditemukan. Silakan refresh halaman dan coba lagi.';
        } else if (error.message.includes('403')) {
            message = 'Anda tidak memiliki izin untuk melakukan aksi ini.';
        } else if (error.message !== 'Terjadi kesalahan') {
            message = error.message;
        }

        Swal.fire({
            title: 'Error!',
            text: message,
            icon: 'error'
        });
    })
    .finally(() => {
        // Restore button state
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/security/index.blade.php ENDPATH**/ ?>