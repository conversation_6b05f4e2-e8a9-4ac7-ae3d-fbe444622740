@extends('layouts.dashboard')

@section('title', 'Manajemen User')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Manajemen User</h1>
    <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Tambah User
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number">{{ $stats['total_users'] }}</div>
            <div class="stats-label">Total User</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="stats-number">{{ $stats['total_students'] }}</div>
            <div class="stats-label">Siswa</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-chalkboard-teacher"></i>
            </div>
            <div class="stats-number">{{ $stats['total_teachers'] }}</div>
            <div class="stats-label">Guru</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-users-cog"></i>
            </div>
            <div class="stats-number">{{ $stats['total_parents'] }}</div>
            <div class="stats-label">Orang Tua</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="stats-number">{{ $stats['total_admins'] }}</div>
            <div class="stats-label">Admin</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.users.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="user_type" class="form-label">Tipe User</label>
                <select name="user_type" id="user_type" class="form-select">
                    <option value="">Semua Tipe</option>
                    <option value="super_admin" {{ request('user_type') == 'super_admin' ? 'selected' : '' }}>Super Admin</option>
                    <option value="admin" {{ request('user_type') == 'admin' ? 'selected' : '' }}>Admin</option>
                    <option value="guru" {{ request('user_type') == 'guru' ? 'selected' : '' }}>Guru</option>
                    <option value="siswa" {{ request('user_type') == 'siswa' ? 'selected' : '' }}>Siswa</option>
                    <option value="orang_tua" {{ request('user_type') == 'orang_tua' ? 'selected' : '' }}>Orang Tua</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">Pencarian</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari nama, email, username, NISN, atau NIP..." 
                       value="{{ request('search') }}">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Daftar User</h5>
    </div>
    <div class="card-body">
        @if($users->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Foto</th>
                            <th>Nama</th>
                            <th>Email</th>
                            <th>Tipe User</th>
                            <th>NISN/NIP</th>
                            <th>Status</th>
                            <th>Lock Status</th>
                            <th>Bergabung</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                        <tr>
                            <td>
                                <div class="avatar-sm">
                                    @if($user->avatar_url)
                                        <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="rounded-circle">
                                    @else
                                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                                            {{ $user->avatar_initials }}
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $user->name }}</strong>
                                    @if($user->username)
                                        <br><small class="text-muted">{{ $user->username }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>{{ $user->email }}</td>
                            <td>
                                <span class="badge bg-{{ $user->user_type == 'super_admin' ? 'danger' : ($user->user_type == 'admin' ? 'warning' : ($user->user_type == 'guru' ? 'info' : ($user->user_type == 'siswa' ? 'success' : 'secondary'))) }}">
                                    {{ ucfirst(str_replace('_', ' ', $user->user_type)) }}
                                </span>
                            </td>
                            <td>
                                @if($user->nisn)
                                    <small class="text-muted">NISN:</small> {{ $user->nisn }}
                                @elseif($user->nip)
                                    <small class="text-muted">NIP:</small> {{ $user->nip }}
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input status-toggle" type="checkbox"
                                           data-user-id="{{ $user->id }}"
                                           data-user-name="{{ $user->name }}"
                                           {{ $user->is_active ? 'checked' : '' }}
                                           {{ $user->id === Auth::id() ? 'disabled' : '' }}>
                                    <label class="form-check-label">
                                        {{ $user->is_active ? 'Aktif' : 'Nonaktif' }}
                                    </label>
                                </div>
                            </td>
                            <td>
                                @if($user->isLocked())
                                    <span class="badge bg-danger">
                                        <i class="fas fa-lock me-1"></i>Terkunci
                                    </span>
                                    <br><small class="text-muted">{{ $user->locked_until->format('d M Y H:i') }}</small>
                                @elseif($user->hasFailedAttempts())
                                    <span class="badge bg-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>{{ $user->failed_login_attempts }} Gagal
                                    </span>
                                @else
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Normal
                                    </span>
                                @endif
                            </td>
                            <td>
                                <small class="text-muted">{{ $user->created_at->format('d M Y') }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($user->id !== Auth::id())
                                        @if($user->isLocked())
                                            <button type="button" class="btn btn-sm btn-outline-success unlock-account"
                                                    data-user-id="{{ $user->id }}"
                                                    data-user-name="{{ $user->name }}" title="Buka Kunci">
                                                <i class="fas fa-unlock"></i>
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-sm btn-outline-secondary lock-account"
                                                    data-user-id="{{ $user->id }}"
                                                    data-user-name="{{ $user->name }}" title="Kunci Akun">
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-user"
                                                data-user-id="{{ $user->id }}"
                                                data-user-name="{{ $user->name }}" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan {{ $users->firstItem() }} - {{ $users->lastItem() }} dari {{ $users->total() }} user
                </div>
                {{ $users->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada user ditemukan</h5>
                <p class="text-muted">Belum ada user yang sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Tambah User Pertama
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@push('styles')
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-sm img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    background-color: #6c757d;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.bg-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}
</style>
@endpush

@push('scripts')
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Status toggle with SweetAlert
    $('.status-toggle').change(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');
        const isActive = $(this).is(':checked');
        const statusText = isActive ? 'aktifkan' : 'nonaktifkan';
        const checkbox = $(this);

        console.log('Toggle Status:', {
            userId: userId,
            userName: userName,
            isActive: isActive,
            statusText: statusText
        });

        Swal.fire({
            title: 'Konfirmasi',
            text: `Apakah Anda yakin ingin ${statusText} user ${userName}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `Ya, ${statusText}!`,
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('Making AJAX request to:', `/admin/users/${userId}/toggle-status`);

                $.ajax({
                    url: `/admin/users/${userId}/toggle-status`,
                    method: 'POST',
                    beforeSend: function() {
                        console.log('AJAX request started');
                    },
                    success: function(response) {
                        console.log('AJAX Success Response:', response);

                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                            // Update label
                            const label = checkbox.next('label');
                            label.text(response.is_active ? 'Aktif' : 'Nonaktif');
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                            // Revert checkbox
                            checkbox.prop('checked', !isActive);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('AJAX Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat mengubah status user.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                        // Revert checkbox
                        checkbox.prop('checked', !isActive);
                    }
                });
            } else {
                // Revert checkbox if cancelled
                checkbox.prop('checked', !isActive);
            }
        });
    });

    // Delete user with SweetAlert
    $('.delete-user').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');

        console.log('Delete User:', {
            userId: userId,
            userName: userName,
            csrfToken: $('meta[name="csrf-token"]').attr('content')
        });

        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: `Apakah Anda yakin ingin menghapus user ${userName}? Tindakan ini tidak dapat dibatalkan!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'Menghapus...',
                    text: 'Mohon tunggu sebentar',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Use AJAX for delete
                console.log('Making DELETE request to:', `/admin/users/${userId}`);

                $.ajax({
                    url: `/admin/users/${userId}`,
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    beforeSend: function() {
                        console.log('DELETE request started');
                    },
                    success: function(response) {
                        console.log('DELETE Success Response:', response);

                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message || 'User berhasil dihapus.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    },
                    error: function(xhr, status, error) {
                        console.log('Delete AJAX Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat menghapus user.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        } else if (xhr.responseText) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.message) {
                                    message = response.message;
                                }
                            } catch (e) {
                                // Keep default message
                            }
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Lock account
    $('.lock-account').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');

        console.log('Lock Account:', {
            userId: userId,
            userName: userName
        });

        Swal.fire({
            title: 'Konfirmasi Kunci Akun',
            text: `Apakah Anda yakin ingin mengunci akun ${userName}? Akun akan terkunci selama 24 jam.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#6c757d',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Kunci Akun!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('Making AJAX request to lock account:', `/admin/users/${userId}/lock-account`);

                $.ajax({
                    url: `/admin/users/${userId}/lock-account`,
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    beforeSend: function() {
                        console.log('Lock account request started');
                    },
                    success: function(response) {
                        console.log('Lock Account Success Response:', response);

                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 3000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Lock Account Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat mengunci akun.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Unlock account
    $('.unlock-account').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');

        console.log('Unlock Account:', {
            userId: userId,
            userName: userName
        });

        Swal.fire({
            title: 'Konfirmasi Buka Kunci',
            text: `Apakah Anda yakin ingin membuka kunci akun ${userName}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Buka Kunci!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('Making AJAX request to unlock account:', `/admin/users/${userId}/unlock-account`);

                $.ajax({
                    url: `/admin/users/${userId}/unlock-account`,
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    beforeSend: function() {
                        console.log('Unlock account request started');
                    },
                    success: function(response) {
                        console.log('Unlock Account Success Response:', response);

                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 3000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Unlock Account Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat membuka kunci akun.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
});
</script>
@endpush
