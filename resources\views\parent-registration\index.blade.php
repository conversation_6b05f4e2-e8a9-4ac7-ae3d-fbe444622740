@extends('layouts.landing')

@section('title', 'Pendaftaran Orang Tua')
@section('description', 'Daftar sebagai orang tua siswa untuk mengakses portal orang tua')

@push('styles')
<style>
    .registration-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 40px 0;
    }
    
    .registration-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        max-width: 800px;
        margin: 0 auto;
    }
    
    .card-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .card-header h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
    }
    
    .card-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
    }
    
    .card-body {
        padding: 40px;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-right: 10px;
        color: #667eea;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 1rem;
    }
    
    .verification-methods {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .method-option {
        margin-bottom: 15px;
    }
    
    .method-option:last-child {
        margin-bottom: 0;
    }
    
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .student-info {
        background: #e3f2fd;
        border-radius: 10px;
        padding: 15px;
        margin-top: 15px;
        display: none;
    }
    
    .student-info.show {
        display: block;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 15px 40px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        width: 100%;
        margin-top: 20px;
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 10px 20px;
        border-radius: 20px;
        font-weight: 600;
        margin-left: 10px;
    }
    
    .alert {
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .required {
        color: #dc3545;
    }
    
    @media (max-width: 768px) {
        .registration-container {
            padding: 20px 10px;
        }
        
        .card-header {
            padding: 20px;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .card-header h1 {
            font-size: 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<div class="registration-container">
    <div class="container">
        <div class="registration-card">
            <!-- Header -->
            <div class="card-header">
                <h1><i class="fas fa-users me-2"></i>Pendaftaran Orang Tua</h1>
                <p>Daftar untuk mengakses portal orang tua dan memantau perkembangan anak Anda</p>
            </div>
            
            <!-- Body -->
            <div class="card-body">
                <form id="parentRegistrationForm" method="POST" action="{{ route('parent-registration.register') }}">
                    @csrf
                    
                    <!-- Parent Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-user"></i>
                            Informasi Orang Tua
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Nama Lengkap <span class="required">*</span></label>
                                    <input type="text" class="form-control" name="parent_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Email <span class="required">*</span></label>
                                    <input type="email" class="form-control" name="parent_email" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Nomor Telepon <span class="required">*</span></label>
                                    <input type="tel" class="form-control" name="parent_phone" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Status <span class="required">*</span></label>
                                    <select class="form-select" name="parent_type" required>
                                        <option value="">Pilih Status</option>
                                        <option value="ayah">Ayah</option>
                                        <option value="ibu">Ibu</option>
                                        <option value="wali">Wali</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">NIK <span class="required">*</span></label>
                                    <input type="text" class="form-control" name="parent_nik" maxlength="16" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Tanggal Lahir <span class="required">*</span></label>
                                    <input type="date" class="form-control" name="parent_birth_date" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Pekerjaan</label>
                                    <input type="text" class="form-control" name="parent_occupation">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Hubungan dengan Siswa <span class="required">*</span></label>
                                    <select class="form-select" name="relationship" required>
                                        <option value="">Pilih Hubungan</option>
                                        <option value="ayah">Ayah</option>
                                        <option value="ibu">Ibu</option>
                                        <option value="wali">Wali</option>
                                        <option value="kakek">Kakek</option>
                                        <option value="nenek">Nenek</option>
                                        <option value="paman">Paman</option>
                                        <option value="bibi">Bibi</option>
                                        <option value="lainnya">Lainnya</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Alamat Lengkap <span class="required">*</span></label>
                            <textarea class="form-control" name="parent_address" rows="3" required></textarea>
                        </div>
                    </div>
                    
                    <!-- Student Verification Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-graduation-cap"></i>
                            Verifikasi Siswa
                        </h3>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Informasi:</strong> Untuk memverifikasi hubungan Anda dengan siswa, silakan pilih salah satu metode di bawah ini.
                            Jika siswa belum memiliki akun portal, gunakan metode NISN dan Nama Siswa.
                        </div>
                        
                        <div class="verification-methods">
                            <p><strong>Pilih metode verifikasi siswa:</strong></p>

                            <div class="method-option">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="verification_method" id="method_nisn" value="nisn" checked>
                                    <label class="form-check-label" for="method_nisn">
                                        <strong>NISN dan Nama Siswa</strong> <span class="badge bg-success">Direkomendasikan</span><br>
                                        <small class="text-muted">Verifikasi menggunakan NISN dan nama siswa yang terdaftar</small>
                                    </label>
                                </div>
                            </div>

                            <div class="method-option">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="verification_method" id="method_code" value="code">
                                    <label class="form-check-label" for="method_code">
                                        <strong>Kode Verifikasi Siswa</strong> <span class="badge bg-warning">Beta</span><br>
                                        <small class="text-muted">Gunakan kode verifikasi dari portal siswa (jika siswa sudah memiliki akun)</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Code Verification Fields -->
                        <div id="code_fields" class="verification-fields" style="display: none;">
                            <div class="form-group">
                                <label class="form-label">Kode Verifikasi Siswa <span class="required">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="student_verification_code" id="verification_code" placeholder="Masukkan kode verifikasi">
                                    <button type="button" class="btn btn-secondary" id="check_code_btn">Cek Kode</button>
                                </div>
                                <small class="text-muted">Minta kode verifikasi dari siswa yang bersangkutan</small>
                            </div>
                        </div>
                        
                        <!-- NISN Verification Fields -->
                        <div id="nisn_fields" class="verification-fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Nama Siswa <span class="required">*</span></label>
                                        <input type="text" class="form-control" name="student_name" placeholder="Nama lengkap siswa">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">NISN Siswa</label>
                                        <input type="text" class="form-control" name="student_nisn" maxlength="10" placeholder="Nomor Induk Siswa Nasional">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Kelas Siswa</label>
                                <select class="form-select" name="student_class">
                                    <option value="">Pilih Kelas Siswa</option>
                                    @foreach($classes as $class)
                                        <option value="{{ $class->name }}">{{ $class->name }}</option>
                                    @endforeach
                                </select>
                                <small class="text-muted">Pilih kelas siswa saat ini</small>
                            </div>
                        </div>
                        
                        <!-- Student Info Display -->
                        <div id="student_info" class="student-info">
                            <h5><i class="fas fa-check-circle text-success me-2"></i>Siswa Ditemukan</h5>
                            <div id="student_details"></div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary" id="submit_btn">
                            <i class="fas fa-paper-plane me-2"></i>Daftar Sekarang
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const verificationMethods = document.querySelectorAll('input[name="verification_method"]');
    const codeFields = document.getElementById('code_fields');
    const nisnFields = document.getElementById('nisn_fields');
    const checkCodeBtn = document.getElementById('check_code_btn');
    const studentInfo = document.getElementById('student_info');
    const form = document.getElementById('parentRegistrationForm');

    // Show NISN fields by default (since it's checked)
    nisnFields.style.display = 'block';

    // Handle verification method change
    verificationMethods.forEach(method => {
        method.addEventListener('change', function() {
            codeFields.style.display = 'none';
            nisnFields.style.display = 'none';
            studentInfo.classList.remove('show');

            if (this.value === 'code') {
                codeFields.style.display = 'block';
            } else if (this.value === 'nisn') {
                nisnFields.style.display = 'block';
            }
        });
    });
    
    // Check verification code
    checkCodeBtn.addEventListener('click', function() {
        const code = document.getElementById('verification_code').value;
        
        if (!code) {
            Swal.fire({
                icon: 'warning',
                title: 'Kode Diperlukan',
                text: 'Silakan masukkan kode verifikasi terlebih dahulu.'
            });
            return;
        }
        
        // Disable button
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mengecek...';
        
        // Send request
        fetch('{{ route("parent-registration.check-code") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ code: code })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show student info
                document.getElementById('student_details').innerHTML = `
                    <p><strong>Nama:</strong> ${data.student.name}</p>
                    <p><strong>NISN:</strong> ${data.student.nisn || 'Tidak tersedia'}</p>
                    <p><strong>Kelas:</strong> ${data.student.class}</p>
                `;
                studentInfo.classList.add('show');
                
                Swal.fire({
                    icon: 'success',
                    title: 'Siswa Ditemukan!',
                    text: `Siswa ${data.student.name} berhasil ditemukan.`
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Kode Tidak Valid',
                    text: data.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Terjadi Kesalahan',
                text: 'Gagal mengecek kode verifikasi.'
            });
        })
        .finally(() => {
            // Re-enable button
            this.disabled = false;
            this.innerHTML = 'Cek Kode';
        });
    });
    
    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show confirmation
        Swal.fire({
            title: 'Konfirmasi Pendaftaran',
            text: 'Apakah data yang Anda masukkan sudah benar?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#667eea',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Daftar',
            cancelButtonText: 'Periksa Lagi'
        }).then((result) => {
            if (result.isConfirmed) {
                submitForm();
            }
        });
    });
    
    function submitForm() {
        const submitBtn = document.getElementById('submit_btn');
        const formData = new FormData(form);
        
        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mendaftar...';
        
        // Send request
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Pendaftaran Berhasil!',
                    text: data.message,
                    confirmButtonColor: '#667eea'
                }).then(() => {
                    window.location.href = data.redirect_url;
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Pendaftaran Gagal',
                    text: data.message,
                    confirmButtonColor: '#667eea'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Terjadi Kesalahan',
                text: 'Gagal mengirim data pendaftaran.',
                confirmButtonColor: '#667eea'
            });
        })
        .finally(() => {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Daftar Sekarang';
        });
    }
});
</script>
@endpush
