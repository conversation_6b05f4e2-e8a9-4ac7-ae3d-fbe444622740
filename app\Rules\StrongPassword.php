<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class StrongPassword implements ValidationRule
{
    private $minLength;
    private $requireUppercase;
    private $requireLowercase;
    private $requireNumbers;
    private $requireSymbols;
    private $forbiddenPatterns;

    public function __construct(
        int $minLength = 8,
        bool $requireUppercase = true,
        bool $requireLowercase = true,
        bool $requireNumbers = true,
        bool $requireSymbols = true
    ) {
        $this->minLength = $minLength;
        $this->requireUppercase = $requireUppercase;
        $this->requireLowercase = $requireLowercase;
        $this->requireNumbers = $requireNumbers;
        $this->requireSymbols = $requireSymbols;
        
        // Common weak password patterns
        $this->forbiddenPatterns = [
            'password',
            '123456',
            'qwerty',
            'admin',
            'user',
            'guest',
            'test',
            'demo',
            'sekolah',
            'siswa',
            'guru',
            'orang_tua'
        ];
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            $fail('Password harus berupa string.');
            return;
        }

        // Check minimum length
        if (strlen($value) < $this->minLength) {
            $fail("Password minimal {$this->minLength} karakter.");
            return;
        }

        // Check maximum length (prevent DoS)
        if (strlen($value) > 128) {
            $fail('Password maksimal 128 karakter.');
            return;
        }

        $errors = [];

        // Check for uppercase letters
        if ($this->requireUppercase && !preg_match('/[A-Z]/', $value)) {
            $errors[] = 'minimal 1 huruf besar (A-Z)';
        }

        // Check for lowercase letters
        if ($this->requireLowercase && !preg_match('/[a-z]/', $value)) {
            $errors[] = 'minimal 1 huruf kecil (a-z)';
        }

        // Check for numbers
        if ($this->requireNumbers && !preg_match('/[0-9]/', $value)) {
            $errors[] = 'minimal 1 angka (0-9)';
        }

        // Check for symbols
        if ($this->requireSymbols && !preg_match('/[^A-Za-z0-9]/', $value)) {
            $errors[] = 'minimal 1 simbol (!@#$%^&*()_+-=[]{}|;:,.<>?)';
        }

        // Check for forbidden patterns
        $lowerValue = strtolower($value);
        foreach ($this->forbiddenPatterns as $pattern) {
            if (strpos($lowerValue, strtolower($pattern)) !== false) {
                $errors[] = "tidak boleh mengandung kata '{$pattern}'";
                break;
            }
        }

        // Check for sequential characters
        if ($this->hasSequentialChars($value)) {
            $errors[] = 'tidak boleh mengandung karakter berurutan (123, abc, dll)';
        }

        // Check for repeated characters
        if ($this->hasRepeatedChars($value)) {
            $errors[] = 'tidak boleh mengandung karakter berulang lebih dari 3 kali';
        }

        // Check for common keyboard patterns
        if ($this->hasKeyboardPattern($value)) {
            $errors[] = 'tidak boleh mengandung pola keyboard (qwerty, asdf, dll)';
        }

        if (!empty($errors)) {
            $fail('Password harus mengandung ' . implode(', ', $errors) . '.');
        }
    }

    /**
     * Check for sequential characters
     */
    private function hasSequentialChars(string $password): bool
    {
        $sequences = [
            '0123456789',
            'abcdefghijklmnopqrstuvwxyz',
            'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
        ];

        foreach ($sequences as $sequence) {
            for ($i = 0; $i <= strlen($sequence) - 4; $i++) {
                $subseq = substr($sequence, $i, 4);
                if (strpos($password, $subseq) !== false || strpos($password, strrev($subseq)) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check for repeated characters
     */
    private function hasRepeatedChars(string $password): bool
    {
        return preg_match('/(.)\1{3,}/', $password);
    }

    /**
     * Check for keyboard patterns
     */
    private function hasKeyboardPattern(string $password): bool
    {
        $patterns = [
            'qwerty', 'qwertyuiop', 'asdf', 'asdfghjkl', 'zxcv', 'zxcvbnm',
            '1234', '12345', '123456', '1234567', '12345678', '123456789'
        ];

        $lowerPassword = strtolower($password);
        
        foreach ($patterns as $pattern) {
            if (strpos($lowerPassword, $pattern) !== false || strpos($lowerPassword, strrev($pattern)) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get password strength score (0-100)
     */
    public static function getStrengthScore(string $password): int
    {
        $score = 0;
        $length = strlen($password);

        // Length score (max 25 points)
        if ($length >= 8) $score += 10;
        if ($length >= 12) $score += 10;
        if ($length >= 16) $score += 5;

        // Character variety (max 40 points)
        if (preg_match('/[a-z]/', $password)) $score += 10;
        if (preg_match('/[A-Z]/', $password)) $score += 10;
        if (preg_match('/[0-9]/', $password)) $score += 10;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 10;

        // Complexity bonus (max 35 points)
        $uniqueChars = count(array_unique(str_split($password)));
        if ($uniqueChars >= 8) $score += 15;
        if ($uniqueChars >= 12) $score += 10;
        if ($uniqueChars >= 16) $score += 10;

        return min(100, $score);
    }

    /**
     * Get password strength label
     */
    public static function getStrengthLabel(int $score): string
    {
        if ($score < 30) return 'Sangat Lemah';
        if ($score < 50) return 'Lemah';
        if ($score < 70) return 'Sedang';
        if ($score < 90) return 'Kuat';
        return 'Sangat Kuat';
    }

    /**
     * Get password strength color
     */
    public static function getStrengthColor(int $score): string
    {
        if ($score < 30) return 'danger';
        if ($score < 50) return 'warning';
        if ($score < 70) return 'info';
        if ($score < 90) return 'success';
        return 'primary';
    }
}
