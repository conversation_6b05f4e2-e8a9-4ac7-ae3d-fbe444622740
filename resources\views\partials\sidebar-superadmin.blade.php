<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>

    <!-- Profile -->
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" href="{{ route('profile.show') }}">
            <i class="fas fa-user-circle"></i>
            Profil Saya
        </a>
    </li>

    <!-- User Management -->
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}" href="{{ route('admin.users.index') }}">
            <i class="fas fa-users"></i>
            Manajemen User
        </a>
    </li>
    
    <!-- Content Management -->
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.content.*') ? 'active' : '' }}" href="#" 
           data-bs-toggle="collapse" data-bs-target="#contentMenu" aria-expanded="{{ request()->routeIs('admin.content.*') ? 'true' : 'false' }}">
            <i class="fas fa-edit"></i>
            Manajemen Konten
        </a>
        <div class="collapse {{ request()->routeIs('admin.content.*') ? 'show' : '' }}" id="contentMenu">
            <ul class="nav flex-column ms-3">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.content.news.*') ? 'active' : '' }}" 
                       href="{{ route('admin.content.news.index') }}">
                        <i class="fas fa-newspaper"></i>
                        Berita & Pengumuman
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.content.programs.*') ? 'active' : '' }}" 
                       href="{{ route('admin.content.programs.index') }}">
                        <i class="fas fa-graduation-cap"></i>
                        Program Pendidikan
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.content.facilities.*') ? 'active' : '' }}" 
                       href="{{ route('admin.content.facilities.index') }}">
                        <i class="fas fa-building"></i>
                        Fasilitas
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.content.gallery.*') ? 'active' : '' }}" 
                       href="{{ route('admin.content.gallery.index') }}">
                        <i class="fas fa-images"></i>
                        Galeri
                    </a>
                </li>
            </ul>
        </div>
    </li>
    
    <!-- PPDB Management -->
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('admin.ppdb.*') ? 'active' : '' }}" href="{{ route('admin.ppdb.dashboard') }}">
            <i class="fas fa-user-graduate"></i>
            PPDB Online
        </a>
    </li>
    
    <!-- School Settings -->
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}" href="#" 
           data-bs-toggle="collapse" data-bs-target="#settingsMenu" aria-expanded="{{ request()->routeIs('admin.settings.*') ? 'true' : 'false' }}">
            <i class="fas fa-cog"></i>
            Pengaturan Sekolah
        </a>
        <div class="collapse {{ request()->routeIs('admin.settings.*') ? 'show' : '' }}" id="settingsMenu">
            <ul class="nav flex-column ms-3">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.index') ? 'active' : '' }}" 
                       href="{{ route('admin.settings.index') }}">
                        <i class="fas fa-school"></i>
                        Info Sekolah
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.hero-sections*') ? 'active' : '' }}" 
                       href="{{ route('admin.settings.hero-sections') }}">
                        <i class="fas fa-image"></i>
                        Hero Sections
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.system*') ? 'active' : '' }}" 
                       href="{{ route('admin.settings.system') }}">
                        <i class="fas fa-server"></i>
                        Sistem
                    </a>
                </li>
            </ul>
        </div>
    </li>
    
    <!-- Security & Logs -->
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.security.*') ? 'active' : '' }}" href="#" 
           data-bs-toggle="collapse" data-bs-target="#securityMenu" aria-expanded="{{ request()->routeIs('admin.security.*') ? 'true' : 'false' }}">
            <i class="fas fa-shield-alt"></i>
            Keamanan & Log
        </a>
        <div class="collapse {{ request()->routeIs('admin.security.*') ? 'show' : '' }}" id="securityMenu">
            <ul class="nav flex-column ms-3">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.security.dashboard') ? 'active' : '' }}" 
                       href="{{ route('admin.security.dashboard') }}">
                        <i class="fas fa-chart-line"></i>
                        Dashboard Keamanan
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.security.index') ? 'active' : '' }}" 
                       href="{{ route('admin.security.index') }}">
                        <i class="fas fa-list"></i>
                        Log Keamanan
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.security.user-activity*') ? 'active' : '' }}" 
                       href="{{ route('admin.security.user-activity') }}">
                        <i class="fas fa-user-clock"></i>
                        Aktivitas User
                    </a>
                </li>
            </ul>
        </div>
    </li>
    
    <li class="nav-item mt-3">
        <hr class="sidebar-divider">
    </li>
    
    <li class="nav-item">
        <a class="nav-link" href="{{ route('landing') }}" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>

<style>
.sidebar-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

.nav-link.dropdown-toggle {
    position: relative;
}

.nav-link.dropdown-toggle::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.nav-link.dropdown-toggle[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}

.nav-link.dropdown-toggle::before {
    display: none;
}

.collapse .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.collapse .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.collapse .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
}

.collapse .nav-link i {
    width: 20px;
    margin-right: 8px;
}
</style>
