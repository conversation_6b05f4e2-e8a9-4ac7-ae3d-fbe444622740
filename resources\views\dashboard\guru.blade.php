@extends('layouts.dashboard')

@section('title', 'Portal Guru')
@section('page-title', 'Portal Guru - Selamat Datang di Portal Guru')

@section('sidebar-menu')
<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" href="{{ route('profile.show') }}">
            <i class="fas fa-user-circle"></i>
            Profil Saya
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-user-graduate"></i>
            Data Siswa
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-newspaper"></i>
            Berita & Pengumuman
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-book"></i>
            Materi Pembelajaran
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-tasks"></i>
            Tugas & Ujian
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-chart-line"></i>
            Nilai & Rapor
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-calendar-alt"></i>
            Jadwal Mengajar
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="{{ route('landing') }}" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-primary border-0" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-chalkboard-teacher fa-2x"></i>
                </div>
                <div>
                    <h5 class="mb-1">🎓 Selamat Datang di Portal Guru!</h5>
                    <p class="mb-0">
                        Halo, <strong>{{ Auth::user()->name }}</strong>!
                        @if($staffProfile)
                            Anda login sebagai {{ $staffProfile->position == 'kepala_sekolah' ? 'Kepala Sekolah' : 'Guru' }}
                            @if($staffProfile->subject) {{ $staffProfile->subject }}@endif.
                        @endif
                        <br><small>Kelola pembelajaran dan berinteraksi dengan siswa melalui portal ini.</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Card -->
@if($staffProfile)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-id-card me-2"></i>Profil Tenaga Pendidik
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        @if(Auth::user()->avatar)
                            <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="Avatar" class="rounded-circle mb-3" width="120" height="120" style="object-fit: cover;">
                        @else
                            <div class="rounded-circle bg-gradient-primary d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px;">
                                <i class="fas fa-user fa-3x text-white"></i>
                            </div>
                        @endif
                        <h5>{{ Auth::user()->name }}</h5>
                        @if($staffProfile->degree)
                            <p class="text-primary fw-semibold">{{ $staffProfile->degree }}</p>
                        @endif
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>NIP:</strong></td>
                                        <td>{{ Auth::user()->nip ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ Auth::user()->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Telepon:</strong></td>
                                        <td>{{ Auth::user()->phone ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Posisi:</strong></td>
                                        <td>
                                            @if($staffProfile->position == 'kepala_sekolah')
                                                Kepala Sekolah
                                            @elseif($staffProfile->position == 'guru')
                                                Guru
                                            @else
                                                {{ ucfirst(str_replace('_', ' ', $staffProfile->position)) }}
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    @if($staffProfile->subject)
                                    <tr>
                                        <td><strong>Mata Pelajaran:</strong></td>
                                        <td><span class="badge bg-primary">{{ $staffProfile->subject }}</span></td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td><strong>Bergabung:</strong></td>
                                        <td>{{ $staffProfile->join_date ? $staffProfile->join_date->format('d M Y') : '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ $staffProfile->employment_status == 'tetap' ? 'success' : 'warning' }}">
                                                {{ ucfirst($staffProfile->employment_status) }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Pengalaman:</strong></td>
                                        <td>{{ $staffProfile->experience ?? '-' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number">{{ $stats['my_students'] }}</div>
            <div class="stats-label">Siswa Saya</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-chalkboard"></i>
            </div>
            <div class="stats-number">{{ $stats['my_classes'] }}</div>
            <div class="stats-label">Kelas Saya</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stats-number">{{ $stats['pending_assignments'] }}</div>
            <div class="stats-label">Tugas Pending</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-book"></i>
            </div>
            <div class="stats-number">{{ $stats['my_subjects'] }}</div>
            <div class="stats-label">Mata Pelajaran</div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-newspaper me-2"></i>Berita Saya
            </div>
            <div class="card-body">
                @forelse($myNews as $news)
                    <div class="d-flex align-items-start mb-3">
                        @if($news->featured_image)
                            <img src="{{ asset('storage/' . $news->featured_image) }}" alt="News" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                        @else
                            <div class="rounded bg-gradient-primary d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-newspaper text-white"></i>
                            </div>
                        @endif
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ Str::limit($news->title, 50) }}</h6>
                            <small class="text-muted">
                                {{ $news->created_at->diffForHumans() }}
                            </small>
                            <br>
                            <span class="badge bg-{{ $news->is_published ? 'success' : 'warning' }}">
                                {{ $news->is_published ? 'Published' : 'Draft' }}
                            </span>
                        </div>
                    </div>
                @empty
                    <p class="text-muted text-center">Belum ada berita yang Anda buat</p>
                @endforelse
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-graduate me-2"></i>Siswa Terbaru
            </div>
            <div class="card-body">
                @forelse($recentStudents as $student)
                    <div class="d-flex align-items-center mb-3">
                        @if($student->avatar)
                            <img src="{{ asset('storage/' . $student->avatar) }}" alt="Avatar" class="rounded-circle me-3" width="40" height="40">
                        @else
                            <div class="rounded-circle bg-gradient-success d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">{{ strtoupper(substr($student->name, 0, 1)) }}</span>
                            </div>
                        @endif
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ $student->name }}</h6>
                            <small class="text-muted">
                                NISN: {{ $student->nisn ?? '-' }} • {{ $student->created_at->diffForHumans() }}
                            </small>
                        </div>
                        <span class="badge bg-{{ $student->is_active ? 'success' : 'danger' }}">
                            {{ $student->is_active ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                @empty
                    <p class="text-muted text-center">Belum ada siswa terbaru</p>
                @endforelse
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>Aksi Cepat
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-newspaper me-2"></i>Buat Berita
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-book me-2"></i>Upload Materi
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-tasks me-2"></i>Buat Tugas
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-danger w-100">
                            <i class="fas fa-user me-2"></i>Edit Profil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
