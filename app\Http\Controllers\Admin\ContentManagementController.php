<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;
use App\Models\Program;
use App\Models\Facility;
use App\Models\Gallery;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ContentManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:super_admin|admin');
    }

    // NEWS MANAGEMENT
    public function newsIndex(Request $request)
    {
        $query = News::with('author');

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $news = $query->latest()->paginate(15);

        $stats = [
            'total_news' => News::count(),
            'published' => News::where('status', 'published')->count(),
            'draft' => News::where('status', 'draft')->count(),
            'featured' => News::where('is_featured', true)->count(),
        ];

        return view('admin.content.news.index', compact('news', 'stats'));
    }

    public function newsCreate()
    {
        return view('admin.content.news.create');
    }

    public function newsStore(Request $request)
    {
        // Simple validation
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:news,slug',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'type' => 'required|in:news,announcement',
            'status' => 'required|in:draft,published,archived',
            'category' => 'nullable|string|max:100',
            'tags' => 'nullable|string',
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'published_at' => 'nullable|date',
            'is_featured' => 'boolean',
        ]);

        try {

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                $validated['featured_image'] = $request->file('featured_image')->store('news', 'public');
            }

            // Set author
            $validated['author_id'] = auth()->id();

            // Handle published_at
            if ($validated['status'] === 'published' && !$validated['published_at']) {
                $validated['published_at'] = now();
            }

            // Create news
            $news = News::create($validated);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('news', 'public');
        }

        // Set published_at if status is published and not set
        if ($request->status === 'published' && !$request->published_at) {
            $data['published_at'] = now();
        }

        \Log::info('News Store - About to create news', [
            'data' => $data,
            'user_id' => auth()->id()
        ]);

        $news = News::create($data);

        \Log::info('News Store - News created successfully', [
            'news_id' => $news->id,
            'news_title' => $news->title,
            'user_id' => auth()->id()
        ]);

        \Log::info('=== NEWS STORE SUCCESS ===');

        return redirect()->route('admin.content.news.index')
            ->with('success', 'Berita berhasil ditambahkan.');

    } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('News Store - Validation Error', [
                'errors' => $e->errors(),
                'user_id' => auth()->id(),
                'request_data' => $request->except(['_token', 'featured_image'])
            ]);

        // Return back to form with validation errors
        return redirect()->back()
            ->withErrors($e->validator)
            ->withInput()
            ->with('error', 'Terdapat kesalahan validasi. Silakan periksa form Anda.');

    } catch (\Exception $e) {
            \Log::error('News Store - General Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'request_data' => $request->except(['_token', 'featured_image']),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);

        return redirect()->back()
            ->withInput()
            ->with('error', 'Terjadi kesalahan saat menyimpan berita: ' . $e->getMessage());
    }
    }

    public function newsShow(News $news)
    {
        // Load author relationship
        $news->load('author');

        return view('admin.content.news.show', compact('news'));
    }

    public function newsEdit(News $news)
    {
        return view('admin.content.news.edit', compact('news'));
    }

    public function newsUpdate(Request $request, News $news)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'type' => 'required|in:news,announcement',
            'status' => 'required|in:draft,published,archived',
            'category' => 'nullable|string|max:100',
            'tags' => 'nullable|string',
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'published_at' => 'nullable|date',
            'is_featured' => 'boolean',
        ]);

        $data = $request->only([
            'title', 'slug', 'excerpt', 'content', 'type', 'status',
            'category', 'tags', 'meta_title', 'meta_description'
        ]);

        // Handle published_at separately
        if ($request->published_at) {
            $data['published_at'] = $request->published_at;
        }

        $data['is_featured'] = $request->boolean('is_featured');
        $data['author_id'] = Auth::id();

        // Debug logging
        \Log::info('News Update - Featured Status', [
            'news_id' => $news->id,
            'old_is_featured' => $news->is_featured,
            'new_is_featured' => $data['is_featured'],
            'request_has_featured' => $request->has('is_featured'),
            'request_featured_value' => $request->get('is_featured'),
            'boolean_result' => $request->boolean('is_featured')
        ]);

        // Handle remove current image checkbox
        if ($request->boolean('remove_current_image')) {
            if ($news->featured_image && Storage::disk('public')->exists($news->featured_image)) {
                Storage::disk('public')->delete($news->featured_image);
            }
            $data['featured_image'] = null;
        }
        // Handle featured image upload
        elseif ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($news->featured_image && Storage::disk('public')->exists($news->featured_image)) {
                Storage::disk('public')->delete($news->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('news', 'public');
        }

        // Set published_at if status is published and not set
        if ($request->status === 'published' && !$request->published_at && !$news->published_at) {
            $data['published_at'] = now();
        }

        $news->update($data);

        return redirect()->route('admin.content.news.index')
            ->with('success', 'Berita berhasil diperbarui.');
    }

    public function newsDestroy($id)
    {
        try {
            // Find news by ID
            $news = News::findOrFail($id);

            \Log::info('Delete News Request - Manual Find Success', [
                'news_id' => $news->id,
                'news_title' => $news->title,
                'user_id' => Auth::id(),
                'request_url' => request()->url(),
                'request_method' => request()->method()
            ]);

            $newsTitle = $news->title;

            // Delete featured image if exists
            if ($news->featured_image && Storage::disk('public')->exists($news->featured_image)) {
                Storage::disk('public')->delete($news->featured_image);
            }

            $news->delete();

            \Log::info('Delete News Success', [
                'news_id' => $news->id,
                'news_title' => $newsTitle,
                'deleted_by' => Auth::id()
            ]);

            // Return JSON response for AJAX requests
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "Berita '{$newsTitle}' berhasil dihapus."
                ]);
            }

            // Return redirect for form submissions
            return redirect()->route('admin.content.news.index')
                ->with('success', "Berita '{$newsTitle}' berhasil dihapus.");

        } catch (\Exception $e) {
            \Log::error('Delete News Error', [
                'news_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $message = 'Terjadi kesalahan saat menghapus berita: ' . $e->getMessage();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $message
                ], 500);
            }

            return redirect()->route('admin.content.news.index')
                ->with('error', $message);
        }
    }

    /**
     * Remove featured image from news
     */
    public function newsRemoveImage(News $news)
    {
        try {
            \Log::info('Remove News Image Request', [
                'news_id' => $news->id,
                'news_title' => $news->title,
                'current_image' => $news->featured_image,
                'user_id' => Auth::id()
            ]);

            if ($news->featured_image && Storage::disk('public')->exists($news->featured_image)) {
                // Delete the image file
                Storage::disk('public')->delete($news->featured_image);

                // Update database
                $news->update(['featured_image' => null]);

                \Log::info('Remove News Image Success', [
                    'news_id' => $news->id,
                    'removed_by' => Auth::id()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Gambar berhasil dihapus.'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Gambar tidak ditemukan.'
            ], 404);

        } catch (\Exception $e) {
            \Log::error('Remove News Image Error', [
                'news_id' => $news->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    // PROGRAM MANAGEMENT
    public function programIndex(Request $request)
    {
        $query = Program::query();

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $programs = $query->orderBy('order')->paginate(15);

        $stats = [
            'total_programs' => Program::count(),
            'active' => Program::where('is_active', true)->count(),
            'inactive' => Program::where('is_active', false)->count(),
        ];

        return view('admin.content.programs.index', compact('programs', 'stats'));
    }

    public function programCreate()
    {
        return view('admin.content.programs.create');
    }

    public function programStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'level' => 'required|in:TK,SD,SMP,SMA,SMK',
            'type' => 'required|in:regular,unggulan,internasional',
            'curriculum' => 'nullable|string',
            'requirements' => 'nullable|string',
            'subjects' => 'nullable|array',
            'extracurricular' => 'nullable|array',
            'duration_years' => 'required|integer|min:1|max:6',
            'fee' => 'nullable|numeric|min:0',
            'capacity' => 'nullable|integer|min:1',
            'current_students' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        $data['is_active'] = $request->boolean('is_active', true);

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('programs', 'public');
        }

        Program::create($data);

        return redirect()->route('admin.content.programs.index')
            ->with('success', 'Program berhasil ditambahkan.');
    }

    public function programShow(Program $program)
    {
        return view('admin.content.programs.show', compact('program'));
    }

    public function programEdit(Program $program)
    {
        return view('admin.content.programs.edit', compact('program'));
    }

    public function programUpdate(Request $request, Program $program)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'level' => 'required|in:TK,SD,SMP,SMA,SMK',
            'type' => 'required|in:regular,unggulan,internasional',
            'curriculum' => 'nullable|string',
            'requirements' => 'nullable|string',
            'duration_years' => 'required|integer|min:1|max:6',
            'fee' => 'nullable|numeric|min:0',
            'capacity' => 'nullable|integer|min:1',
            'current_students' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'nullable|boolean',
        ]);

        $data = $request->only([
            'name', 'description', 'level', 'type', 'curriculum',
            'requirements', 'duration_years', 'fee', 'capacity', 'current_students'
        ]);

        $data['slug'] = Str::slug($request->name);
        $data['is_active'] = $request->has('is_active') ? true : false;

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($program->image) {
                Storage::disk('public')->delete($program->image);
            }
            $data['image'] = $request->file('image')->store('programs', 'public');
        }

        $program->update($data);

        return redirect()->route('admin.content.programs.index')
            ->with('success', 'Program berhasil diperbarui.');
    }

    public function programRemoveImage($programId)
    {
        try {
            // Find program by ID with error handling
            $program = Program::find($programId);

            if (!$program) {
                return response()->json([
                    'success' => false,
                    'message' => 'Program tidak ditemukan.'
                ], 404);
            }

            if ($program->image) {
                // Delete the image file
                Storage::disk('public')->delete($program->image);

                // Update the program record to remove image reference
                $program->update(['image' => null]);

                return response()->json([
                    'success' => true,
                    'message' => 'Gambar berhasil dihapus.'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Tidak ada gambar untuk dihapus.'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error removing program image: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    public function programDestroy(Program $program)
    {
        try {
            // Delete image if exists
            if ($program->image) {
                Storage::disk('public')->delete($program->image);
            }

            // Delete the program
            $program->delete();

            // Check if request expects JSON (AJAX)
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Program berhasil dihapus.'
                ]);
            }

            return redirect()->route('admin.content.programs.index')
                ->with('success', 'Program berhasil dihapus.');
        } catch (\Exception $e) {
            \Log::error('Error deleting program: ' . $e->getMessage());

            // Check if request expects JSON (AJAX)
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal menghapus program: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('admin.content.programs.index')
                ->with('error', 'Gagal menghapus program: ' . $e->getMessage());
        }
    }

    // FACILITY MANAGEMENT
    public function facilityIndex(Request $request)
    {
        $query = Facility::query();

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $facilities = $query->orderBy('order')->paginate(15);

        $stats = [
            'total_facilities' => Facility::count(),
            'available' => Facility::where('is_available', true)->count(),
            'featured' => Facility::where('is_featured', true)->count(),
        ];

        return view('admin.content.facilities.index', compact('facilities', 'stats'));
    }

    public function facilityCreate()
    {
        return view('admin.content.facilities.create');
    }

    public function facilityStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category' => 'required|in:academic,sports,library,laboratory,dormitory,cafeteria,mosque,other',
            'capacity' => 'nullable|integer|min:1',
            'specifications' => 'nullable|string',
            'features' => 'nullable|array',
            'is_available' => 'boolean',
            'is_featured' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        $data['is_available'] = $request->boolean('is_available', true);
        $data['is_featured'] = $request->boolean('is_featured');

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('facilities', 'public');
        }

        Facility::create($data);

        return redirect()->route('admin.content.facilities.index')
            ->with('success', 'Fasilitas berhasil ditambahkan.');
    }

    public function facilityShow(Facility $facility)
    {
        return view('admin.content.facilities.show', compact('facility'));
    }

    public function facilityEdit(Facility $facility)
    {
        return view('admin.content.facilities.edit', compact('facility'));
    }

    public function facilityUpdate(Request $request, Facility $facility)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category' => 'required|in:academic,sports,library,laboratory,dormitory,cafeteria,mosque,other',
            'capacity' => 'nullable|integer|min:1',
            'specifications' => 'nullable|string',
            'features' => 'nullable|array',
            'is_available' => 'boolean',
            'is_featured' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        $data['is_available'] = $request->boolean('is_available', true);
        $data['is_featured'] = $request->boolean('is_featured');

        if ($request->hasFile('image')) {
            if ($facility->image) {
                Storage::disk('public')->delete($facility->image);
            }
            $data['image'] = $request->file('image')->store('facilities', 'public');
        }

        $facility->update($data);

        return redirect()->route('admin.content.facilities.index')
            ->with('success', 'Fasilitas berhasil diperbarui.');
    }

    public function facilityRemoveImage(Facility $facility)
    {
        try {
            if ($facility->image) {
                // Delete the image file
                Storage::disk('public')->delete($facility->image);

                // Update the facility record to remove image reference
                $facility->update(['image' => null]);

                return response()->json([
                    'success' => true,
                    'message' => 'Gambar berhasil dihapus.'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Tidak ada gambar untuk dihapus.'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error removing facility image: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    public function facilityDestroy(Facility $facility)
    {
        try {
            // Delete image if exists
            if ($facility->image) {
                Storage::disk('public')->delete($facility->image);
            }

            // Delete the facility
            $facility->delete();

            // Check if request expects JSON (AJAX)
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Fasilitas berhasil dihapus.'
                ]);
            }

            return redirect()->route('admin.content.facilities.index')
                ->with('success', 'Fasilitas berhasil dihapus.');
        } catch (\Exception $e) {
            \Log::error('Error deleting facility: ' . $e->getMessage());

            // Check if request expects JSON (AJAX)
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal menghapus fasilitas: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('admin.content.facilities.index')
                ->with('error', 'Gagal menghapus fasilitas: ' . $e->getMessage());
        }
    }

    // GALLERY MANAGEMENT
    public function galleryIndex(Request $request)
    {
        $query = Gallery::with('uploader');

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('album')) {
            $query->where('album', $request->album);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $gallery = $query->latest()->paginate(24);

        $stats = [
            'total_images' => Gallery::where('file_type', 'image')->count(),
            'total_videos' => Gallery::where('file_type', 'video')->count(),
            'featured' => Gallery::where('is_featured', true)->count(),
        ];

        $albums = Gallery::select('album')
            ->whereNotNull('album')
            ->distinct()
            ->pluck('album');

        return view('admin.content.gallery.index', compact('gallery', 'stats', 'albums'));
    }

    public function galleryCreate()
    {
        // Get existing albums for select options
        $albums = Gallery::select('album')
            ->whereNotNull('album')
            ->where('album', '!=', '')
            ->distinct()
            ->orderBy('album')
            ->pluck('album');

        return view('admin.content.gallery.create', compact('albums'));
    }

    public function galleryStore(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'file' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,avi,mov|max:10240',
            'category' => 'nullable|string|max:100',
            'album' => 'nullable|string|max:100',
            'is_featured' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->store('gallery', 'public');

        $data = [
            'title' => $request->title,
            'description' => $request->description,
            'file_path' => $filePath,
            'file_name' => $fileName,
            'file_type' => strpos($file->getMimeType(), 'image') !== false ? 'image' : 'video',
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'category' => $request->category,
            'album' => $request->album,
            'is_featured' => $request->boolean('is_featured'),
            'order' => $request->order ?? 0,
            'uploaded_by' => Auth::id(),
        ];

        Gallery::create($data);

        return redirect()->route('admin.content.gallery.index')
            ->with('success', 'File berhasil ditambahkan ke galeri.');
    }

    public function galleryShow(Gallery $gallery)
    {
        return view('admin.content.gallery.show', compact('gallery'));
    }

    public function galleryEdit(Gallery $gallery)
    {
        // Get existing albums for select options
        $albums = Gallery::select('album')
            ->whereNotNull('album')
            ->where('album', '!=', '')
            ->distinct()
            ->orderBy('album')
            ->pluck('album');

        return view('admin.content.gallery.edit', compact('gallery', 'albums'));
    }

    public function galleryUpdate(Request $request, Gallery $gallery)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'album' => 'nullable|string|max:100',
            'is_featured' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        $gallery->update([
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'album' => $request->album,
            'is_featured' => $request->boolean('is_featured'),
            'order' => $request->order ?? 0,
        ]);

        return redirect()->route('admin.content.gallery.index')
            ->with('success', 'Galeri berhasil diperbarui.');
    }

    public function galleryDestroy(Gallery $gallery)
    {
        try {
            // Delete file if exists
            if ($gallery->file_path) {
                Storage::disk('public')->delete($gallery->file_path);
            }

            // Delete the gallery item
            $gallery->delete();

            // Check if request expects JSON (AJAX)
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'File berhasil dihapus dari galeri.'
                ]);
            }

            return redirect()->route('admin.content.gallery.index')
                ->with('success', 'File berhasil dihapus dari galeri.');
        } catch (\Exception $e) {
            \Log::error('Error deleting gallery: ' . $e->getMessage());

            // Check if request expects JSON (AJAX)
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal menghapus galeri: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('admin.content.gallery.index')
                ->with('error', 'Gagal menghapus galeri: ' . $e->getMessage());
        }
    }

    /**
     * Toggle featured status of news
     */
    public function toggleFeatured($id)
    {
        try {
            $news = News::findOrFail($id);

            \Log::info('Toggle Featured Request', [
                'news_id' => $news->id,
                'news_title' => $news->title,
                'old_featured' => $news->is_featured,
                'user_id' => Auth::id()
            ]);

            // Toggle featured status
            $news->is_featured = !$news->is_featured;
            $news->save();

            \Log::info('Toggle Featured Success', [
                'news_id' => $news->id,
                'new_featured' => $news->is_featured,
                'toggled_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'is_featured' => $news->is_featured,
                'message' => $news->is_featured ?
                    "Berita '{$news->title}' berhasil dijadikan featured." :
                    "Berita '{$news->title}' berhasil dihapus dari featured."
            ]);

        } catch (\Exception $e) {
            \Log::error('Toggle Featured Error', [
                'news_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengubah status featured: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if slug is available
     */
    public function checkSlug(Request $request)
    {
        $slug = $request->input('slug');
        $excludeId = $request->input('exclude_id'); // For edit mode

        if (!$slug) {
            return response()->json([
                'available' => false,
                'message' => 'Slug tidak boleh kosong.'
            ]);
        }

        $query = News::where('slug', $slug);

        // Exclude current news when editing
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        $exists = $query->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ?
                'Slug sudah digunakan. Silakan gunakan slug yang lain.' :
                'Slug tersedia untuk digunakan.',
            'slug' => $slug
        ]);
    }
}
