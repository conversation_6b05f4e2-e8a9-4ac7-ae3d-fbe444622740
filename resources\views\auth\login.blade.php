<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Login - {{ config('app.name', '<PERSON><PERSON>lah<PERSON>') }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .login-container {
            width: 100%;
            max-width: 1000px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            overflow: hidden;
            background: white;
            position: relative;
        }

        .login-wrapper {
            display: flex;
            min-height: 600px;
        }

        /* Left Card - Animation */
        .left-card {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .animation-content {
            text-align: center;
            color: white;
            z-index: 2;
            position: relative;
        }

        .animation-content h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease-out;
        }

        .animation-content p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .school-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
        }

        .school-logo i {
            font-size: 3rem;
            color: white;
        }

        /* Floating shapes animation */
        .floating-shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-shape:nth-child(1) {
            top: 20%;
            left: 20%;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }

        .floating-shape:nth-child(3) {
            bottom: 20%;
            left: 30%;
            animation-delay: 4s;
        }

        /* Right Card - Forms */
        .right-card {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .form-container {
            position: absolute;
            width: 100%;
            height: 100%;
            transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.8s ease;
            opacity: 1;
        }

        .form-container.active {
            transform: translateX(0);
            opacity: 1;
            z-index: 2;
        }

        .form-container.slide-out-left {
            transform: translateX(-100%);
            opacity: 0;
            z-index: 1;
        }

        .form-container.slide-in-right {
            transform: translateX(100%);
            opacity: 0;
            z-index: 1;
        }

        .form-container.slide-in-from-right {
            transform: translateX(0);
            opacity: 1;
            z-index: 2;
        }

        .form-content {
            padding: 3rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-header h3 {
            color: #333;
            font-weight: 600;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .form-header p {
            color: #666;
            font-size: 0.95rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 12px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            z-index: 1;
        }

        .form-control.with-icon {
            padding-left: 45px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .form-links {
            text-align: center;
            margin-top: 1.5rem;
        }

        .form-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .form-links a:hover {
            color: #764ba2;
        }

        .form-switch-links {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
        }

        .form-switch-links a {
            color: #999;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .form-switch-links a:hover {
            color: #667eea;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-wrapper {
                flex-direction: column;
            }

            .left-card {
                min-height: 200px;
            }

            .animation-content h2 {
                font-size: 2rem;
            }

            .form-content {
                padding: 2rem;
            }
        }

        /* Alert Styles */
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }

        .alert-warning {
            background: linear-gradient(135deg, #ffd43b, #fab005);
            color: white;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-wrapper">
            <!-- Left Card - Animation -->
            <div class="left-card">
                <div class="floating-shape">
                    <i class="fas fa-graduation-cap fa-3x"></i>
                </div>
                <div class="floating-shape">
                    <i class="fas fa-book fa-2x"></i>
                </div>
                <div class="floating-shape">
                    <i class="fas fa-users fa-2x"></i>
                </div>

                <div class="animation-content">
                    <div class="school-logo">
                        <i class="fas fa-school"></i>
                    </div>
                    <h2>Selamat Datang</h2>
                    <p>Sistem Informasi Sekolah Terpadu<br>Masuk untuk mengakses dashboard Anda</p>
                </div>
            </div>

            <!-- Right Card - Forms -->
            <div class="right-card">
                <!-- Login Form -->
                <div class="form-container active" id="loginForm">
                    <div class="form-content">
                        <div class="form-header">
                            <h3>Masuk ke Akun</h3>
                            <p>Gunakan NISN, NIP, Username, atau Email untuk masuk</p>
                        </div>

                        @if ($errors->any())
                            <div class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <div>{{ $error }}</div>
                                @endforeach
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if (session('warning'))
                            <div class="alert alert-warning">
                                {{ session('warning') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('login') }}">
                            @csrf
                            <div class="form-group">
                                <i class="fas fa-user input-icon"></i>
                                <input type="text"
                                       class="form-control with-icon @error('login') is-invalid @enderror"
                                       name="login"
                                       value="{{ old('login') }}"
                                       placeholder="NISN / NIP / Username / Email"
                                       required
                                       autofocus>
                            </div>

                            <div class="form-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password"
                                       class="form-control with-icon @error('password') is-invalid @enderror"
                                       name="password"
                                       placeholder="Password"
                                       required>
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="remember" id="remember">
                                    <label class="form-check-label" for="remember">
                                        Ingat saya
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Masuk
                            </button>
                        </form>

                        <div class="form-switch-links">
                            <a href="#" onclick="showResetForm()">Lupa Password?</a>
                            <a href="{{ route('landing') }}">Kembali ke Beranda</a>
                        </div>
                    </div>
                </div>



                <!-- Reset Password Form -->
                <div class="form-container slide-in-right" id="resetForm">
                    <div class="form-content">
                        <div class="form-header">
                            <h3>Reset Password</h3>
                            <p>Masukkan email Anda untuk reset password</p>
                        </div>

                        <form method="POST" action="{{ route('password.email') }}">
                            @csrf
                            <div class="form-group">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email"
                                       class="form-control with-icon"
                                       name="email"
                                       placeholder="Email"
                                       required>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Kirim Link Reset
                            </button>
                        </form>

                        <div class="form-switch-links">
                            <a href="#" onclick="showLoginForm()">Kembali ke Login</a>
                            <a href="{{ route('landing') }}">Kembali ke Beranda</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Form switching animations
        function showLoginForm() {
            switchForm('loginForm');
        }

        function showResetForm() {
            switchForm('resetForm');
        }

        function switchForm(targetFormId) {
            const currentForm = document.querySelector('.form-container.active');
            const targetForm = document.getElementById(targetFormId);

            if (currentForm === targetForm) return;

            // Add smooth transition effect
            currentForm.style.transition = 'transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.8s ease';
            targetForm.style.transition = 'transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.8s ease';

            // Phase 1: Slide out current form to left with fade
            currentForm.classList.remove('active');
            currentForm.classList.add('slide-out-left');

            // Phase 2: Prepare target form from right (after small delay for smoother effect)
            setTimeout(() => {
                targetForm.classList.remove('slide-in-right');
                targetForm.classList.add('slide-in-from-right');
                targetForm.classList.add('active');
            }, 200);

            // Phase 3: Reset classes after animation completes
            setTimeout(() => {
                currentForm.classList.remove('slide-out-left');
                currentForm.classList.add('slide-in-right');
                targetForm.classList.remove('slide-in-from-right');
            }, 1400); // Increased timeout for longer animation
        }

        // Enhanced form validation with SweetAlert
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');

            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    // Show loading state
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';
                    submitBtn.disabled = true;

                    // Re-enable button after 3 seconds (in case of validation errors)
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 3000);
                });
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
        });

        // Floating shapes animation enhancement
        function createFloatingShapes() {
            const leftCard = document.querySelector('.left-card');
            const shapes = ['fas fa-star', 'fas fa-heart', 'fas fa-diamond', 'fas fa-circle'];

            setInterval(() => {
                const shape = document.createElement('div');
                shape.className = 'floating-shape';
                shape.innerHTML = `<i class="${shapes[Math.floor(Math.random() * shapes.length)]}"></i>`;
                shape.style.left = Math.random() * 100 + '%';
                shape.style.top = Math.random() * 100 + '%';
                shape.style.animationDuration = (Math.random() * 3 + 3) + 's';

                leftCard.appendChild(shape);

                setTimeout(() => {
                    shape.remove();
                }, 6000);
            }, 2000);
        }

        // Initialize floating shapes
        createFloatingShapes();

        // Smooth scroll and focus effects
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Password strength indicator (for register form)
        const passwordInput = document.querySelector('input[name="password"]');
        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                // You can add visual feedback here
            });
        }

        function calculatePasswordStrength(password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }

        // Show success message with SweetAlert
        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        @endif

        // Show error message with SweetAlert
        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: '{{ session('error') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        // Show warning message with SweetAlert
        @if(session('warning'))
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian!',
                text: '{{ session('warning') }}',
                confirmButtonColor: '#667eea'
            });
        @endif

        // Handle active session error with force login option
        @if($errors->has('login') && str_contains($errors->first('login'), 'sedang aktif'))
            Swal.fire({
                icon: 'warning',
                title: 'Sesi Aktif Terdeteksi',
                html: `
                    <p>{{ $errors->first('login') }}</p>
                    <br>
                    <p><strong>Apakah Anda ingin memaksa login dan mengakhiri sesi lain?</strong></p>
                `,
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Paksa Login',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Create and submit force login form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{{ route('login.force') }}';

                    // Add CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';
                    form.appendChild(csrfToken);

                    // Add login field
                    const loginField = document.createElement('input');
                    loginField.type = 'hidden';
                    loginField.name = 'login';
                    loginField.value = '{{ old('login') }}';
                    form.appendChild(loginField);

                    // Add password field
                    const passwordField = document.createElement('input');
                    passwordField.type = 'hidden';
                    passwordField.name = 'password';
                    passwordField.value = document.querySelector('input[name="password"]').value;
                    form.appendChild(passwordField);

                    // Add remember field if checked
                    const rememberCheckbox = document.querySelector('input[name="remember"]');
                    if (rememberCheckbox && rememberCheckbox.checked) {
                        const rememberField = document.createElement('input');
                        rememberField.type = 'hidden';
                        rememberField.name = 'remember';
                        rememberField.value = '1';
                        form.appendChild(rememberField);
                    }

                    document.body.appendChild(form);
                    form.submit();
                }
            });
        @endif
    </script>
</body>
</html>
