@extends('layouts.landing')

@section('title', 'Status Pendaftaran - PPDB Online')
@section('description', 'Status pendaftaran PPDB Online untuk ' . $registration->full_name)

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="h3 fw-bold">Status Pendaftaran PPDB</h1>
                <p class="text-muted">Informasi lengkap status pendaftaran Anda</p>
            </div>
            
            <!-- Registration Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>Informasi Pendaftaran
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>Nomor Pendaftaran:</strong>
                            <div class="bg-light p-2 rounded mt-1">
                                <code class="text-primary">{{ $registration->registration_number }}</code>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Status:</strong>
                            <div class="mt-1">
                                <span class="badge bg-{{ $registration->status_badge }} fs-6">
                                    {{ $registration->status_label }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Nama Lengkap:</strong>
                            <div class="mt-1">{{ $registration->full_name }}</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Program Pilihan:</strong>
                            <div class="mt-1">{{ $registration->program->name }}</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Tanggal Daftar:</strong>
                            <div class="mt-1">{{ $registration->created_at ? $registration->created_at->format('d F Y, H:i') : '-' }} WIB</div>
                        </div>

                        @if($registration->verified_at)
                        <div class="col-md-6 mb-3">
                            <strong>Tanggal Verifikasi:</strong>
                            <div class="mt-1">{{ $registration->verified_at->format('d F Y, H:i') }} WIB</div>
                        </div>
                        @endif
                    </div>
                    
                    @if($registration->notes)
                    <div class="mt-3 p-3 bg-light rounded">
                        <h6><i class="fas fa-sticky-note me-2 text-warning"></i>Catatan:</h6>
                        <p class="mb-0">{{ $registration->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>
            
            <!-- Status Progress -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>Progress Pendaftaran
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        @php
                        $steps = [
                            ['key' => 'pending', 'title' => 'Pendaftaran Diterima', 'icon' => 'fa-check'],
                            ['key' => 'verified', 'title' => 'Dokumen Diverifikasi', 'icon' => 'fa-file-check'],
                            ['key' => 'approved', 'title' => 'Diterima', 'icon' => 'fa-thumbs-up'],
                            ['key' => 'enrolled', 'title' => 'Terdaftar', 'icon' => 'fa-graduation-cap']
                        ];
                        
                        $currentIndex = array_search($registration->status, array_column($steps, 'key'));
                        if ($registration->status === 'rejected') {
                            $currentIndex = -1;
                        }
                        @endphp
                        
                        @foreach($steps as $index => $step)
                        <div class="col-6 col-md-3 text-center mb-3">
                            <div class="position-relative">
                                <div class="rounded-circle d-inline-flex align-items-center justify-content-center mb-2
                                    @if($index <= $currentIndex) bg-success text-white
                                    @elseif($registration->status === 'rejected' && $index === 1) bg-danger text-white
                                    @else bg-light text-muted
                                    @endif" 
                                    style="width: 50px; height: 50px;">
                                    <i class="fas {{ $step['icon'] }}"></i>
                                </div>
                                <h6 class="small">{{ $step['title'] }}</h6>
                            </div>
                        </div>
                        @endforeach
                        
                        @if($registration->status === 'rejected')
                        <div class="col-12 text-center">
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>Pendaftaran Ditolak</strong>
                                @if($registration->notes)
                                    <br><small>{{ $registration->notes }}</small>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Documents Status -->
            @if($registration->documents->count() > 0)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-folder-open me-2"></i>Status Dokumen
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        @foreach($registration->documents as $document)
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center justify-content-between p-3 border rounded">
                                <div>
                                    <h6 class="mb-1">{{ $document->document_type_name }}</h6>
                                    <small class="text-muted">{{ $document->document_name }}</small>
                                </div>
                                <span class="badge bg-{{ $document->status_badge }}">
                                    {{ $document->status_label }}
                                </span>
                            </div>
                            @if($document->status === 'rejected' && $document->rejection_reason)
                            <div class="mt-2">
                                <small class="text-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ $document->rejection_reason }}
                                </small>
                            </div>
                            @endif
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
            
            <!-- Test Score -->
            @if($registration->test_score)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Hasil Tes
                    </h5>
                </div>
                <div class="card-body p-4 text-center">
                    <div class="display-4 fw-bold text-success mb-2">{{ $registration->test_score }}</div>
                    <p class="text-muted">Nilai Tes Masuk</p>
                </div>
            </div>
            @endif
            
            <!-- Next Steps -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-arrow-right me-2"></i>Langkah Selanjutnya
                    </h5>
                </div>
                <div class="card-body p-4">
                    @if($registration->status === 'pending')
                        <div class="alert alert-info">
                            <h6><i class="fas fa-clock me-2"></i>Menunggu Verifikasi</h6>
                            <p class="mb-0">Pendaftaran Anda sedang dalam proses verifikasi. Pastikan Anda telah mengupload semua dokumen yang diperlukan melalui Portal Calon Siswa.</p>
                        </div>
                        <a href="{{ route('login') }}" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login ke Portal Calon Siswa
                        </a>
                    @elseif($registration->status === 'verified')
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Dokumen Terverifikasi</h6>
                            <p class="mb-0">Selamat! Dokumen Anda telah diverifikasi. Silakan tunggu jadwal tes masuk atau pengumuman selanjutnya.</p>
                        </div>
                    @elseif($registration->status === 'approved')
                        <div class="alert alert-success">
                            <h6><i class="fas fa-party-horn me-2"></i>Selamat! Anda Diterima</h6>
                            <p class="mb-0">Selamat! Anda telah diterima sebagai siswa baru. Silakan lakukan daftar ulang sesuai jadwal yang ditentukan.</p>
                        </div>
                        <a href="{{ route('login') }}" class="btn btn-success">
                            <i class="fas fa-graduation-cap me-2"></i>Proses Daftar Ulang
                        </a>
                    @elseif($registration->status === 'enrolled')
                        <div class="alert alert-primary">
                            <h6><i class="fas fa-graduation-cap me-2"></i>Terdaftar Sebagai Siswa</h6>
                            <p class="mb-0">Selamat! Anda telah resmi terdaftar sebagai siswa baru. Selamat datang di keluarga besar sekolah kami!</p>
                        </div>
                    @elseif($registration->status === 'rejected')
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times-circle me-2"></i>Pendaftaran Ditolak</h6>
                            <p class="mb-0">Mohon maaf, pendaftaran Anda tidak dapat diproses lebih lanjut. Silakan hubungi kami untuk informasi lebih lanjut.</p>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="text-center">
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    @if(in_array($registration->status, ['pending', 'verified', 'approved']))
                    <a href="{{ route('login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>Login ke Portal
                    </a>
                    @endif
                    
                    <a href="{{ route('ppdb.check-status') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>Cek Status Lain
                    </a>
                    
                    <a href="{{ route('landing') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>Beranda
                    </a>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div class="mt-5">
                <div class="card border-0 bg-light">
                    <div class="card-body p-4 text-center">
                        <h6 class="card-title">
                            <i class="fas fa-headset me-2 text-success"></i>Butuh Bantuan?
                        </h6>
                        <p class="card-text">
                            Hubungi kami di <strong>(021) 123-4567</strong> atau 
                            <strong><EMAIL></strong>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
