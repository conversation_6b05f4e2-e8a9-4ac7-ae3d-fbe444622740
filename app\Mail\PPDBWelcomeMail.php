<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\PPDBRegistration;
use App\Models\User;

class PPDBWelcomeMail extends Mailable
{
    use Queueable, SerializesModels;

    public $registration;
    public $user;
    public $temporaryPassword;

    /**
     * Create a new message instance.
     */
    public function __construct(PPDBRegistration $registration, User $user, $temporaryPassword)
    {
        $this->registration = $registration;
        $this->user = $user;
        $this->temporaryPassword = $temporaryPassword;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Selamat Datang di PPDB Online - Informasi Login Portal Calon Siswa',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.ppdb-welcome',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
