@extends('layouts.dashboard')

@section('title', 'Hero Sections')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Hero Sections</h1>
        <p class="text-muted">Kelola banner utama yang tampil di halaman depan website</p>
    </div>
    <div>
        <a href="{{ route('admin.settings.hero-sections.create') }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-2"></i>Tambah Hero Section
        </a>
        <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Kembali
        </a>
    </div>
</div>

<!-- Hero Sections List -->
<div class="row">
    @if($heroSections->count() > 0)
        @foreach($heroSections as $hero)
        <div class="col-lg-6 mb-4">
            <div class="card hero-card {{ $hero->is_active ? '' : 'inactive' }}">
                <div class="hero-image">
                    @if($hero->image)
                        <img src="{{ Storage::url($hero->image) }}" alt="{{ $hero->title }}" class="card-img-top">
                    @else
                        <div class="image-placeholder">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="text-muted mt-2">Tidak ada gambar</p>
                        </div>
                    @endif
                    
                    <!-- Status Badge -->
                    <div class="status-badge">
                        <span class="badge bg-{{ $hero->is_active ? 'success' : 'secondary' }}">
                            {{ $hero->is_active ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                    
                    <!-- Order Badge -->
                    <div class="order-badge">
                        <span class="badge bg-primary">{{ $hero->order }}</span>
                    </div>
                </div>
                
                <div class="card-body">
                    <h5 class="card-title">{{ $hero->title }}</h5>
                    @if($hero->subtitle)
                        <h6 class="card-subtitle mb-2 text-muted">{{ $hero->subtitle }}</h6>
                    @endif
                    @if($hero->description)
                        <p class="card-text">{{ Str::limit($hero->description, 100) }}</p>
                    @endif
                    
                    @if($hero->button_text && $hero->button_link)
                        <div class="mb-3">
                            <small class="text-muted">Button: </small>
                            <span class="badge bg-info">{{ $hero->button_text }}</span>
                        </div>
                    @endif
                    
                    @if($hero->video_url)
                        <div class="mb-3">
                            <small class="text-muted">Video URL: </small>
                            <a href="{{ $hero->video_url }}" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt"></i> Lihat Video
                            </a>
                        </div>
                    @endif
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" role="group">
                            <a href="{{ route('admin.settings.hero-sections.edit', $hero) }}" class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-{{ $hero->is_active ? 'secondary' : 'success' }} toggle-status" 
                                    data-hero-id="{{ $hero->id }}">
                                <i class="fas fa-{{ $hero->is_active ? 'eye-slash' : 'eye' }}"></i>
                                {{ $hero->is_active ? 'Nonaktifkan' : 'Aktifkan' }}
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-hero" 
                                    data-hero-id="{{ $hero->id }}" 
                                    data-hero-title="{{ $hero->title }}">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </div>
                        
                        <div class="order-controls">
                            @if(!$loop->first)
                                <button type="button" class="btn btn-sm btn-outline-primary move-up" 
                                        data-hero-id="{{ $hero->id }}">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                            @endif
                            @if(!$loop->last)
                                <button type="button" class="btn btn-sm btn-outline-primary move-down" 
                                        data-hero-id="{{ $hero->id }}">
                                    <i class="fas fa-arrow-down"></i>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    @else
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-image fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">Belum ada Hero Section</h4>
                    <p class="text-muted">Tambahkan hero section pertama untuk menampilkan banner di halaman utama website.</p>
                    <a href="{{ route('admin.settings.hero-sections.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tambah Hero Section
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>


@endsection

@push('styles')
<style>
.hero-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.hero-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.hero-card.inactive {
    opacity: 0.7;
    border-color: #dee2e6;
}

.hero-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #dee2e6;
}

.status-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

.order-badge {
    position: absolute;
    top: 10px;
    left: 10px;
}

.order-controls {
    display: flex;
    gap: 5px;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-subtitle {
    font-size: 0.9rem;
}

.card-text {
    font-size: 0.9rem;
    color: #6c757d;
}

.btn-group .btn {
    font-size: 0.8rem;
}

.order-controls .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}
</style>
@endpush

@push('scripts')
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Toggle hero status
    $('.toggle-status').click(function() {
        const heroId = $(this).data('hero-id');
        const button = $(this);
        const isActive = button.hasClass('btn-outline-secondary');
        const action = isActive ? 'nonaktifkan' : 'aktifkan';

        Swal.fire({
            title: `${action.charAt(0).toUpperCase() + action.slice(1)} Hero Section?`,
            text: `Apakah Anda yakin ingin ${action} hero section ini?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#6c757d',
            confirmButtonText: `Ya, ${action.charAt(0).toUpperCase() + action.slice(1)}!`,
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/settings/hero-sections/${heroId}/toggle`,
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error!', 'Terjadi kesalahan saat mengubah status.', 'error');
                        }
                    },
                    error: function(xhr) {
                        Swal.fire('Error!', 'Terjadi kesalahan saat mengubah status.', 'error');
                    }
                });
            }
        });
    });

    // Delete hero section
    $('.delete-hero').click(function() {
        const heroId = $(this).data('hero-id');
        const heroTitle = $(this).data('hero-title');

        Swal.fire({
            title: 'Hapus Hero Section?',
            text: `Apakah Anda yakin ingin menghapus hero section "${heroTitle}"? Tindakan ini tidak dapat dibatalkan.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // Create form and submit
                const form = $('<form>', {
                    'method': 'POST',
                    'action': `/admin/settings/hero-sections/${heroId}`
                });

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_token',
                    'value': '{{ csrf_token() }}'
                }));

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_method',
                    'value': 'DELETE'
                }));

                $('body').append(form);
                form.submit();
            }
        });
    });

    // Move hero up/down
    $('.move-up, .move-down').click(function() {
        const heroId = $(this).data('hero-id');
        const direction = $(this).hasClass('move-up') ? 'up' : 'down';

        Swal.fire({
            title: 'Info',
            text: 'Fitur pengurutan hero section akan segera tersedia.',
            icon: 'info',
            confirmButtonText: 'OK'
        });
    });
});
</script>
@endpush
