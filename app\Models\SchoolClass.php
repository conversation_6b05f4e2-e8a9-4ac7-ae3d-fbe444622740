<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SchoolClass extends Model
{
    protected $table = 'classes';

    protected $fillable = [
        'name',
        'level',
        'major',
        'class_number',
        'academic_year',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get students in this class
     */
    public function students()
    {
        return $this->hasMany(User::class, 'class_id');
    }

    /**
     * Scope for active classes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for current academic year
     */
    public function scopeCurrentYear($query)
    {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $academicYear = $currentYear . '/' . $nextYear;

        return $query->where('academic_year', $academicYear);
    }

    /**
     * Get formatted class name
     */
    public function getFormattedNameAttribute(): string
    {
        if ($this->major) {
            return "{$this->level} {$this->major} {$this->class_number}";
        }

        return "{$this->level} {$this->class_number}";
    }
}
