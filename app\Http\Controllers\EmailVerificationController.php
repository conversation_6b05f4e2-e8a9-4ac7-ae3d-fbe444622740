<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Mail\EmailVerificationMail;

class EmailVerificationController extends Controller
{
    /**
     * Verify email with token
     */
    public function verify(Request $request, $id, $token)
    {
        try {
            $user = User::findOrFail($id);

            // Check if email is already verified
            if ($user->hasVerifiedEmail()) {
                return view('auth.email-verification-result', [
                    'success' => true,
                    'message' => 'Email Anda sudah terverifikasi sebelumnya.',
                    'user' => $user
                ]);
            }

            // Verify the token
            if ($user->verifyEmail($token)) {
                \Log::info('Email verified successfully for user: ' . $user->email);

                return view('auth.email-verification-result', [
                    'success' => true,
                    'message' => 'Email berhasil diverifikasi! Anda sekarang dapat login ke sistem.',
                    'user' => $user
                ]);
            } else {
                \Log::warning('Email verification failed for user: ' . $user->email . ' with token: ' . $token);

                return view('auth.email-verification-result', [
                    'success' => false,
                    'message' => 'Link verifikasi tidak valid atau sudah kedaluwarsa.',
                    'user' => $user,
                    'can_resend' => $user->canResendEmailVerification()
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Email verification error: ' . $e->getMessage());

            return view('auth.email-verification-result', [
                'success' => false,
                'message' => 'Terjadi kesalahan saat memverifikasi email.',
                'user' => null
            ]);
        }
    }

    /**
     * Resend verification email
     */
    public function resend(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:users,email'
        ]);

        try {
            $user = User::where('email', $request->email)->first();

            // Check if email is already verified
            if ($user->hasVerifiedEmail()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email sudah terverifikasi.'
                ]);
            }

            // Check if can resend
            if (!$user->canResendEmailVerification()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tunggu 5 menit sebelum mengirim ulang email verifikasi.'
                ]);
            }

            // Generate new token and send email
            $token = $user->generateEmailVerificationToken();
            Mail::to($user->email)->send(new EmailVerificationMail($user, $token));

            \Log::info('Email verification resent to: ' . $user->email);

            return response()->json([
                'success' => true,
                'message' => 'Email verifikasi telah dikirim ulang. Silakan cek inbox Anda.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Email verification resend error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengirim email verifikasi.'
            ], 500);
        }
    }

    /**
     * Show verification notice
     */
    public function notice()
    {
        return view('auth.verify-email');
    }
}
