@extends('layouts.dashboard')

@section('title', 'Detail Fasilitas - ' . $facility->name)

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Detail Fasilitas</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="#">Manajemen Konten</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.content.facilities.index') }}">Fasilitas</a></li>
                    <li class="breadcrumb-item active">{{ $facility->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.content.facilities.edit', $facility) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-2"></i>Edit Fasilitas
            </a>
            <a href="{{ route('admin.content.facilities.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Facility Details -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>{{ $facility->name }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($facility->image)
                        <div class="mb-4">
                            <img src="{{ Storage::url($facility->image) }}" 
                                 alt="{{ $facility->name }}" 
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 400px; width: 100%; object-fit: cover;">
                        </div>
                    @endif

                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Deskripsi Fasilitas</h6>
                        <div class="content-text">
                            {!! nl2br(e($facility->description)) !!}
                        </div>
                    </div>

                    @if($facility->specifications)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Spesifikasi</h6>
                            <div class="content-text">
                                {!! nl2br(e($facility->specifications)) !!}
                            </div>
                        </div>
                    @endif

                    @if($facility->features && is_array($facility->features) && count($facility->features) > 0)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Fitur-fitur</h6>
                            <div class="row">
                                @foreach($facility->features as $feature)
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>{{ $feature }}</span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @elseif($facility->features && is_string($facility->features) && !empty($facility->features))
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Fitur-fitur</h6>
                            <div class="content-text">
                                {!! nl2br(e($facility->features)) !!}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Facility Info -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informasi Fasilitas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Nama Fasilitas</small>
                        <div class="fw-bold">{{ $facility->name }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Kategori</small>
                        <div class="fw-bold">
                            @switch($facility->category)
                                @case('academic')
                                    <span class="badge bg-primary">Akademik</span>
                                    @break
                                @case('sports')
                                    <span class="badge bg-success">Olahraga</span>
                                    @break
                                @case('library')
                                    <span class="badge bg-info">Perpustakaan</span>
                                    @break
                                @case('laboratory')
                                    <span class="badge bg-warning">Laboratorium</span>
                                    @break
                                @case('dormitory')
                                    <span class="badge bg-secondary">Asrama</span>
                                    @break
                                @case('cafeteria')
                                    <span class="badge bg-danger">Kantin</span>
                                    @break
                                @case('mosque')
                                    <span class="badge bg-dark">Masjid</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">Lainnya</span>
                            @endswitch
                        </div>
                    </div>

                    @if($facility->capacity)
                        <div class="mb-3">
                            <small class="text-muted">Kapasitas</small>
                            <div class="fw-bold">{{ number_format($facility->capacity) }} orang</div>
                        </div>
                    @endif

                    <div class="mb-3">
                        <small class="text-muted">Status Ketersediaan</small>
                        <div>
                            @if($facility->is_available)
                                <span class="badge bg-success">Tersedia</span>
                            @else
                                <span class="badge bg-danger">Tidak Tersedia</span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Featured</small>
                        <div>
                            @if($facility->is_featured)
                                <span class="badge bg-warning text-dark">Ya</span>
                            @else
                                <span class="badge bg-secondary">Tidak</span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Dibuat</small>
                        <div class="fw-bold">{{ $facility->created_at->format('d M Y, H:i') }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Terakhir Diperbarui</small>
                        <div class="fw-bold">{{ $facility->updated_at->format('d M Y, H:i') }}</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Aksi
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.content.facilities.edit', $facility) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit Fasilitas
                        </a>
                        <button type="button" class="btn btn-danger" onclick="deleteFacility('{{ $facility->slug }}', '{{ addslashes($facility->name) }}')">
                            <i class="fas fa-trash me-2"></i>Hapus Fasilitas
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.content-text {
    line-height: 1.6;
    color: #495057;
}

.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteFacility(slug, name) {
    Swal.fire({
        title: 'Hapus Fasilitas?',
        text: `Apakah Anda yakin ingin menghapus fasilitas "${name}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus fasilitas',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send AJAX DELETE request
            $.ajax({
                url: `/admin/content/facilities/${slug}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Fasilitas berhasil dihapus.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = '/admin/content/facilities';
                    });
                },
                error: function(xhr) {
                    console.error('Delete error:', xhr);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus fasilitas: ' + (xhr.responseJSON?.message || 'Unknown error'),
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>
@endpush
