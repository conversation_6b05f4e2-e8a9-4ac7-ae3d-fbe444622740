<!DOCTYPE html>
<html>
<head>
    <title>Isolated Test</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <h1>Isolated Form Test</h1>
    <p>User: {{ auth()->check() ? auth()->user()->name : 'Not authenticated' }}</p>
    <p>Session ID: {{ session()->getId() }}</p>
    <p>CSRF Token: {{ csrf_token() }}</p>
    
    <form id="isolatedForm" action="{{ route('admin.content.news.ultra-simple') }}" method="POST">
        @csrf
        <div>
            <label>Title:</label>
            <input type="text" name="title" value="Isolated Test" required>
        </div>
        <div>
            <label>Slug:</label>
            <input type="text" name="slug" value="isolated-test" required>
        </div>
        <div>
            <label>Content:</label>
            <textarea name="content">Isolated test content</textarea>
        </div>
        <div>
            <label>Type:</label>
            <select name="type">
                <option value="news">News</option>
                <option value="announcement">Announcement</option>
            </select>
        </div>
        <div>
            <label>Status:</label>
            <select name="status">
                <option value="draft">Draft</option>
                <option value="published">Published</option>
            </select>
        </div>
        <div>
            <button type="submit">Submit Isolated Test</button>
        </div>
    </form>

    <div id="result"></div>

    <script>
        $('#isolatedForm').on('submit', function(e) {
            e.preventDefault();
            
            console.log('=== ISOLATED FORM SUBMIT ===');
            console.log('Form action:', this.action);
            console.log('Current URL before AJAX:', window.location.href);
            
            $.ajax({
                url: this.action,
                method: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('=== ISOLATED SUCCESS ===', response);
                    console.log('Current URL after success:', window.location.href);
                    
                    $('#result').html('<div style="color: green;">SUCCESS: ' + JSON.stringify(response) + '</div>');
                    
                    // Don't redirect, just show success
                    alert('SUCCESS: ' + response.message);
                },
                error: function(xhr, status, error) {
                    console.error('=== ISOLATED ERROR ===', xhr, status, error);
                    console.log('Current URL after error:', window.location.href);
                    
                    $('#result').html('<div style="color: red;">ERROR: ' + xhr.status + ' - ' + xhr.responseText + '</div>');
                    
                    alert('ERROR: ' + xhr.status + ' - ' + error);
                }
            });
        });

        // Monitor URL changes
        let currentUrl = window.location.href;
        setInterval(() => {
            if (window.location.href !== currentUrl) {
                console.log('=== ISOLATED URL CHANGED ===');
                console.log('From:', currentUrl);
                console.log('To:', window.location.href);
                alert('URL CHANGED! From: ' + currentUrl + ' To: ' + window.location.href);
                currentUrl = window.location.href;
            }
        }, 500);
    </script>
</body>
</html>
