@extends('layouts.dashboard')

@section('title', 'Edit Program - ' . $program->name)

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Program</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="#">Manajemen Konten</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.content.programs.index') }}">Program Pendidikan</a></li>
                    <li class="breadcrumb-item active">Edit Program</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.content.programs.show', $program) }}" class="btn btn-info me-2">
                <i class="fas fa-eye me-2"></i>Lihat Program
            </a>
            <a href="{{ route('admin.content.programs.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Form Edit Program
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.content.programs.update', $program) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="name" class="form-label">Nama Program <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $program->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required>{{ old('description', $program->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="level" class="form-label">Level <span class="text-danger">*</span></label>
                                    <select class="form-select @error('level') is-invalid @enderror" id="level" name="level" required>
                                        <option value="">Pilih Level</option>
                                        <option value="TK" {{ old('level', $program->level) == 'TK' ? 'selected' : '' }}>TK</option>
                                        <option value="SD" {{ old('level', $program->level) == 'SD' ? 'selected' : '' }}>SD</option>
                                        <option value="SMP" {{ old('level', $program->level) == 'SMP' ? 'selected' : '' }}>SMP</option>
                                        <option value="SMA" {{ old('level', $program->level) == 'SMA' ? 'selected' : '' }}>SMA</option>
                                        <option value="SMK" {{ old('level', $program->level) == 'SMK' ? 'selected' : '' }}>SMK</option>
                                    </select>
                                    @error('level')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Tipe Program <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                        <option value="">Pilih Tipe</option>
                                        <option value="regular" {{ old('type', $program->type) == 'regular' ? 'selected' : '' }}>Regular</option>
                                        <option value="unggulan" {{ old('type', $program->type) == 'unggulan' ? 'selected' : '' }}>Unggulan</option>
                                        <option value="internasional" {{ old('type', $program->type) == 'internasional' ? 'selected' : '' }}>Internasional</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="duration_years" class="form-label">Durasi (Tahun) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('duration_years') is-invalid @enderror"
                                           id="duration_years" name="duration_years"
                                           value="{{ old('duration_years', $program->duration_years) }}"
                                           min="1" max="6" required>
                                    @error('duration_years')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="curriculum" class="form-label">Kurikulum</label>
                            <textarea class="form-control @error('curriculum') is-invalid @enderror" 
                                      id="curriculum" name="curriculum" rows="4">{{ old('curriculum', $program->curriculum) }}</textarea>
                            @error('curriculum')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="requirements" class="form-label">Persyaratan</label>
                            <textarea class="form-control @error('requirements') is-invalid @enderror"
                                      id="requirements" name="requirements" rows="4">{{ old('requirements', $program->requirements) }}</textarea>
                            @error('requirements')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Kapasitas Siswa</label>
                                    <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                           id="capacity" name="capacity"
                                           value="{{ old('capacity', $program->capacity) }}"
                                           min="1" placeholder="Contoh: 30">
                                    @error('capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="current_students" class="form-label">Siswa Saat Ini</label>
                                    <input type="number" class="form-control @error('current_students') is-invalid @enderror"
                                           id="current_students" name="current_students"
                                           value="{{ old('current_students', $program->current_students) }}"
                                           min="0" placeholder="Contoh: 25">
                                    @error('current_students')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="fee" class="form-label">Biaya Program (Rp)</label>
                                    <input type="number" class="form-control @error('fee') is-invalid @enderror"
                                           id="fee" name="fee"
                                           value="{{ old('fee', $program->fee) }}"
                                           min="0" step="1000" placeholder="Contoh: 500000">
                                    @error('fee')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Gambar Program</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Format: JPG, PNG, GIF. Maksimal 2MB.</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', $program->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Program Aktif
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('admin.content.programs.index') }}" class="btn btn-secondary me-2">Batal</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Program
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current Image Preview -->
        <div class="col-lg-4">
            @if($program->image)
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-image me-2"></i>Gambar Saat Ini
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="position-relative d-inline-block">
                            <img src="{{ Storage::url($program->image) }}" 
                                 alt="{{ $program->name }}" 
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 200px;">
                            <button type="button" 
                                    class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                                    onclick="removeImage({{ $program->id }})"
                                    title="Hapus Gambar">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Klik X untuk menghapus gambar</small>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Help Card -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Bantuan
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="mb-0 ps-3">
                            <li>Nama program harus unik dan deskriptif</li>
                            <li>Deskripsi sebaiknya menjelaskan tujuan program</li>
                            <li>Durasi dan level membantu calon siswa memahami program</li>
                            <li>Kurikulum berisi mata pelajaran yang diajarkan</li>
                            <li>Persyaratan berisi syarat masuk program</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function removeImage(programId) {
    Swal.fire({
        title: 'Hapus Gambar?',
        text: 'Apakah Anda yakin ingin menghapus gambar ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus gambar',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // AJAX call to remove image
            $.ajax({
                url: `/admin/content/programs/${programId}/remove-image`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });

                        // Remove the current image section
                        $('.card:has(img)').fadeOut(300, function() {
                            $(this).remove();
                        });
                    } else {
                        Swal.fire({
                            title: 'Gagal!',
                            text: response.message,
                            icon: 'error'
                        });
                    }
                },
                error: function(xhr) {
                    let message = 'Terjadi kesalahan saat menghapus gambar.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        title: 'Error!',
                        text: message,
                        icon: 'error'
                    });
                }
            });
        }
    });
}
</script>
@endpush
