/* Placeholder Images CSS */
.placeholder-container {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.placeholder-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

/* Principal Photo Placeholder */
.placeholder-principal {
    width: 500px;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 18px;
    font-weight: 600;
}

/* Facility Placeholder */
.placeholder-facility {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 16px;
    font-weight: 600;
    position: absolute;
    top: 0;
    left: 0;
}

/* News Placeholder */
.placeholder-news {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 16px;
    font-weight: 600;
}

.placeholder-news.ppdb {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    color: #000;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
}

/* Gallery Placeholder */
.placeholder-gallery {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 14px;
    font-weight: 600;
}

/* Icon Styles */
.placeholder-icon {
    margin-bottom: 12px;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .placeholder-principal {
        width: 100% !important;
        height: 250px !important;
        font-size: 16px;
    }
    
    .placeholder-facility,
    .placeholder-news,
    .placeholder-gallery {
        font-size: 14px;
    }
    
    .placeholder-icon {
        margin-bottom: 8px;
    }
}

@media (max-width: 576px) {
    .placeholder-principal {
        height: 200px !important;
        font-size: 14px;
    }
    
    .placeholder-facility,
    .placeholder-news,
    .placeholder-gallery {
        font-size: 12px;
    }
}

/* Animation Effects */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.placeholder-container {
    animation: fadeIn 0.6s ease-out;
}

/* Loading State */
.placeholder-loading {
    position: relative;
    overflow: hidden;
}

.placeholder-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
