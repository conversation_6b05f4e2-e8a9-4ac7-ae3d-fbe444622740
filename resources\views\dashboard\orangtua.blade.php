@extends('layouts.dashboard')

@section('title', 'Portal Orang Tua')
@section('page-title', 'Portal Orang Tua - Selamat Datang di Portal Orang Tua')

@section('sidebar-menu')
<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" href="{{ route('profile.show') }}">
            <i class="fas fa-user-circle"></i>
            Profil Saya
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-child"></i>
            Data Anak
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-chart-line"></i>
            Nilai & Rapor Anak
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-calendar-check"></i>
            Kehadiran Anak
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-tasks"></i>
            Tugas & Ujian
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-calendar-alt"></i>
            Jadwal Pelajaran
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-newspaper"></i>
            Berita & Pengumuman
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-comments"></i>
            Komunikasi Guru
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="{{ route('landing') }}" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-success border-0" style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <div>
                    <h5 class="mb-1">👨‍👩‍👧‍👦 Selamat Datang di Portal Orang Tua!</h5>
                    <p class="mb-0">
                        Halo, <strong>{{ Auth::user()->name }}</strong>!
                        Anda memiliki {{ $stats['my_children'] }} anak yang terdaftar di sekolah.
                        <br><small>Pantau perkembangan akademik anak dan berkomunikasi dengan guru melalui portal ini.</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Children Cards -->
@if(count($children) > 0)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-child me-2"></i>Data Anak
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($children as $child)
                    <div class="col-md-6 mb-3">
                        <div class="card border">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    @if($child->avatar)
                                        <img src="{{ asset('storage/' . $child->avatar) }}" alt="Avatar" class="rounded-circle me-3" width="60" height="60" style="object-fit: cover;">
                                    @else
                                        <div class="rounded-circle bg-gradient-info d-inline-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    @endif
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ $child->name }}</h6>
                                        <small class="text-muted">NISN: {{ $child->nisn ?? '-' }}</small><br>
                                        @if($child->studentProfile)
                                            <small class="text-muted">Kelas: {{ $child->studentProfile->class ?? '-' }}</small>
                                        @endif
                                        <br>
                                        <span class="badge bg-{{ $child->is_active ? 'success' : 'danger' }} mt-1">
                                            {{ $child->is_active ? 'Aktif' : 'Nonaktif' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@else
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Belum ada data anak yang terdaftar. Silakan hubungi administrator sekolah untuk menghubungkan akun anak Anda.
        </div>
    </div>
</div>
@endif

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-child"></i>
            </div>
            <div class="stats-number">{{ $stats['my_children'] }}</div>
            <div class="stats-label">Anak Saya</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="stats-number">{{ $stats['unread_messages'] }}</div>
            <div class="stats-label">Pesan Belum Dibaca</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stats-number">{{ $stats['upcoming_events'] }}</div>
            <div class="stats-label">Event Mendatang</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="stats-number">{{ $stats['pending_payments'] }}</div>
            <div class="stats-label">Pembayaran Pending</div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-newspaper me-2"></i>Berita & Pengumuman Terbaru
            </div>
            <div class="card-body">
                @forelse($latestNews as $news)
                    <div class="d-flex align-items-start mb-3">
                        @if($news->featured_image)
                            <img src="{{ asset('storage/' . $news->featured_image) }}" alt="News" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                        @else
                            <div class="rounded bg-gradient-primary d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-newspaper text-white"></i>
                            </div>
                        @endif
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ Str::limit($news->title, 60) }}</h6>
                            <p class="text-muted small mb-2">{{ Str::limit($news->excerpt, 100) }}</p>
                            <small class="text-muted">
                                {{ $news->created_at->diffForHumans() }}
                            </small>
                            <span class="badge bg-{{ $news->type == 'announcement' ? 'warning' : 'info' }} ms-2">
                                {{ $news->type == 'announcement' ? 'Pengumuman' : ucfirst($news->type) }}
                            </span>
                        </div>
                    </div>
                    @if(!$loop->last)<hr>@endif
                @empty
                    <p class="text-muted text-center">Belum ada berita terbaru</p>
                @endforelse
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i>Informasi Penting
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-calendar me-2"></i>Jadwal Penting</h6>
                    <ul class="mb-0">
                        <li>Rapat Orang Tua: 15 Agustus 2024</li>
                        <li>Ujian Tengah Semester: 20-25 Agustus 2024</li>
                        <li>Libur Semester: 1-15 September 2024</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Pengingat</h6>
                    <ul class="mb-0">
                        <li>Pembayaran SPP bulan ini</li>
                        <li>Pengumpulan formulir kesehatan</li>
                        <li>Update data kontak darurat</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Academic Overview for Children -->
@if(count($children) > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>Ringkasan Akademik Anak
            </div>
            <div class="card-body">
                @foreach($children as $child)
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary">{{ $child->name }}</h6>
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                    <h5>-</h5>
                                    <small class="text-muted">Rata-rata Nilai</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                                    <h5>-</h5>
                                    <small class="text-muted">Kehadiran</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-tasks fa-2x text-warning mb-2"></i>
                                    <h5>-</h5>
                                    <small class="text-muted">Tugas Selesai</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-trophy fa-2x text-danger mb-2"></i>
                                    <h5>-</h5>
                                    <small class="text-muted">Prestasi</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @if(!$loop->last)<hr>@endif
                @endforeach
            </div>
        </div>
    </div>
</div>
@endif

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>Aksi Cepat
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-chart-line me-2"></i>Lihat Nilai Anak
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-calendar-check me-2"></i>Cek Kehadiran
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-comments me-2"></i>Hubungi Guru
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-danger w-100">
                            <i class="fas fa-user me-2"></i>Edit Profil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
