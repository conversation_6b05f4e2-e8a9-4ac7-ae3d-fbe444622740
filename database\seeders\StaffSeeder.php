<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\StaffProfile;
use Illuminate\Support\Facades\Hash;

class StaffSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some staff profiles
        $principalUser = User::create([
            'name' => 'Dr. Ahmad W<PERSON>',
            'email' => '<EMAIL>',
            'nip' => '196501011990031001',
            'user_type' => 'guru',
            'phone' => '08123456789',
            'gender' => 'L',
            'password' => Hash::make('principal123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $principalUser->assignRole('guru');

        StaffProfile::create([
            'user_id' => $principalUser->id,
            'employee_id' => 'KS001',
            'position' => 'kepala_sekolah',
            'qualifications' => 'S3 Pendidikan, S2 Manajemen Pendidikan',
            'degree' => 'Dr., S.Pd., M.Pd.',
            'experience' => '25 tahun pengalaman dalam bidang pendidikan',
            'join_date' => '2010-07-01',
            'employment_status' => 'tetap',
            'bio' => 'Kepala sekolah berpengalaman dengan dedikasi tinggi untuk kemajuan pendidikan.',
            'is_featured' => true,
            'order' => 1,
        ]);

        $teacher1 = User::create([
            'name' => 'Dra. Siti Nurhaliza',
            'email' => '<EMAIL>',
            'nip' => '197203151998032001',
            'user_type' => 'guru',
            'phone' => '08123456790',
            'gender' => 'P',
            'password' => Hash::make('teacher123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $teacher1->assignRole('guru');

        StaffProfile::create([
            'user_id' => $teacher1->id,
            'employee_id' => 'GR001',
            'position' => 'guru',
            'subject' => 'Matematika',
            'qualifications' => 'S2 Pendidikan Matematika',
            'degree' => 'Dra., M.Pd.',
            'experience' => '15 tahun mengajar Matematika',
            'join_date' => '2008-08-01',
            'employment_status' => 'tetap',
            'bio' => 'Guru matematika berpengalaman dengan metode pembelajaran inovatif.',
            'is_featured' => true,
            'order' => 2,
        ]);

        $teacher2 = User::create([
            'name' => 'Budi Santoso',
            'email' => '<EMAIL>',
            'nip' => '198005102005011002',
            'user_type' => 'guru',
            'phone' => '08123456791',
            'gender' => 'L',
            'password' => Hash::make('teacher123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $teacher2->assignRole('guru');

        StaffProfile::create([
            'user_id' => $teacher2->id,
            'employee_id' => 'GR002',
            'position' => 'guru',
            'subject' => 'Fisika',
            'qualifications' => 'S2 Fisika',
            'degree' => 'S.Pd., M.Si.',
            'experience' => '12 tahun mengajar Fisika',
            'join_date' => '2012-07-01',
            'employment_status' => 'tetap',
            'bio' => 'Guru fisika yang passionate dalam eksperimen dan penelitian.',
            'is_featured' => true,
            'order' => 3,
        ]);

        $teacher3 = User::create([
            'name' => 'Rina Kartika',
            'email' => '<EMAIL>',
            'nip' => '198507252010012001',
            'user_type' => 'guru',
            'phone' => '08123456792',
            'gender' => 'P',
            'password' => Hash::make('teacher123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $teacher3->assignRole('guru');

        StaffProfile::create([
            'user_id' => $teacher3->id,
            'employee_id' => 'GR003',
            'position' => 'guru',
            'subject' => 'Bahasa Inggris',
            'qualifications' => 'S1 Pendidikan Bahasa Inggris',
            'degree' => 'S.Pd.',
            'experience' => '8 tahun mengajar Bahasa Inggris',
            'join_date' => '2016-07-01',
            'employment_status' => 'tetap',
            'bio' => 'Guru bahasa Inggris dengan sertifikasi internasional TOEFL.',
            'is_featured' => true,
            'order' => 4,
        ]);
    }
}
