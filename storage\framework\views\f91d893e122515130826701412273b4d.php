<?php $__env->startSection('title', 'Portal Guru'); ?>
<?php $__env->startSection('page-title', 'Portal Guru - Selamat Datang di Portal Guru'); ?>

<?php $__env->startSection('sidebar-menu'); ?>
<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo e(request()->routeIs('profile.*') ? 'active' : ''); ?>" href="<?php echo e(route('profile.show')); ?>">
            <i class="fas fa-user-circle"></i>
            Profil <PERSON>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-user-graduate"></i>
            Data Siswa
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-newspaper"></i>
            Berita & Pengumuman
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-book"></i>
            Materi Pembelajaran
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-tasks"></i>
            Tugas & Ujian
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-chart-line"></i>
            Nilai & Rapor
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-calendar-alt"></i>
            Jadwal Mengajar
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('landing')); ?>" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-primary border-0" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-chalkboard-teacher fa-2x"></i>
                </div>
                <div>
                    <h5 class="mb-1">🎓 Selamat Datang di Portal Guru!</h5>
                    <p class="mb-0">
                        Halo, <strong><?php echo e(Auth::user()->name); ?></strong>!
                        <?php if($staffProfile): ?>
                            Anda login sebagai <?php echo e($staffProfile->position == 'kepala_sekolah' ? 'Kepala Sekolah' : 'Guru'); ?>

                            <?php if($staffProfile->subject): ?> <?php echo e($staffProfile->subject); ?><?php endif; ?>.
                        <?php endif; ?>
                        <br><small>Kelola pembelajaran dan berinteraksi dengan siswa melalui portal ini.</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Card -->
<?php if($staffProfile): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-id-card me-2"></i>Profil Tenaga Pendidik
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <?php if(Auth::user()->avatar): ?>
                            <img src="<?php echo e(asset('storage/' . Auth::user()->avatar)); ?>" alt="Avatar" class="rounded-circle mb-3" width="120" height="120" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="rounded-circle bg-gradient-primary d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px;">
                                <i class="fas fa-user fa-3x text-white"></i>
                            </div>
                        <?php endif; ?>
                        <h5><?php echo e(Auth::user()->name); ?></h5>
                        <?php if($staffProfile->degree): ?>
                            <p class="text-primary fw-semibold"><?php echo e($staffProfile->degree); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>NIP:</strong></td>
                                        <td><?php echo e(Auth::user()->nip ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo e(Auth::user()->email); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Telepon:</strong></td>
                                        <td><?php echo e(Auth::user()->phone ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Posisi:</strong></td>
                                        <td>
                                            <?php if($staffProfile->position == 'kepala_sekolah'): ?>
                                                Kepala Sekolah
                                            <?php elseif($staffProfile->position == 'guru'): ?>
                                                Guru
                                            <?php else: ?>
                                                <?php echo e(ucfirst(str_replace('_', ' ', $staffProfile->position))); ?>

                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <?php if($staffProfile->subject): ?>
                                    <tr>
                                        <td><strong>Mata Pelajaran:</strong></td>
                                        <td><span class="badge bg-primary"><?php echo e($staffProfile->subject); ?></span></td>
                                    </tr>
                                    <?php endif; ?>
                                    <tr>
                                        <td><strong>Bergabung:</strong></td>
                                        <td><?php echo e($staffProfile->join_date ? $staffProfile->join_date->format('d M Y') : '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-<?php echo e($staffProfile->employment_status == 'tetap' ? 'success' : 'warning'); ?>">
                                                <?php echo e(ucfirst($staffProfile->employment_status)); ?>

                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Pengalaman:</strong></td>
                                        <td><?php echo e($staffProfile->experience ?? '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['my_students']); ?></div>
            <div class="stats-label">Siswa Saya</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-chalkboard"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['my_classes']); ?></div>
            <div class="stats-label">Kelas Saya</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['pending_assignments']); ?></div>
            <div class="stats-label">Tugas Pending</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-book"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['my_subjects']); ?></div>
            <div class="stats-label">Mata Pelajaran</div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-newspaper me-2"></i>Berita Saya
            </div>
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $myNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="d-flex align-items-start mb-3">
                        <?php if($news->featured_image): ?>
                            <img src="<?php echo e(asset('storage/' . $news->featured_image)); ?>" alt="News" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="rounded bg-gradient-primary d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-newspaper text-white"></i>
                            </div>
                        <?php endif; ?>
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo e(Str::limit($news->title, 50)); ?></h6>
                            <small class="text-muted">
                                <?php echo e($news->created_at->diffForHumans()); ?>

                            </small>
                            <br>
                            <span class="badge bg-<?php echo e($news->is_published ? 'success' : 'warning'); ?>">
                                <?php echo e($news->is_published ? 'Published' : 'Draft'); ?>

                            </span>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-muted text-center">Belum ada berita yang Anda buat</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-graduate me-2"></i>Siswa Terbaru
            </div>
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $recentStudents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="d-flex align-items-center mb-3">
                        <?php if($student->avatar): ?>
                            <img src="<?php echo e(asset('storage/' . $student->avatar)); ?>" alt="Avatar" class="rounded-circle me-3" width="40" height="40">
                        <?php else: ?>
                            <div class="rounded-circle bg-gradient-success d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold"><?php echo e(strtoupper(substr($student->name, 0, 1))); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo e($student->name); ?></h6>
                            <small class="text-muted">
                                NISN: <?php echo e($student->nisn ?? '-'); ?> • <?php echo e($student->created_at->diffForHumans()); ?>

                            </small>
                        </div>
                        <span class="badge bg-<?php echo e($student->is_active ? 'success' : 'danger'); ?>">
                            <?php echo e($student->is_active ? 'Aktif' : 'Nonaktif'); ?>

                        </span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-muted text-center">Belum ada siswa terbaru</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>Aksi Cepat
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-newspaper me-2"></i>Buat Berita
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-book me-2"></i>Upload Materi
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-tasks me-2"></i>Buat Tugas
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-danger w-100">
                            <i class="fas fa-user me-2"></i>Edit Profil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/dashboard/guru.blade.php ENDPATH**/ ?>