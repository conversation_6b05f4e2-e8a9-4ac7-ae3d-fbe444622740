<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StudentProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'student_id',
        'class',
        'admission_year',
        'parent_id',
        'previous_school',
        'address',
        'blood_type',
        'health_info',
        'emergency_contact',
        'achievements',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'achievements' => 'array',
            'admission_year' => 'integer',
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function parent()
    {
        return $this->belongsTo(ParentProfile::class, 'parent_id');
    }
}
