@extends('layouts.dashboard')

@section('title', 'Pengaturan Sistem')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Pengaturan Sistem</h1>
        <p class="text-muted">Kelola konfigurasi sistem dan aplikasi</p>
    </div>
    <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Kembali
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Konfigurasi Aplikasi</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.settings.system.update') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="app_name" class="form-label"><PERSON>a Aplik<PERSON> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('app_name') is-invalid @enderror" 
                                   id="app_name" name="app_name" 
                                   value="{{ old('app_name', config('app.name')) }}" required>
                            @error('app_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="app_url" class="form-label">URL Aplikasi <span class="text-danger">*</span></label>
                            <input type="url" class="form-control @error('app_url') is-invalid @enderror" 
                                   id="app_url" name="app_url" 
                                   value="{{ old('app_url', config('app.url')) }}" required>
                            @error('app_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="timezone" class="form-label">Timezone <span class="text-danger">*</span></label>
                            <select class="form-select @error('timezone') is-invalid @enderror" 
                                    id="timezone" name="timezone" required>
                                <option value="Asia/Jakarta" {{ old('timezone', config('app.timezone')) == 'Asia/Jakarta' ? 'selected' : '' }}>Asia/Jakarta (WIB)</option>
                                <option value="Asia/Makassar" {{ old('timezone', config('app.timezone')) == 'Asia/Makassar' ? 'selected' : '' }}>Asia/Makassar (WITA)</option>
                                <option value="Asia/Jayapura" {{ old('timezone', config('app.timezone')) == 'Asia/Jayapura' ? 'selected' : '' }}>Asia/Jayapura (WIT)</option>
                            </select>
                            @error('timezone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="locale" class="form-label">Bahasa</label>
                            <select class="form-select" id="locale" name="locale">
                                <option value="id" {{ old('locale', config('app.locale')) == 'id' ? 'selected' : '' }}>Bahasa Indonesia</option>
                                <option value="en" {{ old('locale', config('app.locale')) == 'en' ? 'selected' : '' }}>English</option>
                            </select>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">Pengaturan Fitur</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" value="1"
                                       {{ old('maintenance_mode', $schoolSetting->maintenance_mode ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="maintenance_mode">
                                    Mode Maintenance
                                </label>
                            </div>
                            <small class="text-muted">Aktifkan untuk menutup sementara akses ke website</small>
                        </div>

                        <div class="col-12 mb-3" id="maintenance_message_group" style="display: {{ old('maintenance_mode', $schoolSetting->maintenance_mode ?? false) ? 'block' : 'none' }};">
                            <label for="maintenance_message" class="form-label">Pesan Maintenance</label>
                            <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="3"
                                      placeholder="Pesan yang akan ditampilkan saat website dalam mode maintenance">{{ old('maintenance_message', $schoolSetting->maintenance_message ?? '') }}</textarea>
                            <small class="text-muted">Kosongkan untuk menggunakan pesan default</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="registration_enabled" name="registration_enabled" value="1" 
                                       {{ old('registration_enabled', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="registration_enabled">
                                    Registrasi User Baru
                                </label>
                            </div>
                            <small class="text-muted">Izinkan pendaftaran user baru</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_verification_required" name="email_verification_required" value="1" 
                                       {{ old('email_verification_required', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_verification_required">
                                    Verifikasi Email Wajib
                                </label>
                            </div>
                            <small class="text-muted">User harus verifikasi email sebelum dapat login</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="debug_mode" name="debug_mode" value="1" 
                                       {{ old('debug_mode', config('app.debug', false)) ? 'checked' : '' }}>
                                <label class="form-check-label" for="debug_mode">
                                    Debug Mode
                                </label>
                            </div>
                            <small class="text-muted">Tampilkan error detail (hanya untuk development)</small>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">Pengaturan Keamanan</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="session_lifetime" class="form-label">Durasi Session (menit)</label>
                            <input type="number" class="form-control" 
                                   id="session_lifetime" name="session_lifetime" 
                                   value="{{ old('session_lifetime', config('session.lifetime', 120)) }}" 
                                   min="30" max="1440">
                            <small class="text-muted">Berapa lama user tetap login tanpa aktivitas</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="max_login_attempts" class="form-label">Maksimal Percobaan Login</label>
                            <input type="number" class="form-control" 
                                   id="max_login_attempts" name="max_login_attempts" 
                                   value="{{ old('max_login_attempts', 5) }}" 
                                   min="3" max="10">
                            <small class="text-muted">Jumlah percobaan login sebelum akun dikunci</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="force_https" name="force_https" value="1" 
                                       {{ old('force_https', false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="force_https">
                                    Paksa HTTPS
                                </label>
                            </div>
                            <small class="text-muted">Redirect semua request ke HTTPS</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="log_user_activity" name="log_user_activity" value="1" 
                                       {{ old('log_user_activity', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="log_user_activity">
                                    Log Aktivitas User
                                </label>
                            </div>
                            <small class="text-muted">Catat semua aktivitas user untuk keamanan</small>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Simpan Pengaturan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- System Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Sistem</h5>
            </div>
            <div class="card-body">
                <div class="system-info">
                    <div class="info-item">
                        <strong>Laravel Version:</strong>
                        <span class="badge bg-success">{{ app()->version() }}</span>
                    </div>
                    <div class="info-item">
                        <strong>PHP Version:</strong>
                        <span class="badge bg-info">{{ PHP_VERSION }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Environment:</strong>
                        <span class="badge bg-{{ config('app.env') == 'production' ? 'danger' : 'warning' }}">
                            {{ strtoupper(config('app.env')) }}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>Debug Mode:</strong>
                        <span class="badge bg-{{ config('app.debug') ? 'warning' : 'success' }}">
                            {{ config('app.debug') ? 'ON' : 'OFF' }}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>Timezone:</strong>
                        <span>{{ config('app.timezone') }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Current Time:</strong>
                        <span>{{ now()->format('d M Y H:i:s T') }} ({{ now()->format('l') }})</span>
                    </div>
                    <div class="info-item">
                        <strong>Server Time:</strong>
                        <span>{{ \Carbon\Carbon::now()->setTimezone('UTC')->format('d M Y H:i:s T') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cache Management -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Cache Management</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-warning cache-action" data-action="config">
                        <i class="fas fa-sync me-2"></i>Clear Config Cache
                    </button>
                    <button type="button" class="btn btn-outline-warning cache-action" data-action="route">
                        <i class="fas fa-sync me-2"></i>Clear Route Cache
                    </button>
                    <button type="button" class="btn btn-outline-warning cache-action" data-action="view">
                        <i class="fas fa-sync me-2"></i>Clear View Cache
                    </button>
                    <button type="button" class="btn btn-outline-info cache-action" data-action="application">
                        <i class="fas fa-database me-2"></i>Clear Application Cache
                    </button>
                    <button type="button" class="btn btn-outline-danger cache-action" data-action="all">
                        <i class="fas fa-trash me-2"></i>Clear All Cache
                    </button>
                    <button type="button" class="btn btn-outline-success" id="optimizeBtn" onclick="optimizeApplication()">
                        <i class="fas fa-rocket me-2"></i>Optimize Application
                    </button>
                </div>
            </div>
        </div>

        <!-- Backup & Maintenance -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Backup & Maintenance</h5>
                <div class="maintenance-status">
                    @if($schoolSetting && $schoolSetting->maintenance_mode)
                        <span class="badge bg-warning">
                            <i class="fas fa-tools me-1"></i>Mode Maintenance Aktif
                        </span>
                    @else
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>Website Normal
                        </span>
                    @endif
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="createBackup()">
                                <i class="fas fa-download me-2"></i>Backup Database
                            </button>
                            <button type="button" class="btn btn-outline-info cache-action" data-action="all">
                                <i class="fas fa-broom me-2"></i>Clear All Cache
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="runMaintenance('basic')">
                                <i class="fas fa-tools me-2"></i>Basic Maintenance
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="runMaintenance('full')">
                                <i class="fas fa-cogs me-2"></i>Full Maintenance
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn {{ ($schoolSetting && $schoolSetting->maintenance_mode) ? 'btn-success' : 'btn-warning' }}"
                                    id="toggleMaintenanceBtn" onclick="toggleMaintenance()">
                                <i class="fas fa-{{ ($schoolSetting && $schoolSetting->maintenance_mode) ? 'play' : 'pause' }} me-2"></i>
                                {{ ($schoolSetting && $schoolSetting->maintenance_mode) ? 'Nonaktifkan' : 'Aktifkan' }} Maintenance
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.open('{{ route('maintenance') }}', '_blank')">
                                <i class="fas fa-eye me-2"></i>Preview Maintenance Page
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Backup List Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="mb-3">
                            <i class="fas fa-archive me-2"></i>Daftar Backup
                            <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="loadBackupList()">
                                <i class="fas fa-sync me-1"></i>Refresh
                            </button>
                        </h6>
                        <div id="backupList">
                            <div class="text-center py-3">
                                <i class="fas fa-spinner fa-spin me-2"></i>Memuat daftar backup...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Toggle maintenance message visibility
    document.getElementById('maintenance_mode').addEventListener('change', function() {
        const messageGroup = document.getElementById('maintenance_message_group');
        if (this.checked) {
            messageGroup.style.display = 'block';
        } else {
            messageGroup.style.display = 'none';
        }
    });

    // Toggle maintenance mode
    function toggleMaintenance() {
        const btn = document.getElementById('toggleMaintenanceBtn');
        const originalText = btn.innerHTML;

        // Disable button and show loading
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        fetch('{{ route("admin.settings.toggle-maintenance") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update button
                if (data.maintenance_mode) {
                    btn.className = 'btn btn-success';
                    btn.innerHTML = '<i class="fas fa-play me-2"></i>Nonaktifkan Maintenance';
                } else {
                    btn.className = 'btn btn-warning';
                    btn.innerHTML = '<i class="fas fa-pause me-2"></i>Aktifkan Maintenance';
                }

                // Update status badge
                const statusBadge = document.querySelector('.maintenance-status .badge');
                if (data.maintenance_mode) {
                    statusBadge.className = 'badge bg-warning';
                    statusBadge.innerHTML = '<i class="fas fa-tools me-1"></i>Mode Maintenance Aktif';
                } else {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.innerHTML = '<i class="fas fa-check me-1"></i>Website Normal';
                }

                // Update checkbox
                document.getElementById('maintenance_mode').checked = data.maintenance_mode;

                // Show/hide maintenance message
                const messageGroup = document.getElementById('maintenance_message_group');
                messageGroup.style.display = data.maintenance_mode ? 'block' : 'none';

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: data.message,
                    timer: 3000,
                    showConfirmButton: false
                });
            } else {
                throw new Error(data.message || 'Terjadi kesalahan');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btn.innerHTML = originalText;

            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: 'Terjadi kesalahan saat mengubah mode maintenance.',
                confirmButtonText: 'OK'
            });
        })
        .finally(() => {
            btn.disabled = false;
        });
    }

    // Optimize application
    function optimizeApplication() {
        const btn = document.getElementById('optimizeBtn');
        const originalHtml = btn.innerHTML;

        // Show confirmation
        Swal.fire({
            title: 'Optimize Application?',
            text: 'Ini akan membersihkan semua cache dan mengoptimasi aplikasi. Proses ini mungkin memakan waktu beberapa detik.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Optimize!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mengoptimasi...';

                fetch('{{ route("admin.settings.optimize") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: data.message,
                            timer: 5000,
                            showConfirmButton: false
                        });
                    } else {
                        throw new Error(data.message || 'Terjadi kesalahan');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal!',
                        text: error.message || 'Terjadi kesalahan saat mengoptimasi aplikasi.',
                        confirmButtonText: 'OK'
                    });
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = originalHtml;
                });
            }
        });
    }

    // Create backup
    function createBackup() {
        Swal.fire({
            title: 'Buat Backup Database?',
            text: 'Proses ini akan membuat backup lengkap dari database.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Buat Backup!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                const btn = event.target;
                const originalHtml = btn.innerHTML;

                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Membuat backup...';

                fetch('{{ route("admin.settings.create-backup") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            html: `${data.message}<br><small>File: ${data.backup_file} (${data.file_size})</small>`,
                            timer: 5000,
                            showConfirmButton: false
                        });
                        loadBackupList(); // Refresh backup list
                    } else {
                        throw new Error(data.message || 'Terjadi kesalahan');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal!',
                        text: error.message || 'Terjadi kesalahan saat membuat backup.',
                        confirmButtonText: 'OK'
                    });
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = originalHtml;
                });
            }
        });
    }

    // Run maintenance
    function runMaintenance(type) {
        const typeText = type === 'full' ? 'Full' : 'Basic';
        const description = type === 'full'
            ? 'Maintenance lengkap termasuk optimasi database dan rebuild cache.'
            : 'Maintenance dasar untuk membersihkan cache dan file temporary.';

        Swal.fire({
            title: `Jalankan ${typeText} Maintenance?`,
            text: description,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: type === 'full' ? '#dc3545' : '#ffc107',
            cancelButtonColor: '#6c757d',
            confirmButtonText: `Ya, Jalankan ${typeText} Maintenance!`,
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                const btn = event.target;
                const originalHtml = btn.innerHTML;

                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Menjalankan maintenance...';

                fetch('{{ route("admin.settings.run-maintenance") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        type: type
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: data.message,
                            timer: 5000,
                            showConfirmButton: false
                        });
                    } else {
                        throw new Error(data.message || 'Terjadi kesalahan');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal!',
                        text: error.message || 'Terjadi kesalahan saat menjalankan maintenance.',
                        confirmButtonText: 'OK'
                    });
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = originalHtml;
                });
            }
        });
    }

    // Load backup list
    function loadBackupList() {
        const backupListDiv = document.getElementById('backupList');
        backupListDiv.innerHTML = '<div class="text-center py-3"><i class="fas fa-spinner fa-spin me-2"></i>Memuat daftar backup...</div>';

        fetch('{{ route("admin.settings.backup-list") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.backups.length === 0) {
                    backupListDiv.innerHTML = '<div class="text-center py-3 text-muted">Belum ada backup yang dibuat.</div>';
                } else {
                    let html = '<div class="table-responsive"><table class="table table-sm table-striped">';
                    html += '<thead><tr><th>Nama File</th><th>Ukuran</th><th>Tanggal</th><th>Aksi</th></tr></thead><tbody>';

                    data.backups.forEach(backup => {
                        html += `<tr>
                            <td><i class="fas fa-database me-2"></i>${backup.name}</td>
                            <td>${backup.size}</td>
                            <td>${backup.date}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadBackup('${backup.name}')">
                                    <i class="fas fa-download me-1"></i>Download
                                </button>
                            </td>
                        </tr>`;
                    });

                    html += '</tbody></table></div>';
                    backupListDiv.innerHTML = html;
                }
            } else {
                backupListDiv.innerHTML = '<div class="alert alert-danger">Gagal memuat daftar backup.</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            backupListDiv.innerHTML = '<div class="alert alert-danger">Terjadi kesalahan saat memuat daftar backup.</div>';
        });
    }

    // Download backup
    function downloadBackup(filename) {
        window.location.href = '{{ route("admin.settings.download-backup") }}?filename=' + encodeURIComponent(filename);
    }

    // Load backup list on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadBackupList();
    });
</script>
@endpush

@push('styles')
<style>
.system-info .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.system-info .info-item:last-child {
    border-bottom: none;
}

.system-info .info-item strong {
    font-size: 0.9rem;
}

.system-info .info-item span {
    font-size: 0.9rem;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Cache management
    $('.cache-action').click(function() {
        const action = $(this).data('action');
        const button = $(this);
        const originalHtml = button.html();

        button.prop('disabled', true);
        button.html('<i class="fas fa-spinner fa-spin me-2"></i>Memproses...');

        // Make AJAX request to clear cache
        fetch('{{ route("admin.settings.clear-cache") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                type: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: data.message,
                    timer: 3000,
                    showConfirmButton: false
                });
            } else {
                throw new Error(data.message || 'Terjadi kesalahan');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: error.message || 'Terjadi kesalahan saat membersihkan cache.',
                confirmButtonText: 'OK'
            });
        })
        .finally(() => {
            button.prop('disabled', false);
            button.html(originalHtml);
        });
    });

    // Form validation
    $('form').on('submit', function(e) {
        const appUrl = $('#app_url').val();
        if (!appUrl.startsWith('http://') && !appUrl.startsWith('https://')) {
            e.preventDefault();
            toastr.error('URL aplikasi harus dimulai dengan http:// atau https://');
            return false;
        }
    });
});
</script>
@endpush
