<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class RunMaintenance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'maintenance:run {--full : Run full maintenance including optimization}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run maintenance tasks for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting maintenance tasks...');
        $startTime = microtime(true);

        try {
            // 1. Clear all caches
            $this->task('Clearing caches', function () {
                Artisan::call('cache:clear');
                Artisan::call('config:clear');
                Artisan::call('route:clear');
                Artisan::call('view:clear');
                return true;
            });

            // 2. Clean up expired tokens and sessions
            $this->task('Cleaning up expired tokens', function () {
                Artisan::call('security:cleanup-tokens');
                return true;
            });

            // 3. Clean up old logs
            $this->task('Cleaning up old logs', function () {
                $this->cleanupOldLogs();
                return true;
            });

            // 4. Clean up temporary files
            $this->task('Cleaning up temporary files', function () {
                $this->cleanupTempFiles();
                return true;
            });

            // 5. Optimize database (if full maintenance)
            if ($this->option('full')) {
                $this->task('Optimizing database', function () {
                    $this->optimizeDatabase();
                    return true;
                });

                // 6. Rebuild caches
                $this->task('Rebuilding caches', function () {
                    Artisan::call('config:cache');
                    Artisan::call('route:cache');
                    Artisan::call('optimize');
                    return true;
                });
            }

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);

            $this->info("Maintenance completed successfully in {$duration} seconds!");

            // Log maintenance activity
            \App\Models\SecurityLog::logEvent('maintenance_completed', null, [
                'duration_seconds' => $duration,
                'full_maintenance' => $this->option('full'),
                'tasks_completed' => $this->option('full') ? 6 : 4,
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->error('Maintenance failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Clean up old log files
     */
    private function cleanupOldLogs()
    {
        // Clean up Laravel logs older than 30 days
        $logPath = storage_path('logs');
        $files = glob($logPath . '/laravel-*.log');
        $cutoffDate = Carbon::now()->subDays(30);
        $deletedCount = 0;

        foreach ($files as $file) {
            $fileDate = Carbon::createFromTimestamp(filemtime($file));
            if ($fileDate->lt($cutoffDate)) {
                unlink($file);
                $deletedCount++;
            }
        }

        // Clean up old security logs from database
        $oldSecurityLogs = \App\Models\SecurityLog::where('created_at', '<', Carbon::now()->subDays(90))->count();
        if ($oldSecurityLogs > 0) {
            \App\Models\SecurityLog::where('created_at', '<', Carbon::now()->subDays(90))->delete();
        }

        $this->line("  - Deleted {$deletedCount} old log files");
        $this->line("  - Deleted {$oldSecurityLogs} old security logs");
    }

    /**
     * Clean up temporary files
     */
    private function cleanupTempFiles()
    {
        $deletedCount = 0;

        // Clean up storage/framework/cache/data
        $cachePath = storage_path('framework/cache/data');
        if (is_dir($cachePath)) {
            $files = glob($cachePath . '/*');
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < (time() - 3600)) { // Older than 1 hour
                    unlink($file);
                    $deletedCount++;
                }
            }
        }

        // Clean up storage/framework/sessions
        $sessionPath = storage_path('framework/sessions');
        if (is_dir($sessionPath)) {
            $files = glob($sessionPath . '/*');
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < (time() - 7200)) { // Older than 2 hours
                    unlink($file);
                    $deletedCount++;
                }
            }
        }

        // Clean up storage/framework/views (compiled views)
        $viewPath = storage_path('framework/views');
        if (is_dir($viewPath)) {
            $files = glob($viewPath . '/*');
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < (time() - 86400)) { // Older than 1 day
                    unlink($file);
                    $deletedCount++;
                }
            }
        }

        $this->line("  - Deleted {$deletedCount} temporary files");
    }

    /**
     * Optimize database tables
     */
    private function optimizeDatabase()
    {
        try {
            // Get all tables
            $tables = DB::select('SHOW TABLES');
            $dbName = config('database.connections.mysql.database');
            $tableKey = "Tables_in_{$dbName}";
            $optimizedCount = 0;

            foreach ($tables as $table) {
                $tableName = $table->$tableKey;
                DB::statement("OPTIMIZE TABLE `{$tableName}`");
                $optimizedCount++;
            }

            $this->line("  - Optimized {$optimizedCount} database tables");

        } catch (\Exception $e) {
            $this->line("  - Database optimization failed: " . $e->getMessage());
        }
    }

    /**
     * Display a task with spinner
     */
    private function task($description, $task)
    {
        $this->line("  {$description}...");
        
        try {
            $result = $task();
            if ($result) {
                $this->line("  ✓ {$description} completed");
            } else {
                $this->line("  ✗ {$description} failed");
            }
            return $result;
        } catch (\Exception $e) {
            $this->line("  ✗ {$description} failed: " . $e->getMessage());
            return false;
        }
    }
}
