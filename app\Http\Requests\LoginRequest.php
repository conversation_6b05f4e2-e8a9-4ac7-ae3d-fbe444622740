<?php

namespace App\Http\Requests;

use App\Rules\StrongPassword;

class LoginRequest extends SecureFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'login' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // Check for SQL injection
                    if ($this->hasSqlInjection($value)) {
                        $fail('Input mengandung karakter yang tidak diizinkan.');
                    }
                    
                    // Check for XSS
                    if ($this->hasXss($value)) {
                        $fail('Input mengandung karakter yang tidak diizinkan.');
                    }
                    
                    // Check for command injection
                    if ($this->hasCommandInjection($value)) {
                        $fail('Input mengandung karakter yang tidak diizinkan.');
                    }
                }
            ],
            'password' => [
                'required',
                'string',
                'min:6',
                'max:128',
                function ($attribute, $value, $fail) {
                    // Check for null bytes
                    if (strpos($value, "\0") !== false) {
                        $fail('Password mengandung karakter yang tidak diizinkan.');
                    }
                }
            ],
            'remember' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'login.required' => 'Username/Email/NISN/NIP harus diisi.',
            'login.max' => 'Username/Email/NISN/NIP maksimal 255 karakter.',
            'password.required' => 'Password harus diisi.',
            'password.min' => 'Password minimal 6 karakter.',
            'password.max' => 'Password maksimal 128 karakter.',
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'login' => 'username/email/NISN/NIP',
            'password' => 'password',
            'remember' => 'ingat saya',
        ]);
    }

    /**
     * Sanitize string input with special handling for login
     */
    protected function sanitizeString(string $value, string $key): string
    {
        if ($key === 'login') {
            // Remove null bytes and trim
            $value = str_replace("\0", '', $value);
            $value = trim($value);
            
            // Don't over-sanitize login field as it could be email, username, NISN, or NIP
            // Just remove dangerous characters
            $value = preg_replace('/[<>"\'\0\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);
            
            return $value;
        }
        
        return parent::sanitizeString($value, $key);
    }
}
