<?php $__env->startSection('title', 'Dashboard PPDB'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Dashboard PPDB Online</h1>
    <div>
        <a href="<?php echo e(route('admin.ppdb.settings')); ?>" class="btn btn-outline-primary me-2">
            <i class="fas fa-cog me-2"></i>Pengaturan PPDB
        </a>
        <a href="<?php echo e(route('ppdb.index')); ?>" class="btn btn-outline-success" target="_blank">
            <i class="fas fa-external-link-alt me-2"></i>Lihat Halaman PPDB
        </a>
    </div>
</div>

<!-- PPDB Status Alert -->
<?php if($ppdbSetting): ?>
<div class="alert alert-<?php echo e($ppdbSetting->status == 'open' ? 'success' : ($ppdbSetting->status == 'closed' ? 'danger' : 'warning')); ?> mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-<?php echo e($ppdbSetting->status == 'open' ? 'check-circle' : ($ppdbSetting->status == 'closed' ? 'times-circle' : 'clock')); ?> fa-2x me-3"></i>
        <div>
            <h5 class="alert-heading mb-1">Status PPDB: <?php echo e(ucfirst($ppdbSetting->status)); ?></h5>
            <p class="mb-0">
                <?php if($ppdbSetting->status == 'open'): ?>
                    PPDB sedang dibuka untuk tahun ajaran <?php echo e($ppdbSetting->academic_year); ?>

                <?php elseif($ppdbSetting->status == 'closed'): ?>
                    PPDB telah ditutup untuk tahun ajaran <?php echo e($ppdbSetting->academic_year); ?>

                <?php else: ?>
                    PPDB akan segera dibuka untuk tahun ajaran <?php echo e($ppdbSetting->academic_year); ?>

                <?php endif; ?>
            </p>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_registrations']); ?></div>
            <div class="stats-label">Total Pendaftar</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['pending_registrations']); ?></div>
            <div class="stats-label">Menunggu Verifikasi</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['verified_registrations']); ?></div>
            <div class="stats-label">Terverifikasi</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['approved_registrations']); ?></div>
            <div class="stats-label">Diterima</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Registrations -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Pendaftaran Terbaru</h5>
            </div>
            <div class="card-body">
                <?php if($recentRegistrations->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>No. Pendaftaran</th>
                                    <th>Nama</th>
                                    <th>Program</th>
                                    <th>Status</th>
                                    <th>Tanggal</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentRegistrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $registration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($registration->registration_number); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($registration->user->name); ?></strong>
                                            <br><small class="text-muted"><?php echo e($registration->user->email); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo e($registration->program->name ?? 'N/A'); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo e($registration->status == 'approved' ? 'success' : ($registration->status == 'verified' ? 'info' : ($registration->status == 'rejected' ? 'danger' : 'warning'))); ?>">
                                            <?php echo e(ucfirst($registration->status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo e($registration->created_at ? $registration->created_at->format('d M Y') : '-'); ?></small>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('admin.ppdb.registrations.show', $registration->id)); ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="<?php echo e(route('admin.ppdb.registrations')); ?>" class="btn btn-outline-primary">
                            Lihat Semua Pendaftaran
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Belum ada pendaftaran</h6>
                        <p class="text-muted">Pendaftaran baru akan muncul di sini</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Registrations by Program -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Pendaftaran per Program</h5>
            </div>
            <div class="card-body">
                <?php if($registrationsByProgram->count() > 0): ?>
                    <?php $__currentLoopData = $registrationsByProgram; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-1"><?php echo e($item->program->name ?? 'Unknown Program'); ?></h6>
                            <small class="text-muted"><?php echo e($item->program->level ?? ''); ?></small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary fs-6"><?php echo e($item->total); ?></span>
                            <?php if($item->program && $item->program->capacity): ?>
                                <br><small class="text-muted">dari <?php echo e($item->program->capacity); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Belum ada data</h6>
                        <p class="text-muted">Data akan muncul setelah ada pendaftaran</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- PPDB Timeline -->
<?php if($ppdbSetting): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Timeline PPDB <?php echo e($ppdbSetting->academic_year); ?></h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <!-- 1. Pengumuman Pembukaan PPDB -->
                    <div class="timeline-item <?php echo e($ppdbSetting->created_at && now() >= $ppdbSetting->created_at ? 'completed' : 'pending'); ?>">
                        <div class="timeline-marker">
                            <i class="fas fa-<?php echo e($ppdbSetting->created_at && now() >= $ppdbSetting->created_at ? 'check' : 'clock'); ?>"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>Pengumuman Pembukaan PPDB</h6>
                            <p class="text-muted"><?php echo e($ppdbSetting->created_at ? $ppdbSetting->created_at->format('d F Y H:i') : 'Belum ditentukan'); ?></p>
                        </div>
                    </div>

                    <!-- 2. Pendaftaran -->
                    <div class="timeline-item <?php echo e($ppdbSetting->registration_start && $ppdbSetting->registration_end && now() >= $ppdbSetting->registration_start ? (now() <= $ppdbSetting->registration_end ? 'active' : 'completed') : 'pending'); ?>">
                        <div class="timeline-marker">
                            <i class="fas fa-<?php echo e($ppdbSetting->registration_end && now() >= $ppdbSetting->registration_end ? 'check' : ($ppdbSetting->registration_start && now() >= $ppdbSetting->registration_start ? 'clock' : 'clock')); ?>"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>Pendaftaran</h6>
                            <p class="text-muted">
                                <?php echo e($ppdbSetting->registration_start ? $ppdbSetting->registration_start->format('d F Y H:i') : 'Belum ditentukan'); ?> -
                                <?php echo e($ppdbSetting->registration_end ? $ppdbSetting->registration_end->format('d F Y H:i') : 'Belum ditentukan'); ?>

                            </p>
                        </div>
                    </div>

                    <!-- 3. Pelengkapan Berkas -->
                    <div class="timeline-item <?php echo e($ppdbSetting->document_start && $ppdbSetting->document_end && now() >= $ppdbSetting->document_start ? (now() <= $ppdbSetting->document_end ? 'active' : 'completed') : 'pending'); ?>">
                        <div class="timeline-marker">
                            <i class="fas fa-<?php echo e($ppdbSetting->document_end && now() > $ppdbSetting->document_end ? 'check' : ($ppdbSetting->document_start && now() >= $ppdbSetting->document_start ? 'clock' : 'clock')); ?>"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>Pelengkapan/Melengkapi Berkas</h6>
                            <p class="text-muted">
                                <?php if($ppdbSetting->document_start && $ppdbSetting->document_end): ?>
                                    <?php echo e($ppdbSetting->document_start->format('d F Y H:i')); ?> - <?php echo e($ppdbSetting->document_end->format('d F Y H:i')); ?>

                                <?php else: ?>
                                    Calon siswa melengkapi dokumen di portal masing-masing
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>

                    <!-- 4. Pengumuman Hasil -->
                    <div class="timeline-item <?php echo e($ppdbSetting->announcement_date && now() >= $ppdbSetting->announcement_date ? 'completed' : 'pending'); ?>">
                        <div class="timeline-marker">
                            <i class="fas fa-<?php echo e($ppdbSetting->announcement_date && now() >= $ppdbSetting->announcement_date ? 'check' : 'clock'); ?>"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>Pengumuman Hasil</h6>
                            <p class="text-muted"><?php echo e($ppdbSetting->announcement_date ? $ppdbSetting->announcement_date->format('d F Y H:i') : 'Belum ditentukan'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
}

.timeline-item.active .timeline-marker {
    background: #ffc107;
}

.timeline-item.pending .timeline-marker {
    background: #6c757d;
}

.timeline-content h6 {
    margin-bottom: 5px;
    color: #333;
}

.timeline-content p {
    margin-bottom: 0;
    font-size: 0.9rem;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/ppdb/dashboard.blade.php ENDPATH**/ ?>