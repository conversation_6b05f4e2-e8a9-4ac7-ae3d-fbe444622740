<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class TimezoneServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Set timezone from config
        $timezone = config('app.timezone', 'Asia/Jakarta');

        // Set PHP timezone
        date_default_timezone_set($timezone);

        // Set Carbon timezone
        if (class_exists('\Carbon\Carbon')) {
            \Carbon\Carbon::setLocale('id');
        }
    }
}
