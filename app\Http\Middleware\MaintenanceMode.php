<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Models\SchoolSetting;

class MaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if maintenance mode is enabled
        $schoolSetting = SchoolSetting::first();
        $isMaintenanceMode = $schoolSetting && $schoolSetting->maintenance_mode;

        // Allow access for super admins and admins
        if ($isMaintenanceMode && Auth::check()) {
            $user = Auth::user();
            if (in_array($user->user_type, ['super_admin', 'admin'])) {
                return $next($request);
            }
        }

        // Allow access to login, logout, and maintenance routes
        $allowedRoutes = [
            'login',
            'logout',
            'maintenance',
            'admin.login',
            'admin.logout'
        ];

        if ($isMaintenanceMode && !in_array($request->route()->getName(), $allowedRoutes)) {
            // Check if it's an admin route
            if ($request->is('admin/*')) {
                // Allow admin routes for authenticated admins
                if (!Auth::check() || !in_array(Auth::user()->user_type, ['super_admin', 'admin'])) {
                    return redirect()->route('maintenance');
                }
            } else {
                // Redirect to maintenance page for all other routes
                return redirect()->route('maintenance');
            }
        }

        return $next($request);
    }
}
