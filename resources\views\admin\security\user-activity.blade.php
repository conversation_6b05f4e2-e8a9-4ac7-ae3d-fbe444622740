@extends('layouts.dashboard')

@section('title', 'Aktivitas User')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Aktivitas User</h1>
        <p class="text-muted">Monitor aktivitas semua user dalam sistem</p>
    </div>
    <div>
        <a href="{{ route('admin.security.dashboard') }}" class="btn btn-outline-info me-2">
            <i class="fas fa-chart-line me-2"></i>Dashboard Keamanan
        </a>
        <a href="{{ route('admin.security.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-list me-2"></i>Log <PERSON>
        </a>
    </div>
</div>

<!-- Search Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.security.user-activity') }}" class="row g-3">
            <div class="col-md-8">
                <label for="search" class="form-label">Cari User</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari nama atau email user..." 
                       value="{{ request('search') }}">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Cari
                </button>
                <a href="{{ route('admin.security.user-activity') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- User Activity List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Daftar User dan Aktivitas (30 Hari Terakhir)</h5>
    </div>
    <div class="card-body">
        @if($users->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Tipe User</th>
                            <th>Total Aktivitas</th>
                            <th>Login Terakhir</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-3">
                                        @if($user->avatar)
                                            <img src="{{ Storage::url($user->avatar) }}" alt="{{ $user->name }}" class="rounded-circle">
                                        @else
                                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                                                {{ strtoupper(substr($user->name, 0, 1)) }}
                                            </div>
                                        @endif
                                    </div>
                                    <div>
                                        <strong>{{ $user->name }}</strong>
                                        <br><small class="text-muted">{{ $user->email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ $user->user_type == 'super_admin' ? 'danger' : ($user->user_type == 'admin' ? 'warning' : ($user->user_type == 'guru' ? 'info' : ($user->user_type == 'siswa' ? 'success' : 'secondary'))) }}">
                                    {{ ucfirst(str_replace('_', ' ', $user->user_type)) }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary fs-6 me-2">{{ $user->security_logs_count }}</span>
                                    <div class="progress flex-grow-1" style="height: 8px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: {{ min(($user->security_logs_count / 100) * 100, 100) }}%">
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($user->last_login_at)
                                    <div>
                                        <small>{{ $user->last_login_at->format('d M Y H:i') }}</small>
                                        <br><small class="text-muted">{{ $user->last_login_at->diffForHumans() }}</small>
                                    </div>
                                @else
                                    <span class="text-muted">Belum pernah login</span>
                                @endif
                            </td>
                            <td>
                                @if($user->is_active)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Aktif
                                    </span>
                                @else
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times-circle me-1"></i>Nonaktif
                                    </span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.security.user-activity.detail', $user) }}" class="btn btn-sm btn-outline-primary" title="Detail Aktivitas">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-outline-info" title="Profile User">
                                        <i class="fas fa-user"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan {{ $users->firstItem() }} - {{ $users->lastItem() }} dari {{ $users->total() }} user
                </div>
                <div class="pagination-wrapper">
                    {{ $users->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada user ditemukan</h5>
                <p class="text-muted">Belum ada user yang sesuai dengan pencarian yang dilakukan.</p>
            </div>
        @endif
    </div>
</div>

<!-- Activity Summary -->
<div class="row mt-4">
    <div class="col-lg-4 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-3"></i>
                <h4>{{ $users->total() }}</h4>
                <p class="text-muted">Total User</p>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-user-check fa-2x text-success mb-3"></i>
                <h4>{{ $users->where('is_active', true)->count() }}</h4>
                <p class="text-muted">User Aktif</p>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                <h4>{{ $users->where('last_login_at', '>=', now()->subDays(7))->count() }}</h4>
                <p class="text-muted">Login 7 Hari Terakhir</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-sm img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    background-color: #6c757d;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background-color: #007bff;
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

/* Fix for pagination SVG icons that are too large */
.pagination-wrapper .pagination svg {
    width: 16px !important;
    height: 16px !important;
}

.pagination-wrapper .pagination .page-link svg {
    width: 14px !important;
    height: 14px !important;
}

/* Override Tailwind classes that might cause large icons */
.pagination-wrapper .w-5 {
    width: 16px !important;
}

.pagination-wrapper .h-5 {
    height: 16px !important;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Add tooltip to progress bars
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Auto-refresh every 5 minutes (reduced from 60 seconds to prevent fetch errors)
    setInterval(function() {
        if (!$('.modal').hasClass('show') && !document.hidden) {
            // Use fetch API with proper error handling
            fetch(window.location.href, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'text/html'
                }
            }).then(response => {
                if (response.ok) {
                    location.reload();
                }
            }).catch(error => {
                console.log('Auto-refresh failed:', error);
                // Don't reload if fetch fails
            });
        }
    }, 300000); // 5 minutes
});
</script>
@endpush
