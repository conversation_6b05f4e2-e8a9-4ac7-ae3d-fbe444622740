@extends('layouts.app')

@section('title', 'Terlalu Banyak Permintaan')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Terlalu Banyak Permintaan
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-clock fa-4x text-warning mb-3"></i>
                        <h5 class="text-dark">{{ $message }}</h5>
                    </div>
                    
                    <div class="alert alert-info">
                        <strong>Mengapa ini terjadi?</strong><br>
                        Sistem kami mendeteksi terlalu banyak permintaan dari alamat IP Anda dalam waktu singkat. 
                        Ini adalah langkah keamanan untuk melindungi sistem dari penyalahgunaan.
                    </div>

                    <div class="row text-center mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                                    <h6>Keamanan Sistem</h6>
                                    <small class="text-muted">Melindungi dari serangan brute force</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="fas fa-hourglass-half fa-2x text-info mb-2"></i>
                                    <h6>Tunggu Sebentar</h6>
                                    <small class="text-muted">Akses akan pulih secara otomatis</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="countdown-container mb-4">
                        <h6>Silakan tunggu:</h6>
                        <div class="countdown-timer" id="countdown">
                            <span id="minutes">{{ floor($retryAfter / 60) }}</span>:
                            <span id="seconds">{{ str_pad($retryAfter % 60, 2, '0', STR_PAD_LEFT) }}</span>
                        </div>
                    </div>

                    <div class="d-flex justify-content-center gap-3">
                        <a href="{{ route('landing') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Kembali ke Beranda
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                            <i class="fas fa-arrow-left me-2"></i>Kembali
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tips Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Tips untuk Menghindari Pembatasan
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Tunggu beberapa saat sebelum mencoba lagi
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Pastikan koneksi internet stabil
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Jangan menekan tombol berulang kali
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            Hubungi administrator jika masalah berlanjut
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.countdown-timer {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px solid #dee2e6;
    display: inline-block;
    min-width: 120px;
}

.countdown-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid #dee2e6;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.countdown-timer {
    animation: pulse 2s infinite;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let retryAfter = {{ $retryAfter }};
    
    function updateCountdown() {
        const minutes = Math.floor(retryAfter / 60);
        const seconds = retryAfter % 60;
        
        document.getElementById('minutes').textContent = minutes;
        document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        
        if (retryAfter <= 0) {
            // Auto refresh when countdown reaches zero
            window.location.reload();
        } else {
            retryAfter--;
        }
    }
    
    // Update countdown every second
    const countdownInterval = setInterval(updateCountdown, 1000);
    
    // Clear interval when page is unloaded
    window.addEventListener('beforeunload', function() {
        clearInterval(countdownInterval);
    });
});
</script>
@endpush
