<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\SecurityLog;

class SessionSecurity
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (Auth::check()) {
            $user = Auth::user();
            
            // Check for session hijacking
            $this->checkSessionHijacking($request, $user);
            
            // Check for session fixation
            $this->checkSessionFixation($request, $user);
            
            // Update session security data
            $this->updateSessionSecurity($request, $user);
            
            // Regenerate session ID periodically
            $this->regenerateSessionPeriodically($request);
        }

        $response = $next($request);

        // Set secure session cookies
        $this->setSecureSessionCookies($response);

        return $response;
    }

    /**
     * Check for session hijacking attempts
     */
    private function checkSessionHijacking(Request $request, $user): void
    {
        $sessionKey = 'user_session_data_' . $user->id;
        $storedData = Session::get($sessionKey);
        
        if ($storedData) {
            $currentIp = $request->ip();
            $currentUserAgent = $request->userAgent();
            
            // Check IP address change (allow for reasonable IP changes)
            if (isset($storedData['ip']) && $storedData['ip'] !== $currentIp) {
                // Allow IP changes within same subnet for mobile users
                if (!$this->isIpInSameSubnet($storedData['ip'], $currentIp)) {
                    $this->logSuspiciousActivity($request, 'session_ip_change', [
                        'user_id' => $user->id,
                        'old_ip' => $storedData['ip'],
                        'new_ip' => $currentIp,
                    ]);
                    
                    // Force logout for security
                    Auth::logout();
                    Session::invalidate();
                    Session::regenerateToken();
                    
                    abort(401, 'Sesi tidak valid. Silakan login kembali.');
                }
            }
            
            // Check User-Agent change
            if (isset($storedData['user_agent']) && $storedData['user_agent'] !== $currentUserAgent) {
                $this->logSuspiciousActivity($request, 'session_user_agent_change', [
                    'user_id' => $user->id,
                    'old_user_agent' => $storedData['user_agent'],
                    'new_user_agent' => $currentUserAgent,
                ]);
                
                // Force logout for security
                Auth::logout();
                Session::invalidate();
                Session::regenerateToken();
                
                abort(401, 'Sesi tidak valid. Silakan login kembali.');
            }
            
            // Check session timeout
            if (isset($storedData['last_activity'])) {
                $maxInactivity = config('session.lifetime') * 60; // Convert minutes to seconds
                $timeSinceLastActivity = time() - $storedData['last_activity'];
                
                if ($timeSinceLastActivity > $maxInactivity) {
                    $this->logSuspiciousActivity($request, 'session_timeout', [
                        'user_id' => $user->id,
                        'last_activity' => $storedData['last_activity'],
                        'timeout_seconds' => $timeSinceLastActivity,
                    ]);
                    
                    Auth::logout();
                    Session::invalidate();
                    Session::regenerateToken();
                    
                    abort(401, 'Sesi telah berakhir. Silakan login kembali.');
                }
            }
        }
    }

    /**
     * Check for session fixation attempts
     */
    private function checkSessionFixation(Request $request, $user): void
    {
        $sessionKey = 'session_created_' . $user->id;
        $sessionCreated = Session::get($sessionKey);
        
        if (!$sessionCreated) {
            // This is a new session, regenerate ID to prevent fixation
            Session::regenerate(true);
            Session::put($sessionKey, time());
            
            $this->logSuspiciousActivity($request, 'session_regenerated', [
                'user_id' => $user->id,
                'reason' => 'new_session_created',
            ]);
        }
    }

    /**
     * Update session security data
     */
    private function updateSessionSecurity(Request $request, $user): void
    {
        $sessionKey = 'user_session_data_' . $user->id;
        
        Session::put($sessionKey, [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'last_activity' => time(),
            'login_time' => Session::get($sessionKey . '.login_time', time()),
        ]);
    }

    /**
     * Regenerate session ID periodically
     */
    private function regenerateSessionPeriodically(Request $request): void
    {
        $lastRegeneration = Session::get('last_session_regeneration', 0);
        $regenerationInterval = 1800; // 30 minutes
        
        if (time() - $lastRegeneration > $regenerationInterval) {
            Session::regenerate(true);
            Session::put('last_session_regeneration', time());
            
            $this->logSuspiciousActivity($request, 'session_regenerated', [
                'user_id' => Auth::id(),
                'reason' => 'periodic_regeneration',
            ]);
        }
    }

    /**
     * Set secure session cookies
     */
    private function setSecureSessionCookies(Response $response): void
    {
        // Get session cookie name
        $sessionName = config('session.cookie');
        
        // Set secure cookie attributes
        $response->headers->setCookie(
            cookie(
                $sessionName,
                Session::getId(),
                config('session.lifetime'),
                config('session.path'),
                config('session.domain'),
                config('session.secure'), // HTTPS only
                true, // HTTP only
                false,
                config('session.same_site')
            )
        );
    }

    /**
     * Check if two IP addresses are in the same subnet
     */
    private function isIpInSameSubnet(string $ip1, string $ip2): bool
    {
        // For IPv4, check if they're in the same /24 subnet
        if (filter_var($ip1, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) && 
            filter_var($ip2, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            
            $subnet1 = ip2long($ip1) & ip2long('*************');
            $subnet2 = ip2long($ip2) & ip2long('*************');
            
            return $subnet1 === $subnet2;
        }
        
        // For IPv6 or other cases, be more strict
        return false;
    }

    /**
     * Log suspicious session activity
     */
    private function logSuspiciousActivity(Request $request, string $type, array $metadata): void
    {
        SecurityLog::logEvent('suspicious_activity', Auth::id(), array_merge([
            'type' => $type,
            'url' => $request->fullUrl(),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip(),
        ], $metadata));
    }
}
