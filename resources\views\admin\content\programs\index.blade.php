@extends('layouts.dashboard')

@section('title', 'Manajemen Program')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Manajemen Program Pendidikan</h1>
    <a href="{{ route('admin.content.programs.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Tambah Program
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stats-number">{{ $stats['total_programs'] }}</div>
            <div class="stats-label">Total Program</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number">{{ $stats['active'] }}</div>
            <div class="stats-label">Program Aktif</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-pause-circle"></i>
            </div>
            <div class="stats-number">{{ $stats['inactive'] }}</div>
            <div class="stats-label">Program Nonaktif</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.content.programs.index') }}" class="row g-3">
            <div class="col-md-2">
                <label for="level" class="form-label">Level</label>
                <select name="level" id="level" class="form-select">
                    <option value="">Semua Level</option>
                    <option value="TK" {{ request('level') == 'TK' ? 'selected' : '' }}>TK</option>
                    <option value="SD" {{ request('level') == 'SD' ? 'selected' : '' }}>SD</option>
                    <option value="SMP" {{ request('level') == 'SMP' ? 'selected' : '' }}>SMP</option>
                    <option value="SMA" {{ request('level') == 'SMA' ? 'selected' : '' }}>SMA</option>
                    <option value="SMK" {{ request('level') == 'SMK' ? 'selected' : '' }}>SMK</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="type" class="form-label">Tipe</label>
                <select name="type" id="type" class="form-select">
                    <option value="">Semua Tipe</option>
                    <option value="regular" {{ request('type') == 'regular' ? 'selected' : '' }}>Regular</option>
                    <option value="unggulan" {{ request('type') == 'unggulan' ? 'selected' : '' }}>Unggulan</option>
                    <option value="internasional" {{ request('type') == 'internasional' ? 'selected' : '' }}>Internasional</option>
                </select>
            </div>
            <div class="col-md-5">
                <label for="search" class="form-label">Pencarian</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari nama program..." 
                       value="{{ request('search') }}">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ route('admin.content.programs.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Programs Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Daftar Program</h5>
    </div>
    <div class="card-body">
        @if($programs->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Gambar</th>
                            <th>Nama Program</th>
                            <th>Level</th>
                            <th>Tipe</th>
                            <th>Kapasitas</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($programs as $program)
                        <tr>
                            <td>
                                <div class="program-image">
                                    @if($program->image)
                                        <img src="{{ Storage::url($program->image) }}" alt="{{ $program->name }}" class="rounded">
                                    @else
                                        <div class="image-placeholder rounded d-flex align-items-center justify-content-center">
                                            <i class="fas fa-graduation-cap text-muted"></i>
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $program->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ Str::limit($program->description, 60) }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $program->level }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $program->type == 'regular' ? 'secondary' : ($program->type == 'unggulan' ? 'warning' : 'primary') }}">
                                    {{ ucfirst($program->type) }}
                                </span>
                            </td>
                            <td>
                                <small>
                                    {{ $program->current_students ?? 0 }}/{{ $program->capacity ?? 0 }}
                                    @if($program->capacity)
                                        <br><span class="text-muted">{{ round(($program->current_students ?? 0) / $program->capacity * 100) }}%</span>
                                    @endif
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $program->is_active ? 'success' : 'danger' }}">
                                    {{ $program->is_active ? 'Aktif' : 'Nonaktif' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.content.programs.show', $program) }}" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.content.programs.edit', $program) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('landing.program', $program->slug) }}" class="btn btn-sm btn-outline-success" title="Lihat di Website" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteProgram('{{ $program->slug }}', '{{ addslashes($program->name) }}')"
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan {{ $programs->firstItem() }} - {{ $programs->lastItem() }} dari {{ $programs->total() }} program
                </div>
                {{ $programs->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada program ditemukan</h5>
                <p class="text-muted">Belum ada program yang sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('admin.content.programs.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Tambah Program Pertama
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@push('styles')
<style>
.program-image {
    width: 60px;
    height: 40px;
}

.program-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-placeholder {
    width: 60px;
    height: 40px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteProgram(slug, name) {
    Swal.fire({
        title: 'Hapus Program?',
        text: `Apakah Anda yakin ingin menghapus program "${name}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus program',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send AJAX DELETE request
            $.ajax({
                url: `/admin/content/programs/${slug}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Program berhasil dihapus.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    console.error('Delete error:', xhr);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus program: ' + (xhr.responseJSON?.message || 'Unknown error'),
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>
@endpush
