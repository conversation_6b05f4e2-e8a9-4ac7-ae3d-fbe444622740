<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's check if the table exists
        if (!Schema::hasTable('security_logs')) {
            return;
        }

        // Change the event_type column from enum to varchar to avoid truncation
        Schema::table('security_logs', function (Blueprint $table) {
            // Drop the enum constraint and change to varchar
            $table->string('event_type', 100)->change();
        });

        // Update any existing data that might be truncated
        DB::statement("UPDATE security_logs SET event_type = 'suspicious_file_upload' WHERE event_type = 'suspicious_file_uplo'");
        DB::statement("UPDATE security_logs SET event_type = 'rate_limit_exceeded' WHERE event_type = 'rate_limit_exceede'");
        DB::statement("UPDATE security_logs SET event_type = 'suspicious_activity' WHERE event_type = 'suspicious_activit'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to enum if needed
        Schema::table('security_logs', function (Blueprint $table) {
            $table->enum('event_type', [
                'login_success', 
                'login_failed', 
                'logout', 
                'password_reset', 
                'account_locked', 
                'suspicious_activity', 
                'rate_limit_exceeded', 
                'suspicious_file_upload', 
                'data_access', 
                'data_modification',
                'profile_updated',
                'password_changed',
                'avatar_deleted',
                'multiple_sessions_detected'
            ])->default('login_success')->change();
        });
    }
};
