@extends('layouts.dashboard')

@section('title', 'Detail Galeri - ' . $gallery->title)

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Detail Galeri</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="#">Manajemen Konten</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.content.gallery.index') }}">Galeri</a></li>
                    <li class="breadcrumb-item active">{{ $gallery->title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.content.gallery.edit', $gallery) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-2"></i>Edit Galeri
            </a>
            <a href="{{ route('admin.content.gallery.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Gallery Content -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-{{ $gallery->file_type === 'image' ? 'image' : 'video' }} me-2"></i>{{ $gallery->title }}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- File Display -->
                    <div class="mb-4 text-center">
                        @if($gallery->file_type === 'image')
                            <img src="{{ Storage::url($gallery->file_path) }}" 
                                 alt="{{ $gallery->alt_text ?: $gallery->title }}" 
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 500px; max-width: 100%;">
                        @elseif($gallery->file_type === 'video')
                            <video controls class="w-100 rounded shadow-sm" style="max-height: 500px;">
                                <source src="{{ Storage::url($gallery->file_path) }}" type="video/mp4">
                                Browser Anda tidak mendukung video.
                            </video>
                        @endif
                    </div>

                    @if($gallery->description)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Deskripsi</h6>
                            <div class="content-text">
                                {!! nl2br(e($gallery->description)) !!}
                            </div>
                        </div>
                    @endif

                    @if($gallery->alt_text)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Alt Text</h6>
                            <div class="content-text">
                                {{ $gallery->alt_text }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Gallery Info -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informasi Galeri
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Judul</small>
                        <div class="fw-bold">{{ $gallery->title }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Tipe File</small>
                        <div>
                            @if($gallery->file_type === 'image')
                                <span class="badge bg-primary">
                                    <i class="fas fa-image me-1"></i>Gambar
                                </span>
                            @else
                                <span class="badge bg-success">
                                    <i class="fas fa-video me-1"></i>Video
                                </span>
                            @endif
                        </div>
                    </div>

                    @if($gallery->category)
                        <div class="mb-3">
                            <small class="text-muted">Kategori</small>
                            <div class="fw-bold">
                                @switch($gallery->category)
                                    @case('kegiatan')
                                        <span class="badge bg-info">Kegiatan</span>
                                        @break
                                    @case('fasilitas')
                                        <span class="badge bg-warning">Fasilitas</span>
                                        @break
                                    @case('prestasi')
                                        <span class="badge bg-success">Prestasi</span>
                                        @break
                                    @case('acara')
                                        <span class="badge bg-danger">Acara</span>
                                        @break
                                    @case('pembelajaran')
                                        <span class="badge bg-primary">Pembelajaran</span>
                                        @break
                                    @case('ekstrakurikuler')
                                        <span class="badge bg-secondary">Ekstrakurikuler</span>
                                        @break
                                    @default
                                        <span class="badge bg-light text-dark">{{ ucfirst($gallery->category) }}</span>
                                @endswitch
                            </div>
                        </div>
                    @endif

                    @if($gallery->album)
                        <div class="mb-3">
                            <small class="text-muted">Album</small>
                            <div class="fw-bold">{{ $gallery->album }}</div>
                        </div>
                    @endif

                    <div class="mb-3">
                        <small class="text-muted">Featured</small>
                        <div>
                            @if($gallery->is_featured)
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>Ya
                                </span>
                            @else
                                <span class="badge bg-secondary">Tidak</span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Urutan</small>
                        <div class="fw-bold">{{ $gallery->order }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Diupload oleh</small>
                        <div class="fw-bold">{{ $gallery->uploader->name ?? 'Unknown' }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Tanggal Upload</small>
                        <div class="fw-bold">{{ $gallery->created_at->format('d M Y, H:i') }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Terakhir Diperbarui</small>
                        <div class="fw-bold">{{ $gallery->updated_at->format('d M Y, H:i') }}</div>
                    </div>
                </div>
            </div>

            <!-- File Details -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-file me-2"></i>Detail File
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Nama File</small>
                        <div class="fw-bold">{{ basename($gallery->file_path) }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Path</small>
                        <div class="small text-break">{{ $gallery->file_path }}</div>
                    </div>

                    @if(Storage::disk('public')->exists($gallery->file_path))
                        <div class="mb-3">
                            <small class="text-muted">Ukuran File</small>
                            <div class="fw-bold">{{ formatBytes(Storage::disk('public')->size($gallery->file_path)) }}</div>
                        </div>
                    @endif

                    <div class="mb-3">
                        <small class="text-muted">URL Publik</small>
                        <div class="small">
                            <a href="{{ Storage::url($gallery->file_path) }}" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt me-1"></i>Lihat File
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Aksi
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.content.gallery.edit', $gallery) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit Galeri
                        </a>
                        <a href="{{ Storage::url($gallery->file_path) }}" target="_blank" class="btn btn-info">
                            <i class="fas fa-download me-2"></i>Download File
                        </a>
                        <button type="button" class="btn btn-danger" onclick="deleteGallery('{{ $gallery->id }}', '{{ addslashes($gallery->title) }}')">
                            <i class="fas fa-trash me-2"></i>Hapus Galeri
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.content-text {
    line-height: 1.6;
    color: #495057;
}

.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

video {
    background: #000;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteGallery(id, title) {
    Swal.fire({
        title: 'Hapus Galeri?',
        text: `Apakah Anda yakin ingin menghapus galeri "${title}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus galeri',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send AJAX DELETE request
            $.ajax({
                url: `/admin/content/gallery/${id}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Galeri berhasil dihapus.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = '/admin/content/gallery';
                    });
                },
                error: function(xhr) {
                    console.error('Delete error:', xhr);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus galeri: ' + (xhr.responseJSON?.message || 'Unknown error'),
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

// Helper function for file size formatting
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
</script>
@endpush

@php
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
@endphp
