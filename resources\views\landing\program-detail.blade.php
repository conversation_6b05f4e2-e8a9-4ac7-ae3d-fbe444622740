@extends('layouts.landing')

@section('title', $program->name)
@section('description', $program->description)

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 120px 0 80px;
        text-align: center;
    }

    .page-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .program-info-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .program-info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .info-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }



    .stats-card {
        text-align: center;
        padding: 2rem;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 15px;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 2rem;
    }

    .breadcrumb-item a {
        color: white;
        text-decoration: none;
    }

    .breadcrumb-item.active {
        color: rgba(255, 255, 255, 0.8);
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .program-info-card {
            padding: 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item"><a href="{{ route('landing') }}">Beranda</a></li>
                <li class="breadcrumb-item"><a href="{{ route('landing') }}#programs">Program</a></li>
                <li class="breadcrumb-item active">{{ $program->name }}</li>
            </ol>
        </nav>
        
        <h1 class="page-title" data-aos="fade-up">{{ $program->name }}</h1>
        <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            {{ $program->description }}
        </p>
    </div>
</section>

<!-- Program Details -->
<section class="section">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Program Overview -->
                <div class="program-info-card" data-aos="fade-up">
                    <div class="info-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>Tentang Program {{ $program->name }}</h3>
                    <p class="text-muted mb-4">{{ $program->description }}</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-book me-2 text-primary"></i>Kurikulum</h5>
                            <p>{{ $program->curriculum }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-clock me-2 text-primary"></i>Durasi</h5>
                            <p>{{ $program->duration_years }} Tahun</p>
                        </div>
                    </div>
                </div>



                <!-- Requirements -->
                @if($program->requirements)
                    <div class="program-info-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="info-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <h3>Persyaratan</h3>
                        <p class="text-muted mb-3">Persyaratan untuk mengikuti program ini:</p>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ $program->requirements }}
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Program Stats -->
                <div class="stats-card" data-aos="fade-left">
                    <div class="stats-number">{{ $program->capacity }}</div>
                    <div>Kapasitas Siswa</div>
                </div>

                <div class="stats-card" data-aos="fade-left" data-aos-delay="100">
                    <div class="stats-number">{{ $program->current_students }}</div>
                    <div>Siswa Saat Ini</div>
                </div>

                <div class="stats-card" data-aos="fade-left" data-aos-delay="200">
                    <div class="stats-number">{{ $program->capacity - $program->current_students }}</div>
                    <div>Sisa Kuota</div>
                </div>

                <!-- Program Info -->
                <div class="program-info-card" data-aos="fade-left" data-aos-delay="300">
                    <h5><i class="fas fa-info-circle me-2 text-primary"></i>Informasi Program</h5>
                    <hr>
                    <div class="mb-3">
                        <strong>Jenjang:</strong><br>
                        <span class="badge bg-primary">{{ strtoupper($program->level) }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Tipe:</strong><br>
                        <span class="badge bg-secondary">{{ ucfirst($program->type) }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong><br>
                        @if($program->is_active)
                            <span class="badge bg-success">Aktif</span>
                        @else
                            <span class="badge bg-danger">Tidak Aktif</span>
                        @endif
                    </div>
                </div>

                <!-- Contact CTA -->
                <div class="program-info-card text-center" data-aos="fade-left" data-aos-delay="400">
                    <h5>Tertarik dengan Program Ini?</h5>
                    <p class="text-muted">Hubungi kami untuk informasi lebih lanjut</p>
                    <a href="{{ route('landing.contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>Hubungi Kami
                    </a>
                </div>
            </div>
        </div>

        <!-- Back to Programs -->
        <div class="text-center mt-5" data-aos="fade-up">
            <a href="{{ route('landing') }}#programs" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Kembali ke Program Lainnya
            </a>
        </div>
    </div>
</section>
@endsection
