@extends('layouts.landing')

@section('title', 'Cek Status Pendaftaran - PPDB Online')
@section('description', 'Cek status pendaftaran PPDB Online Anda dengan nomor pendaftaran dan NIK.')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- Header -->
            <div class="text-center mb-5">
                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-search fa-2x text-primary"></i>
                </div>
                <h1 class="h3 fw-bold">Cek Status Pendaftaran</h1>
                <p class="text-muted">Masukkan nomor pendaftaran dan NIK untuk melihat status pendaftaran PPDB Anda</p>
            </div>
            
            <!-- Form -->
            <div class="card border-0 shadow">
                <div class="card-body p-4">
                    @if(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>{{ session('error') }}
                        </div>
                    @endif

                    <form action="{{ route('ppdb.check-status.post') }}" method="POST">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="registration_number" class="form-label">Nomor Pendaftaran <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('registration_number') is-invalid @enderror" 
                                   id="registration_number" name="registration_number" 
                                   value="{{ old('registration_number') }}" 
                                   placeholder="Contoh: PPDB20240001" required>
                            @error('registration_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Nomor pendaftaran yang Anda terima setelah mendaftar
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="nik" class="form-label">NIK <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('nik') is-invalid @enderror" 
                                   id="nik" name="nik" value="{{ old('nik') }}" 
                                   placeholder="16 digit NIK" maxlength="16" required>
                            @error('nik')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                NIK yang Anda gunakan saat mendaftar
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Cek Status
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card border-0 bg-light mt-4">
                <div class="card-body p-4">
                    <h6 class="card-title">
                        <i class="fas fa-question-circle me-2 text-primary"></i>Bantuan
                    </h6>
                    <div class="row">
                        <div class="col-12">
                            <p class="card-text mb-2"><strong>Tidak ingat nomor pendaftaran?</strong></p>
                            <ul class="small text-muted mb-3">
                                <li>Cek email yang Anda gunakan saat mendaftar</li>
                                <li>Nomor pendaftaran dikirim otomatis setelah pendaftaran berhasil</li>
                                <li>Format nomor: PPDB[TAHUN][NOMOR URUT]</li>
                            </ul>
                            
                            <p class="card-text mb-2"><strong>Masalah lain?</strong></p>
                            <p class="small text-muted mb-0">
                                Hubungi kami di <strong>(*************</strong> atau 
                                <strong><EMAIL></strong>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div class="text-center mt-4">
                <div class="d-flex flex-column flex-sm-row gap-2 justify-content-center">
                    <a href="{{ route('ppdb.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-2"></i>Daftar Baru
                    </a>
                    <a href="{{ route('login') }}" class="btn btn-outline-success">
                        <i class="fas fa-sign-in-alt me-2"></i>Login Portal
                    </a>
                    <a href="{{ route('landing') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>Beranda
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // NIK validation
    const nikInput = document.getElementById('nik');
    nikInput.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 16);
    });
    
    // Registration number formatting
    const regInput = document.getElementById('registration_number');
    regInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
});
</script>
@endpush
