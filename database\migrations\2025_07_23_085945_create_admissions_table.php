<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admissions', function (Blueprint $table) {
            $table->id();
            $table->string('application_number')->unique();

            // Student Information
            $table->string('student_name');
            $table->string('student_nisn')->nullable();
            $table->string('birth_place');
            $table->date('birth_date');
            $table->enum('gender', ['L', 'P']);
            $table->string('religion');
            $table->text('address');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();

            // Parent Information
            $table->string('father_name');
            $table->string('father_job')->nullable();
            $table->string('father_education')->nullable();
            $table->string('father_phone')->nullable();
            $table->string('mother_name');
            $table->string('mother_job')->nullable();
            $table->string('mother_education')->nullable();
            $table->string('mother_phone')->nullable();
            $table->string('guardian_name')->nullable();
            $table->string('guardian_phone')->nullable();

            // Academic Information
            $table->string('previous_school');
            $table->year('graduation_year');
            $table->decimal('average_grade', 4, 2)->nullable();
            $table->json('achievements')->nullable(); // Store achievements as JSON

            // Program Selection
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            $table->foreignId('program_id_2')->nullable()->constrained('programs')->onDelete('set null');
            $table->text('reason')->nullable();

            // Documents
            $table->json('documents')->nullable(); // Store document paths as JSON

            // Payment
            $table->decimal('registration_fee', 15, 2)->default(0);
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending');
            $table->string('payment_proof')->nullable();
            $table->timestamp('payment_date')->nullable();

            // Application Status
            $table->enum('status', ['draft', 'submitted', 'under_review', 'interview_scheduled', 'accepted', 'rejected', 'waitlisted'])->default('draft');
            $table->text('notes')->nullable();
            $table->timestamp('interview_date')->nullable();
            $table->string('interview_location')->nullable();

            // System fields
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('reviewed_at')->nullable();

            $table->timestamps();

            $table->index(['status', 'submitted_at']);
            $table->index(['payment_status', 'payment_date']);
            $table->index('program_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admissions');
    }
};
