@extends('layouts.dashboard')

@section('title', 'Dashboard Keamanan')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Dashboard Keamanan</h1>
    <div>
        <a href="{{ route('admin.security.index') }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-list me-2"></i>Lihat Semua Log
        </a>
        <a href="{{ route('admin.security.export') }}" class="btn btn-outline-success">
            <i class="fas fa-download me-2"></i>Export Data
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['total_logs']) }}</div>
            <div class="stats-label">Total Log</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['today_logs']) }}</div>
            <div class="stats-label">Log Hari Ini</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['high_risk_logs']) }}</div>
            <div class="stats-label">High Risk</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['unresolved_logs']) }}</div>
            <div class="stats-label">Belum Resolved</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Activity Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Aktivitas 30 Hari Terakhir</h5>
            </div>
            <div class="card-body">
                <canvas id="activityChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Event Type Distribution -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Distribusi Tipe Event</h5>
            </div>
            <div class="card-body">
                <canvas id="eventTypeChart"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent High Risk Activities -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Aktivitas High Risk Terbaru</h5>
            </div>
            <div class="card-body">
                @if($recentHighRisk->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentHighRisk as $log)
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ $log->event_type }}</div>
                                <small class="text-muted">
                                    {{ $log->user ? $log->user->name : 'Unknown' }} - {{ $log->ip_address }}
                                </small>
                                <br>
                                <small class="text-muted">{{ $log->created_at->diffForHumans() }}</small>
                            </div>
                            <span class="badge bg-danger rounded-pill">High Risk</span>
                        </div>
                        @endforeach
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.security.index', ['risk_level' => 'high']) }}" class="btn btn-outline-primary btn-sm">
                            Lihat Semua High Risk
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h6 class="text-muted">Tidak ada aktivitas high risk</h6>
                        <p class="text-muted">Sistem aman dari ancaman tinggi</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Today's Login Activities -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Login Hari Ini</h5>
            </div>
            <div class="card-body">
                @if($todayLogins->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($todayLogins as $log)
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ $log->user ? $log->user->name : 'Unknown' }}</div>
                                <small class="text-muted">{{ $log->ip_address }}</small>
                                <br>
                                <small class="text-muted">{{ $log->created_at->format('H:i:s') }}</small>
                            </div>
                            <span class="badge bg-success rounded-pill">Login</span>
                        </div>
                        @endforeach
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.security.index', ['event_type' => 'login', 'date_from' => today()->format('Y-m-d')]) }}" class="btn btn-outline-primary btn-sm">
                            Lihat Semua Login
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-sign-in-alt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Belum ada login hari ini</h6>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Failed Login Attempts -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Percobaan Login Gagal (7 Hari Terakhir)</h5>
            </div>
            <div class="card-body">
                @if($failedLogins->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>IP Address</th>
                                    <th>User Agent</th>
                                    <th>Waktu</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($failedLogins as $log)
                                <tr>
                                    <td>
                                        <strong>{{ $log->user ? $log->user->name : 'Unknown' }}</strong>
                                        @if($log->user)
                                            <br><small class="text-muted">{{ $log->user->email }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $log->ip_address }}</td>
                                    <td>
                                        <small>{{ Str::limit($log->user_agent, 50) }}</small>
                                    </td>
                                    <td>
                                        <small>{{ $log->created_at->format('d M Y H:i') }}</small>
                                    </td>
                                    <td>
                                        @if($log->is_resolved)
                                            <span class="badge bg-success">Resolved</span>
                                        @else
                                            <span class="badge bg-warning">Pending</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.security.index', ['event_type' => 'failed_login']) }}" class="btn btn-outline-primary">
                            Lihat Semua Failed Login
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h6 class="text-muted">Tidak ada percobaan login gagal</h6>
                        <p class="text-muted">Semua login berhasil dalam 7 hari terakhir</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.bg-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Activity Chart
    const activityCtx = document.getElementById('activityChart').getContext('2d');
    const activityData = @json($dailyActivity);
    
    const activityChart = new Chart(activityCtx, {
        type: 'line',
        data: {
            labels: activityData.map(item => item.date),
            datasets: [{
                label: 'Aktivitas Harian',
                data: activityData.map(item => item.count),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Event Type Chart
    const eventTypeCtx = document.getElementById('eventTypeChart').getContext('2d');
    const eventTypeData = @json($eventTypeStats);
    
    const eventTypeChart = new Chart(eventTypeCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(eventTypeData),
            datasets: [{
                data: Object.values(eventTypeData),
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#17a2b8',
                    '#6f42c1'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endpush
