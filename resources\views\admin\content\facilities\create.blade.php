@extends('layouts.dashboard')

@section('title', 'Tambah Fasilitas')

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Tambah Fasilitas</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="#">Manajemen Konten</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.content.facilities.index') }}">Fasilitas</a></li>
                    <li class="breadcrumb-item active">Tambah Fasilitas</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.content.facilities.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>Form Tambah Fasilitas
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.content.facilities.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <div class="mb-3">
                            <label for="name" class="form-label">Nama Fasilitas <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Kategori <span class="text-danger">*</span></label>
                                    <select class="form-select @error('category') is-invalid @enderror" id="category" name="category" required>
                                        <option value="">Pilih Kategori</option>
                                        <option value="academic" {{ old('category') == 'academic' ? 'selected' : '' }}>Akademik</option>
                                        <option value="sports" {{ old('category') == 'sports' ? 'selected' : '' }}>Olahraga</option>
                                        <option value="library" {{ old('category') == 'library' ? 'selected' : '' }}>Perpustakaan</option>
                                        <option value="laboratory" {{ old('category') == 'laboratory' ? 'selected' : '' }}>Laboratorium</option>
                                        <option value="dormitory" {{ old('category') == 'dormitory' ? 'selected' : '' }}>Asrama</option>
                                        <option value="cafeteria" {{ old('category') == 'cafeteria' ? 'selected' : '' }}>Kantin</option>
                                        <option value="mosque" {{ old('category') == 'mosque' ? 'selected' : '' }}>Masjid</option>
                                        <option value="other" {{ old('category') == 'other' ? 'selected' : '' }}>Lainnya</option>
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Kapasitas</label>
                                    <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                           id="capacity" name="capacity" 
                                           value="{{ old('capacity') }}" 
                                           min="1" placeholder="Contoh: 50">
                                    @error('capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="specifications" class="form-label">Spesifikasi</label>
                            <textarea class="form-control @error('specifications') is-invalid @enderror" 
                                      id="specifications" name="specifications" rows="4">{{ old('specifications') }}</textarea>
                            @error('specifications')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="features" class="form-label">Fitur-fitur</label>
                            <div id="features-container">
                                <div class="input-group mb-2 feature-item">
                                    <input type="text" class="form-control" name="features[]" placeholder="Masukkan fitur">
                                    <button type="button" class="btn btn-outline-danger remove-feature">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="add-feature">
                                <i class="fas fa-plus me-1"></i>Tambah Fitur
                            </button>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Gambar Fasilitas</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Format: JPG, PNG, GIF. Maksimal 2MB.</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_available" name="is_available" value="1" 
                                               {{ old('is_available', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_available">
                                            Fasilitas Tersedia
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" 
                                               {{ old('is_featured') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            Fasilitas Unggulan
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('admin.content.facilities.index') }}" class="btn btn-secondary me-2">Batal</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Simpan Fasilitas
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Card -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Bantuan
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="mb-0 ps-3">
                            <li>Nama fasilitas harus unik dan deskriptif</li>
                            <li>Pilih kategori yang sesuai untuk memudahkan pencarian</li>
                            <li>Kapasitas menunjukkan jumlah maksimal pengguna</li>
                            <li>Spesifikasi berisi detail teknis fasilitas</li>
                            <li>Fitur-fitur adalah keunggulan yang dimiliki</li>
                            <li>Centang "Tersedia" jika fasilitas dapat digunakan</li>
                            <li>Centang "Unggulan" untuk menampilkan di halaman utama</li>
                        </ul>
                    </small>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Preview
                    </h6>
                </div>
                <div class="card-body">
                    <div id="image-preview" style="display: none;">
                        <img id="preview-img" src="" alt="Preview" class="img-fluid rounded shadow-sm" style="max-height: 200px;">
                    </div>
                    <div id="no-preview" class="text-center text-muted">
                        <i class="fas fa-image fa-3x mb-2"></i>
                        <p>Pilih gambar untuk melihat preview</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Add feature functionality
    $('#add-feature').click(function() {
        const newFeature = `
            <div class="input-group mb-2 feature-item">
                <input type="text" class="form-control" name="features[]" placeholder="Masukkan fitur">
                <button type="button" class="btn btn-outline-danger remove-feature">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        $('#features-container').append(newFeature);
    });

    // Remove feature functionality
    $(document).on('click', '.remove-feature', function() {
        if ($('.feature-item').length > 1) {
            $(this).closest('.feature-item').remove();
        }
    });

    // Image preview functionality
    $('#image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').show();
                $('#no-preview').hide();
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').hide();
            $('#no-preview').show();
        }
    });
});
</script>
@endpush
