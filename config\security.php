<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains all security-related configuration for the application.
    | These settings control various security features and protection mechanisms.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Password Policy
    |--------------------------------------------------------------------------
    |
    | Configuration for password strength requirements and validation rules.
    |
    */
    'password' => [
        'min_length' => 8,
        'max_length' => 128,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => true,
        'min_unique_chars' => 6,
        'max_repeated_chars' => 2,
        'check_common_passwords' => true,
        'check_sequential_chars' => true,
        'check_keyboard_patterns' => true,
        'forbidden_patterns' => [
            'password', '123456', 'qwerty', 'admin', 'user', 'login',
            'sekolah', 'siswa', 'guru', 'admin123', 'password123'
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Account Lockout Policy
    |--------------------------------------------------------------------------
    |
    | Configuration for account lockout after failed login attempts.
    |
    */
    'lockout' => [
        'max_attempts' => 3,
        'lockout_duration' => 30, // minutes
        'reset_attempts_after' => 60, // minutes
        'notify_admin' => true,
        'log_attempts' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for rate limiting different types of requests.
    |
    */
    'rate_limits' => [
        'login' => [
            'max_attempts' => 5,
            'decay_minutes' => 15,
        ],
        'register' => [
            'max_attempts' => 3,
            'decay_minutes' => 60,
        ],
        'password_reset' => [
            'max_attempts' => 3,
            'decay_minutes' => 60,
        ],
        'api' => [
            'max_attempts' => 60,
            'decay_minutes' => 1,
        ],
        'upload' => [
            'max_attempts' => 10,
            'decay_minutes' => 10,
        ],
        'general' => [
            'max_attempts' => 30,
            'decay_minutes' => 5,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security
    |--------------------------------------------------------------------------
    |
    | Configuration for session security and management.
    |
    */
    'session' => [
        'regenerate_interval' => 1800, // 30 minutes
        'max_lifetime' => 3600, // 60 minutes
        'strict_ip_check' => false, // Allow IP changes within same subnet
        'strict_user_agent_check' => true,
        'force_logout_on_suspicious_activity' => true,
        'log_session_events' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Verification
    |--------------------------------------------------------------------------
    |
    | Configuration for email verification security.
    |
    */
    'email_verification' => [
        'token_expiry_hours' => 2,
        'resend_cooldown_minutes' => 5,
        'max_resend_attempts' => 3,
        'hash_tokens' => true,
        'log_verification_attempts' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Reset
    |--------------------------------------------------------------------------
    |
    | Configuration for password reset security.
    |
    */
    'password_reset' => [
        'token_expiry_minutes' => 30,
        'resend_cooldown_minutes' => 15,
        'max_reset_attempts' => 3,
        'hash_tokens' => true,
        'clear_all_sessions' => true,
        'log_reset_attempts' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    |
    | Configuration for secure file uploads.
    |
    */
    'file_upload' => [
        'max_file_size' => 10485760, // 10MB in bytes
        'allowed_image_types' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
        'allowed_document_types' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv'],
        'scan_for_malware' => true,
        'check_file_signatures' => true,
        'quarantine_suspicious_files' => true,
        'log_upload_attempts' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    |
    | Configuration for HTTP security headers.
    |
    */
    'headers' => [
        'content_security_policy' => [
            'enabled' => true,
            'report_only' => false,
            'report_uri' => '/csp-report',
        ],
        'strict_transport_security' => [
            'enabled' => true,
            'max_age' => 31536000, // 1 year
            'include_subdomains' => true,
            'preload' => true,
        ],
        'x_frame_options' => 'DENY',
        'x_content_type_options' => 'nosniff',
        'x_xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'permissions_policy' => [
            'camera' => 'none',
            'microphone' => 'none',
            'geolocation' => 'none',
            'payment' => 'none',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Logging
    |--------------------------------------------------------------------------
    |
    | Configuration for security event logging and monitoring.
    |
    */
    'logging' => [
        'enabled' => true,
        'log_successful_logins' => true,
        'log_failed_logins' => true,
        'log_suspicious_activities' => true,
        'log_file_uploads' => true,
        'log_rate_limit_violations' => true,
        'retention_days' => 90,
        'alert_on_high_risk_events' => true,
        'email_alerts' => [
            'enabled' => false,
            'recipients' => ['<EMAIL>'],
            'threshold' => 5, // Send alert after 5 high-risk events
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Input Validation
    |--------------------------------------------------------------------------
    |
    | Configuration for input validation and sanitization.
    |
    */
    'input_validation' => [
        'strict_mode' => true,
        'sanitize_html' => true,
        'check_sql_injection' => true,
        'check_xss_patterns' => true,
        'check_command_injection' => true,
        'max_input_length' => 10000,
        'blocked_patterns' => [
            'script', 'javascript:', 'vbscript:', 'onload', 'onerror',
            'union select', 'drop table', 'delete from', 'insert into',
            'exec(', 'system(', 'shell_exec(', 'passthru(',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Double Login Prevention
    |--------------------------------------------------------------------------
    |
    | Configuration for preventing multiple simultaneous logins.
    |
    */
    'double_login_prevention' => [
        'enabled' => true,
        'terminate_other_sessions' => true,
        'show_warning_message' => true,
        'log_multiple_sessions' => true,
        'allow_admin_multiple_sessions' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration for real-time security monitoring and alerts.
    |
    */
    'monitoring' => [
        'enabled' => true,
        'track_failed_logins' => true,
        'track_suspicious_ips' => true,
        'track_unusual_user_agents' => true,
        'auto_block_suspicious_ips' => false,
        'suspicious_activity_threshold' => 10,
        'monitoring_window_minutes' => 60,
    ],

];
