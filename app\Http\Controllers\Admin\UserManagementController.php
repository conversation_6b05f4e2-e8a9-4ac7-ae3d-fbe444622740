<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\StaffProfile;
use App\Models\StudentProfile;
use App\Models\ParentProfile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use App\Rules\StrongPassword;

class UserManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:super_admin');
    }

    public function index(Request $request)
    {
        $query = User::query();

        // Filter by user type
        if ($request->filled('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%")
                  ->orWhere('nisn', 'like', "%{$search}%")
                  ->orWhere('nip', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(20);

        $stats = [
            'total_users' => User::count(),
            'total_students' => User::where('user_type', 'siswa')->count(),
            'total_teachers' => User::where('user_type', 'guru')->count(),
            'total_parents' => User::where('user_type', 'orang_tua')->count(),
            'total_admins' => User::where('user_type', 'admin')->count(),
        ];

        return view('admin.users.index', compact('users', 'stats'));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'username' => 'nullable|string|max:255|unique:users',
            'user_type' => 'required|in:super_admin,admin,guru,siswa,orang_tua,calon_siswa',
            'password' => ['required', 'string', 'confirmed', new StrongPassword()],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:male,female',
            'nisn' => 'nullable|string|max:20|unique:users',
            'nip' => 'nullable|string|max:20|unique:users',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
        ]);

        // Handle avatar upload
        $avatarPath = null;
        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username,
            'user_type' => $request->user_type,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'address' => $request->address,
            'birth_date' => $request->birth_date,
            'gender' => $request->gender,
            'nisn' => $request->nisn,
            'nip' => $request->nip,
            'avatar' => $avatarPath,
            'is_active' => $request->boolean('is_active', true),
            'email_verified_at' => now(),
        ]);

        // Assign role based on user type
        $user->assignRole($request->user_type);

        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil ditambahkan.');
    }

    public function show(User $user)
    {
        $user->load(['staffProfile', 'studentProfile', 'parentProfile']);
        return view('admin.users.show', compact('user'));
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'username' => ['nullable', 'string', 'max:255', Rule::unique('users')->ignore($user->id)],
            'user_type' => 'required|in:super_admin,admin,guru,siswa,orang_tua,calon_siswa',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:male,female',
            'nisn' => ['nullable', 'string', 'max:20', Rule::unique('users')->ignore($user->id)],
            'nip' => ['nullable', 'string', 'max:20', Rule::unique('users')->ignore($user->id)],
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
        ]);

        // Handle avatar upload
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username,
            'user_type' => $request->user_type,
            'phone' => $request->phone,
            'address' => $request->address,
            'birth_date' => $request->birth_date,
            'gender' => $request->gender,
            'nisn' => $request->nisn,
            'nip' => $request->nip,
            'is_active' => $request->boolean('is_active', true),
        ];

        // Handle avatar upload if new file is provided
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store new avatar
            $updateData['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        $user->update($updateData);

        // Update role if user type changed
        if ($user->wasChanged('user_type')) {
            $user->syncRoles([$request->user_type]);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil diperbarui.');
    }

    public function destroy(User $user)
    {
        try {
            \Log::info('Delete User Request', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'auth_user_id' => Auth::id()
            ]);

            // Prevent deleting own account
            if ($user->id === Auth::id()) {
                $message = 'Anda tidak dapat menghapus akun sendiri.';

                if (request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 403);
                }

                return redirect()->route('admin.users.index')->with('error', $message);
            }

            // Prevent deleting super admin if current user is not super admin
            if ($user->user_type === 'super_admin' && Auth::user()->user_type !== 'super_admin') {
                $message = 'Anda tidak memiliki izin untuk menghapus Super Admin.';

                if (request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 403);
                }

                return redirect()->route('admin.users.index')->with('error', $message);
            }

            $userName = $user->name;

            // Delete avatar file if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            $user->delete();

            \Log::info('Delete User Success', [
                'user_id' => $user->id,
                'user_name' => $userName,
                'deleted_by' => Auth::id()
            ]);

            $message = "User {$userName} berhasil dihapus.";

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message
                ]);
            }

            return redirect()->route('admin.users.index')->with('success', $message);
        } catch (\Exception $e) {
            \Log::error('Delete User Error', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $message = 'Terjadi kesalahan saat menghapus user: ' . $e->getMessage();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $message
                ], 500);
            }

            return redirect()->route('admin.users.index')->with('error', $message);
        }
    }

    public function toggleStatus(User $user)
    {
        try {
            \Log::info('Toggle Status Request', [
                'user_id' => $user->id,
                'current_status' => $user->is_active,
                'auth_user_id' => Auth::id()
            ]);

            // Prevent super admin from deactivating themselves
            if ($user->id === Auth::id() && $user->user_type === 'super_admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak dapat menonaktifkan akun Anda sendiri.'
                ], 403);
            }

            $oldStatus = $user->is_active;
            $user->is_active = !$user->is_active;
            $saved = $user->save();

            \Log::info('Toggle Status Result', [
                'user_id' => $user->id,
                'old_status' => $oldStatus,
                'new_status' => $user->is_active,
                'saved' => $saved
            ]);

            $statusText = $user->is_active ? 'diaktifkan' : 'dinonaktifkan';

            return response()->json([
                'success' => true,
                'message' => "Status user berhasil {$statusText}.",
                'is_active' => $user->is_active
            ]);
        } catch (\Exception $e) {
            \Log::error('Toggle Status Error', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengubah status user: ' . $e->getMessage()
            ], 500);
        }
    }

    public function resetPassword(Request $request, User $user)
    {
        try {
            $request->validate([
                'password' => 'required|string|min:8|confirmed',
            ]);

            $user->update([
                'password' => Hash::make($request->password),
            ]);

            return redirect()->route('admin.users.show', $user)
                ->with('success', 'Password berhasil direset.');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat mereset password.');
        }
    }

    /**
     * Lock user account
     */
    public function lockAccount(User $user)
    {
        try {
            \Log::info('Lock Account Request', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'auth_user_id' => Auth::id()
            ]);

            // Prevent locking own account
            if ($user->id === Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak dapat mengunci akun Anda sendiri.'
                ], 403);
            }

            // Prevent locking super admin if current user is not super admin
            if ($user->user_type === 'super_admin' && Auth::user()->user_type !== 'super_admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki izin untuk mengunci akun Super Admin.'
                ], 403);
            }

            // Lock account for 24 hours
            $user->locked_until = now()->addHours(24);
            $user->failed_login_attempts = 5; // Set to max attempts
            $user->save();

            \Log::info('Lock Account Success', [
                'user_id' => $user->id,
                'locked_until' => $user->locked_until,
                'locked_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Akun {$user->name} berhasil dikunci hingga {$user->locked_until->format('d M Y H:i')}.",
                'locked_until' => $user->locked_until->format('d M Y H:i')
            ]);
        } catch (\Exception $e) {
            \Log::error('Lock Account Error', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengunci akun: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unlock user account
     */
    public function unlockAccount(User $user)
    {
        try {
            \Log::info('Unlock Account Request', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'auth_user_id' => Auth::id()
            ]);

            // Reset lock fields
            $user->locked_until = null;
            $user->failed_login_attempts = 0;
            $user->save();

            \Log::info('Unlock Account Success', [
                'user_id' => $user->id,
                'unlocked_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Akun {$user->name} berhasil dibuka kembali."
            ]);
        } catch (\Exception $e) {
            \Log::error('Unlock Account Error', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuka akun: ' . $e->getMessage()
            ], 500);
        }
    }
}
