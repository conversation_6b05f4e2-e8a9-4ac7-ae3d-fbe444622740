<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ppdb_settings', function (Blueprint $table) {
            $table->id();
            $table->string('academic_year'); // Tahun ajaran
            $table->enum('status', ['closed', 'open', 'maintenance'])->default('closed');
            $table->datetime('registration_start')->nullable();
            $table->datetime('registration_end')->nullable();
            $table->datetime('test_date')->nullable();
            $table->datetime('announcement_date')->nullable();
            $table->decimal('registration_fee', 10, 2)->default(0);
            $table->json('required_documents')->nullable(); // List dokumen yang diperlukan
            $table->json('program_quotas')->nullable(); // <PERSON><PERSON> per program
            $table->text('requirements')->nullable(); // Persyaratan pendaftaran
            $table->text('announcement_text')->nullable(); // Teks pengumuman
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ppdb_settings');
    }
};
