@extends('layouts.dashboard')

@section('title', 'Log <PERSON>')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Log <PERSON></h1>
    <div>
        <a href="{{ route('admin.security.dashboard') }}" class="btn btn-outline-info me-2">
            <i class="fas fa-chart-line me-2"></i>Dashboard Keamanan
        </a>
        <a href="{{ route('admin.security.export') }}" class="btn btn-outline-success">
            <i class="fas fa-download me-2"></i>Export Data
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['total_logs']) }}</div>
            <div class="stats-label">Total Log</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['today_logs']) }}</div>
            <div class="stats-label">Log Hari Ini</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['high_risk']) }}</div>
            <div class="stats-label">High Risk</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['unresolved']) }}</div>
            <div class="stats-label">Belum Resolved</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.security.index') }}" class="row g-3">
            <div class="col-md-2">
                <label for="event_type" class="form-label">Tipe Event</label>
                <select name="event_type" id="event_type" class="form-select">
                    <option value="">Semua Tipe</option>
                    @foreach($eventTypes as $type)
                        <option value="{{ $type }}" {{ request('event_type') == $type ? 'selected' : '' }}>
                            {{ ucfirst(str_replace('_', ' ', $type)) }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label for="risk_level" class="form-label">Risk Level</label>
                <select name="risk_level" id="risk_level" class="form-select">
                    <option value="">Semua Level</option>
                    <option value="low" {{ request('risk_level') == 'low' ? 'selected' : '' }}>Low</option>
                    <option value="medium" {{ request('risk_level') == 'medium' ? 'selected' : '' }}>Medium</option>
                    <option value="high" {{ request('risk_level') == 'high' ? 'selected' : '' }}>High</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="user_id" class="form-label">User</label>
                <select name="user_id" id="user_id" class="form-select">
                    <option value="">Semua User</option>
                    @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                            {{ $user->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">Dari Tanggal</label>
                <input type="date" name="date_from" id="date_from" class="form-control" 
                       value="{{ request('date_from') }}">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">Sampai Tanggal</label>
                <input type="date" name="date_to" id="date_to" class="form-control" 
                       value="{{ request('date_to') }}">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ route('admin.security.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Security Logs Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Daftar Log Keamanan</h5>
            <div class="btn-group">
                @if($logs->where('is_resolved', false)->count() > 0)
                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#bulkResolveModal">
                        <i class="fas fa-check-double me-1"></i>Resolve Selected
                    </button>
                @endif
                @if($logs->count() > 0)
                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAllModal">
                        <i class="fas fa-trash-alt me-1"></i>Delete All
                    </button>
                @endif
            </div>
        </div>
    </div>
    <div class="card-body">
        @if($logs->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Waktu</th>
                            <th>User</th>
                            <th>Event Type</th>
                            <th>Deskripsi</th>
                            <th>IP Address</th>
                            <th>Risk Level</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($logs as $log)
                        <tr class="{{ $log->risk_level == 'high' ? 'table-danger' : ($log->risk_level == 'medium' ? 'table-warning' : '') }}">
                            <td>
                                @if(!$log->is_resolved)
                                    <input type="checkbox" name="log_ids[]" value="{{ $log->id }}" class="form-check-input log-checkbox">
                                @endif
                            </td>
                            <td>
                                <small>{{ $log->created_at->format('d M Y H:i:s') }}</small>
                            </td>
                            <td>
                                @if($log->user)
                                    <div>
                                        <strong>{{ $log->user->name }}</strong>
                                        <br><small class="text-muted">{{ $log->user->email }}</small>
                                    </div>
                                @else
                                    <span class="text-muted">Unknown</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->event_type == 'login' ? 'success' : ($log->event_type == 'failed_login' ? 'danger' : 'info') }}">
                                    {{ ucfirst(str_replace('_', ' ', $log->event_type)) }}
                                </span>
                            </td>
                            <td>
                                <small>{{ Str::limit($log->description, 50) }}</small>
                            </td>
                            <td>
                                <small class="font-monospace">{{ $log->ip_address }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->risk_level == 'high' ? 'danger' : ($log->risk_level == 'medium' ? 'warning' : 'success') }}">
                                    {{ ucfirst($log->risk_level) }}
                                </span>
                            </td>
                            <td>
                                @if($log->is_resolved)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Resolved
                                    </span>
                                @else
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>Pending
                                    </span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.security.show', $log) }}" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(!$log->is_resolved)
                                        <form action="{{ route('admin.security.resolve', $log) }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline-success" title="Resolve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    @endif
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteLog({{ $log->id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan {{ $logs->firstItem() }} - {{ $logs->lastItem() }} dari {{ $logs->total() }} log
                </div>
                {{ $logs->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada log ditemukan</h5>
                <p class="text-muted">Belum ada log keamanan yang sesuai dengan filter yang dipilih.</p>
            </div>
        @endif
    </div>
</div>

<!-- Bulk Resolve Modal -->
<div class="modal fade" id="bulkResolveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resolve Multiple Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menandai log yang dipilih sebagai resolved?</p>
                <p class="text-info"><small>Log yang sudah resolved tidak dapat dikembalikan ke status pending.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form id="bulkResolveForm" action="{{ route('admin.security.bulk-resolve') }}" method="POST" style="display: inline;">
                    @csrf
                    <input type="hidden" name="log_ids" id="selectedLogIds">
                    <button type="submit" class="btn btn-warning">Resolve Selected</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete All Modal -->
<div class="modal fade" id="deleteAllModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Hapus Semua Log Keamanan
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>PERINGATAN!</strong> Tindakan ini akan menghapus SEMUA log keamanan secara permanen dan tidak dapat dibatalkan.
                </div>
                <p>Untuk melanjutkan, ketik <strong>DELETE_ALL_LOGS</strong> di bawah ini:</p>
                <input type="text" id="deleteConfirmInput" class="form-control" placeholder="Ketik DELETE_ALL_LOGS">
                <div class="mt-3">
                    <small class="text-muted">
                        Total log yang akan dihapus: <strong>{{ $logs->total() }}</strong>
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteAllBtn" onclick="submitDeleteAll()" disabled>
                    <i class="fas fa-trash-alt me-1"></i>Hapus Semua Log
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.bg-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.font-monospace {
    font-family: 'Courier New', monospace;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Select all checkbox
    $('#selectAll').change(function() {
        $('.log-checkbox').prop('checked', $(this).is(':checked'));
    });

    // Update select all when individual checkboxes change
    $('.log-checkbox').change(function() {
        const totalCheckboxes = $('.log-checkbox').length;
        const checkedCheckboxes = $('.log-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Bulk resolve
    $('#bulkResolveModal').on('show.bs.modal', function() {
        const selectedIds = $('.log-checkbox:checked').map(function() {
            return $(this).val();
        }).get();
        
        if (selectedIds.length === 0) {
            toastr.warning('Pilih minimal satu log untuk di-resolve');
            return false;
        }
        
        $('#selectedLogIds').val(JSON.stringify(selectedIds));
    });

    // Delete confirmation input validation
    $('#deleteConfirmInput').on('input', function() {
        const confirmBtn = $('#confirmDeleteAllBtn');
        if ($(this).val() === 'DELETE_ALL_LOGS') {
            confirmBtn.prop('disabled', false);
        } else {
            confirmBtn.prop('disabled', true);
        }
    });

    // Auto-refresh every 30 seconds for real-time updates
    setInterval(function() {
        if (!$('.modal').hasClass('show')) { // Don't refresh if modal is open
            location.reload();
        }
    }, 30000);
});

// Delete individual log
function deleteLog(logId) {
    Swal.fire({
        title: 'Hapus Log Keamanan?',
        text: 'Log yang dihapus tidak dapat dikembalikan!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/admin/security/logs/${logId}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    }
                },
                error: function(xhr) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus log keamanan.',
                        icon: 'error'
                    });
                }
            });
        }
    });
}

// Delete all logs
function submitDeleteAll() {
    $.ajax({
        url: '/admin/security/logs/delete-all',
        type: 'DELETE',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            confirm: 'DELETE_ALL_LOGS'
        },
        success: function(response) {
            if (response.success) {
                $('#deleteAllModal').modal('hide');
                Swal.fire({
                    title: 'Berhasil!',
                    text: response.message,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            }
        },
        error: function(xhr) {
            $('#deleteAllModal').modal('hide');
            Swal.fire({
                title: 'Error!',
                text: 'Gagal menghapus semua log keamanan.',
                icon: 'error'
            });
        }
    });
}
</script>
@endpush
