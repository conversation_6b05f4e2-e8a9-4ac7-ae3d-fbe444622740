<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\SchoolSetting;
use App\Models\SecurityLog;

class SuperAdminController extends Controller
{
    /**
     * Show super admin login form
     */
    public function showLoginForm()
    {
        // Check if super admin is already logged in
        if (Auth::check() && Auth::user()->user_type === 'super_admin') {
            return redirect()->route('super-admin.dashboard');
        }

        // Only show this form during maintenance mode
        $schoolSetting = SchoolSetting::first();
        if (!$schoolSetting || !$schoolSetting->maintenance_mode) {
            return redirect()->route('landing');
        }

        return view('auth.super-admin-login');
    }

    /**
     * Handle super admin login
     */
    public function login(Request $request)
    {
        // Check if super admin is already logged in
        if (Auth::check() && Auth::user()->user_type === 'super_admin') {
            return redirect()->route('super-admin.dashboard');
        }

        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        // Only allow during maintenance mode
        $schoolSetting = SchoolSetting::first();
        if (!$schoolSetting || !$schoolSetting->maintenance_mode) {
            return redirect()->route('landing');
        }

        // Check if user exists and is super admin
        $user = User::where('email', $request->email)
                   ->where('user_type', 'super_admin')
                   ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            SecurityLog::logEvent('failed_super_admin_login', null, [
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'email' => 'Kredensial super admin tidak valid.',
            ])->withInput($request->only('email'));
        }

        // Check if account is locked
        if ($user->isLocked()) {
            SecurityLog::logEvent('locked_super_admin_login_attempt', $user->id, [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'email' => 'Akun super admin dikunci. Hubungi administrator sistem.',
            ]);
        }

        // Login successful
        Auth::login($user);

        SecurityLog::logEvent('successful_super_admin_login', $user->id, [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return redirect()->route('super-admin.dashboard');
    }

    /**
     * Super admin dashboard (only accessible during maintenance)
     */
    public function dashboard()
    {
        // Ensure user is super admin
        if (!Auth::check() || Auth::user()->user_type !== 'super_admin') {
            return redirect()->route('super-admin.login');
        }

        $schoolSetting = SchoolSetting::first();

        // Only accessible during maintenance mode (super admin emergency access)
        if (!$schoolSetting || !$schoolSetting->maintenance_mode) {
            // If not in maintenance mode, redirect to normal admin dashboard
            return redirect()->route('admin.dashboard');
        }
        
        // Get system statistics
        $stats = [
            'total_users' => User::count(),
            'admin_users' => User::where('user_type', 'admin')->count(),
            'teacher_users' => User::where('user_type', 'teacher')->count(),
            'student_users' => User::where('user_type', 'student')->count(),
            'locked_accounts' => User::where('locked_until', '>', now())->count(),
            'maintenance_mode' => $schoolSetting ? $schoolSetting->maintenance_mode : false,
        ];

        // Get recent security logs
        $recentLogs = SecurityLog::with('user')
                                ->latest()
                                ->limit(20)
                                ->get();

        return view('super-admin.dashboard', compact('stats', 'recentLogs', 'schoolSetting'));
    }

    /**
     * Toggle maintenance mode
     */
    public function toggleMaintenance(Request $request)
    {
        // Ensure user is super admin
        if (!Auth::check() || Auth::user()->user_type !== 'super_admin') {
            abort(403);
        }

        $schoolSetting = SchoolSetting::first();
        if (!$schoolSetting) {
            $schoolSetting = SchoolSetting::create([
                'school_name' => 'Sekolah',
                'maintenance_mode' => false,
            ]);
        }

        $newMode = !$schoolSetting->maintenance_mode;
        $schoolSetting->update(['maintenance_mode' => $newMode]);

        SecurityLog::logEvent('maintenance_mode_toggled', Auth::id(), [
            'new_mode' => $newMode,
            'ip' => $request->ip(),
        ]);

        $message = $newMode ? 'Mode maintenance diaktifkan.' : 'Mode maintenance dinonaktifkan.';
        
        return redirect()->back()->with('success', $message);
    }

    /**
     * Super admin logout
     */
    public function logout(Request $request)
    {
        if (Auth::check()) {
            SecurityLog::logEvent('super_admin_logout', Auth::id(), [
                'ip' => $request->ip(),
            ]);

            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        }

        return redirect()->route('maintenance');
    }

    /**
    public function logout(Request $request)
    {
        if (Auth::check()) {
            SecurityLog::logEvent('super_admin_logout', Auth::id(), [
                'ip' => $request->ip(),
            ]);
        }

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('maintenance');
    }

    /**
     * Emergency access to admin panel (only during maintenance)
     */
    public function emergencyAccess()
    {
        // Ensure user is super admin and in maintenance mode
        if (!Auth::check() || Auth::user()->user_type !== 'super_admin') {
            return redirect()->route('super-admin.login');
        }

        $schoolSetting = SchoolSetting::first();
        if (!$schoolSetting || !$schoolSetting->maintenance_mode) {
            return redirect()->route('admin.dashboard');
        }

        SecurityLog::logEvent('emergency_admin_access', Auth::id(), [
            'ip' => request()->ip(),
        ]);

        return redirect()->route('admin.dashboard');
    }
}
