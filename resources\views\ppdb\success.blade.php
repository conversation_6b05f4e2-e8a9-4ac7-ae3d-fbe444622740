@extends('layouts.landing')

@section('title', 'Pendaftaran Berhasil - PPDB Online')
@section('description', 'Pendaftaran PPDB Online Anda telah berhasil disubmit.')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <!-- Success Icon -->
            <div class="mb-4">
                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                    <i class="fas fa-check-circle fa-4x text-success"></i>
                </div>
            </div>
            
            <!-- Success Message -->
            <h1 class="display-5 fw-bold text-success mb-3">Pendaftaran Berhasil!</h1>
            <div class="alert alert-success border-0 shadow-sm">
                <h4 class="alert-heading">
                    <i class="fas fa-party-horn me-2"></i>Selamat!
                </h4>
                <p class="mb-0">Pendaftaran PPDB Online Anda telah berhasil disubmit dan sedang dalam proses verifikasi.</p>
            </div>
            
            <!-- Registration Details -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>Detail Pendaftaran
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row text-start">
                        <div class="col-md-6 mb-3">
                            <strong>Nomor Pendaftaran:</strong>
                            <div class="bg-light p-2 rounded mt-1">
                                <code class="fs-5 text-primary">{{ $registration->registration_number }}</code>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Nama Lengkap:</strong>
                            <div class="mt-1">{{ $registration->full_name }}</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Program Pilihan:</strong>
                            <div class="mt-1">{{ $registration->program->name }}</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Status Pendaftaran:</strong>
                            <div class="mt-1">
                                <span class="badge bg-{{ $registration->status_badge }} fs-6">
                                    {{ $registration->status_label }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Tanggal Pendaftaran:</strong>
                            <div class="mt-1">{{ $registration->created_at->format('d F Y, H:i') }} WIB</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Email:</strong>
                            <div class="mt-1">{{ $registration->email }}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Next Steps -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list-check me-2"></i>Langkah Selanjutnya
                    </h5>
                </div>
                <div class="card-body p-4 text-start">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Yang Perlu Anda Lakukan:</h6>
                            <ol class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-envelope text-success me-2"></i>
                                    <strong>Cek Email Anda</strong><br>
                                    <small class="text-muted">Kami telah mengirim informasi login ke email Anda</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-sign-in-alt text-primary me-2"></i>
                                    <strong>Login ke Portal Calon Siswa</strong><br>
                                    <small class="text-muted">Gunakan email dan password yang dikirim via email</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-upload text-warning me-2"></i>
                                    <strong>Upload Dokumen</strong><br>
                                    <small class="text-muted">Lengkapi semua dokumen yang diperlukan</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-clock text-info me-2"></i>
                                    <strong>Tunggu Verifikasi</strong><br>
                                    <small class="text-muted">Tim kami akan memverifikasi data dan dokumen Anda</small>
                                </li>
                            </ol>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary">Dokumen yang Diperlukan:</h6>
                            <ul class="list-unstyled">
                                @if($registration->ppdbSetting->required_documents)
                                    @foreach($registration->ppdbSetting->required_documents as $doc)
                                    <li class="mb-1">
                                        <i class="fas fa-file-alt text-muted me-2"></i>{{ $doc }}
                                    </li>
                                    @endforeach
                                @else
                                    <li class="mb-1"><i class="fas fa-file-alt text-muted me-2"></i>Ijazah/SKHUN</li>
                                    <li class="mb-1"><i class="fas fa-file-alt text-muted me-2"></i>Kartu Keluarga</li>
                                    <li class="mb-1"><i class="fas fa-file-alt text-muted me-2"></i>Akta Kelahiran</li>
                                    <li class="mb-1"><i class="fas fa-file-alt text-muted me-2"></i>Pas Foto 3x4</li>
                                    <li class="mb-1"><i class="fas fa-file-alt text-muted me-2"></i>Surat Keterangan Sehat</li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Important Notes -->
            <div class="alert alert-warning border-0 mt-4">
                <h6 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>Penting untuk Diingat:
                </h6>
                <ul class="mb-0 text-start">
                    <li>Simpan <strong>Nomor Pendaftaran</strong> Anda dengan baik</li>
                    <li>Cek email secara berkala untuk update status pendaftaran</li>
                    <li>Upload dokumen dalam format PDF atau JPG dengan ukuran maksimal 2MB</li>
                    <li>Pastikan dokumen yang diupload jelas dan dapat dibaca</li>
                    <li>Hubungi kami jika tidak menerima email dalam 24 jam</li>
                </ul>
            </div>
            
            <!-- Action Buttons -->
            <div class="mt-5">
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{{ route('login') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Login ke Portal Calon Siswa
                    </a>
                    <a href="{{ route('ppdb.check-status') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-search me-2"></i>Cek Status Pendaftaran
                    </a>
                </div>
                
                <div class="mt-3">
                    <a href="{{ route('landing') }}" class="btn btn-link">
                        <i class="fas fa-home me-2"></i>Kembali ke Beranda
                    </a>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="mt-5">
                <div class="card border-0 bg-light">
                    <div class="card-body p-4">
                        <h6 class="card-title">
                            <i class="fas fa-headset me-2 text-success"></i>Butuh Bantuan?
                        </h6>
                        <p class="card-text mb-0">
                            Jika Anda mengalami kesulitan atau memiliki pertanyaan, jangan ragu untuk menghubungi kami:
                        </p>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <strong><i class="fas fa-phone text-success me-2"></i>Telepon:</strong><br>
                                <span>(*************</span>
                            </div>
                            <div class="col-md-4">
                                <strong><i class="fas fa-envelope text-primary me-2"></i>Email:</strong><br>
                                <span><EMAIL></span>
                            </div>
                            <div class="col-md-4">
                                <strong><i class="fas fa-clock text-warning me-2"></i>Jam Layanan:</strong><br>
                                <span>08:00 - 16:00 WIB</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-select registration number for easy copying
    const regNumberElement = document.querySelector('code');
    if (regNumberElement) {
        regNumberElement.addEventListener('click', function() {
            const range = document.createRange();
            range.selectNode(this);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
        });
    }
});
</script>
@endpush
