@extends('layouts.dashboard')

@section('title', 'Portal Siswa')
@section('page-title', 'Portal Siswa - Selamat Datang di Portal Siswa')

@section('sidebar-menu')
<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" href="{{ route('profile.show') }}">
            <i class="fas fa-user-circle"></i>
            Profil Saya
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-book"></i>
            <PERSON><PERSON>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-tasks"></i>
            Tugas & Ujian
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-chart-line"></i>
            Nilai & Rapor
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-calendar-alt"></i>
            Jadwal Pelajaran
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-newspaper"></i>
            Berita & Pengumuman
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-graduation-cap"></i>
            Program Saya
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="{{ route('landing') }}" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info border-0" style="background: linear-gradient(135deg, #17a2b8, #138496); color: white;">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-graduation-cap fa-2x"></i>
                </div>
                <div>
                    <h5 class="mb-1">📚 Selamat Datang di Portal Siswa!</h5>
                    <p class="mb-0">
                        Halo, <strong>{{ Auth::user()->name }}</strong>!
                        @if($studentProfile)
                            NISN: {{ Auth::user()->nisn ?? '-' }}
                        @endif
                        <br><small>Akses materi pembelajaran, kerjakan tugas, dan pantau progress akademik Anda di sini.</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Card -->
@if($studentProfile)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-id-card me-2"></i>Profil Siswa
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        @if(Auth::user()->avatar)
                            <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="Avatar" class="rounded-circle mb-3" width="120" height="120" style="object-fit: cover;">
                        @else
                            <div class="rounded-circle bg-gradient-info d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px;">
                                <i class="fas fa-user fa-3x text-white"></i>
                            </div>
                        @endif
                        <h5>{{ Auth::user()->name }}</h5>
                        <p class="text-muted">Siswa Aktif</p>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>NISN:</strong></td>
                                        <td>{{ Auth::user()->nisn ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ Auth::user()->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Telepon:</strong></td>
                                        <td>{{ Auth::user()->phone ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Jenis Kelamin:</strong></td>
                                        <td>{{ Auth::user()->gender == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Kelas:</strong></td>
                                        <td>{{ $studentProfile->class ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tahun Masuk:</strong></td>
                                        <td>{{ $studentProfile->admission_year ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ Auth::user()->is_active ? 'success' : 'danger' }}">
                                                {{ Auth::user()->is_active ? 'Aktif' : 'Nonaktif' }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Bergabung:</strong></td>
                                        <td>{{ Auth::user()->created_at->format('d M Y') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-book"></i>
            </div>
            <div class="stats-number">{{ $stats['my_subjects'] }}</div>
            <div class="stats-label">Mata Pelajaran</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stats-number">{{ $stats['pending_assignments'] }}</div>
            <div class="stats-label">Tugas Pending</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stats-number">{{ $stats['average_grade'] }}</div>
            <div class="stats-label">Rata-rata Nilai</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stats-number">{{ $stats['attendance_rate'] }}%</div>
            <div class="stats-label">Tingkat Kehadiran</div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-newspaper me-2"></i>Berita & Pengumuman Terbaru
            </div>
            <div class="card-body">
                @forelse($latestNews as $news)
                    <div class="d-flex align-items-start mb-3">
                        @if($news->featured_image)
                            <img src="{{ asset('storage/' . $news->featured_image) }}" alt="News" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                        @else
                            <div class="rounded bg-gradient-primary d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-newspaper text-white"></i>
                            </div>
                        @endif
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ Str::limit($news->title, 50) }}</h6>
                            <small class="text-muted">
                                {{ $news->created_at->diffForHumans() }}
                            </small>
                            <br>
                            <span class="badge bg-{{ $news->type == 'announcement' ? 'warning' : 'info' }}">
                                {{ $news->type == 'announcement' ? 'Pengumuman' : ucfirst($news->type) }}
                            </span>
                        </div>
                    </div>
                @empty
                    <p class="text-muted text-center">Belum ada berita terbaru</p>
                @endforelse
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-graduation-cap me-2"></i>Program Pendidikan
            </div>
            <div class="card-body">
                @forelse($myPrograms as $program)
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded bg-gradient-success d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="fas fa-graduation-cap text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ $program->name }}</h6>
                            <small class="text-muted">
                                {{ Str::limit($program->description, 60) }}
                            </small>
                        </div>
                        <span class="badge bg-{{ $program->is_active ? 'success' : 'secondary' }}">
                            {{ $program->is_active ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                @empty
                    <p class="text-muted text-center">Belum ada program tersedia</p>
                @endforelse
            </div>
        </div>
    </div>
</div>

<!-- Academic Overview -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>Ringkasan Akademik
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-book fa-2x text-primary mb-2"></i>
                            <h5>-</h5>
                            <small class="text-muted">Mata Pelajaran</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-tasks fa-2x text-success mb-2"></i>
                            <h5>-</h5>
                            <small class="text-muted">Tugas Pending</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                            <h5>-</h5>
                            <small class="text-muted">Rata-rata Nilai</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-calendar-check fa-2x text-danger mb-2"></i>
                            <h5>-</h5>
                            <small class="text-muted">Kehadiran</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>Aksi Cepat
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-book me-2"></i>Lihat Materi
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-tasks me-2"></i>Kerjakan Tugas
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-line me-2"></i>Lihat Nilai
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-danger w-100">
                            <i class="fas fa-user me-2"></i>Edit Profil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
