@extends('layouts.dashboard')

@section('title', 'Super Admin Dashboard - Emergency Mode')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-danger">
            <i class="fas fa-shield-alt me-2"></i>Super Admin Dashboard
        </h1>
        <p class="text-muted mb-0">Emergency Access Mode - All activities are logged</p>
    </div>
    <div>
        <form action="{{ route('super-admin.toggle-maintenance') }}" method="POST" class="d-inline">
            @csrf
            <button type="submit" class="btn {{ $schoolSetting && $schoolSetting->maintenance_mode ? 'btn-success' : 'btn-warning' }} me-2">
                <i class="fas fa-{{ $schoolSetting && $schoolSetting->maintenance_mode ? 'play' : 'pause' }} me-2"></i>
                {{ $schoolSetting && $schoolSetting->maintenance_mode ? 'Nonaktifkan' : 'Aktifkan' }} Maintenance
            </button>
        </form>
        <a href="{{ route('super-admin.logout') }}" class="btn btn-outline-danger">
            <i class="fas fa-sign-out-alt me-2"></i>Logout
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<!-- Maintenance Status Alert -->
@if($schoolSetting && $schoolSetting->maintenance_mode)
<div class="alert alert-warning" role="alert">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
        <div>
            <h5 class="alert-heading mb-1">Mode Maintenance Aktif</h5>
            <p class="mb-0">Website sedang dalam mode maintenance. Hanya Super Admin yang dapat mengakses sistem.</p>
        </div>
    </div>
</div>
@else
<div class="alert alert-success" role="alert">
    <div class="d-flex align-items-center">
        <i class="fas fa-check-circle fa-2x me-3"></i>
        <div>
            <h5 class="alert-heading mb-1">Website Normal</h5>
            <p class="mb-0">Website beroperasi normal. Semua user dapat mengakses sesuai role mereka.</p>
        </div>
    </div>
</div>
@endif

<!-- System Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h3 class="text-primary">{{ $stats['total_users'] }}</h3>
                <p class="text-muted mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-user-shield fa-2x text-success mb-2"></i>
                <h3 class="text-success">{{ $stats['admin_users'] }}</h3>
                <p class="text-muted mb-0">Admin Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-chalkboard-teacher fa-2x text-info mb-2"></i>
                <h3 class="text-info">{{ $stats['teacher_users'] }}</h3>
                <p class="text-muted mb-0">Teachers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-user-lock fa-2x text-warning mb-2"></i>
                <h3 class="text-warning">{{ $stats['locked_accounts'] }}</h3>
                <p class="text-muted mb-0">Locked Accounts</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Access Admin Panel
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-users-cog me-2"></i>
                            Manage Users
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-cogs me-2"></i>
                            System Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Security Logs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Recent Security Logs
                </h5>
            </div>
            <div class="card-body">
                @if($recentLogs->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Event</th>
                                <th>User</th>
                                <th>IP Address</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentLogs as $log)
                            <tr>
                                <td>
                                    <small class="text-muted">
                                        {{ $log->created_at->format('d/m/Y H:i:s') }}
                                    </small>
                                </td>
                                <td>
                                    @php
                                        $badgeColor = match($log->event_type) {
                                            'login', 'successful_super_admin_login' => 'success',
                                            'logout', 'super_admin_logout' => 'info',
                                            'failed_login', 'failed_super_admin_login', 'access_blocked' => 'danger',
                                            'maintenance_mode_toggled' => 'warning',
                                            default => 'secondary'
                                        };
                                    @endphp
                                    <span class="badge bg-{{ $badgeColor }}">
                                        {{ ucfirst(str_replace('_', ' ', $log->event_type)) }}
                                    </span>
                                </td>
                                <td>
                                    @if($log->user)
                                        <small>
                                            {{ $log->user->name }}<br>
                                            <span class="text-muted">({{ $log->user->user_type }})</span>
                                        </small>
                                    @else
                                        <span class="text-muted">Guest</span>
                                    @endif
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ $log->data['ip'] ?? 'Unknown' }}
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        @if(isset($log->data['route']))
                                            Route: {{ $log->data['route'] }}<br>
                                        @endif
                                        @if(isset($log->data['reason']))
                                            Reason: {{ $log->data['reason'] }}
                                        @endif
                                    </small>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No security logs available</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-refresh page every 30 seconds to show latest logs
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
</script>
@endpush
