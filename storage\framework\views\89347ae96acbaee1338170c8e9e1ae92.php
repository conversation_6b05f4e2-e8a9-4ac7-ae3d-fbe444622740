<?php $__env->startSection('title', 'Manajemen Fasilitas'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Manajemen Fasilitas</h1>
    <a href="<?php echo e(route('admin.content.facilities.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Tambah Fasilitas
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-building"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_facilities']); ?></div>
            <div class="stats-label">Total Fasilitas</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['available']); ?></div>
            <div class="stats-label">Tersedia</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-star"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['featured']); ?></div>
            <div class="stats-label">Featured</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.content.facilities.index')); ?>" class="row g-3">
            <div class="col-md-3">
                <label for="category" class="form-label">Kategori</label>
                <select name="category" id="category" class="form-select">
                    <option value="">Semua Kategori</option>
                    <option value="academic" <?php echo e(request('category') == 'academic' ? 'selected' : ''); ?>>Academic</option>
                    <option value="sports" <?php echo e(request('category') == 'sports' ? 'selected' : ''); ?>>Sports</option>
                    <option value="library" <?php echo e(request('category') == 'library' ? 'selected' : ''); ?>>Library</option>
                    <option value="laboratory" <?php echo e(request('category') == 'laboratory' ? 'selected' : ''); ?>>Laboratory</option>
                    <option value="dormitory" <?php echo e(request('category') == 'dormitory' ? 'selected' : ''); ?>>Dormitory</option>
                    <option value="cafeteria" <?php echo e(request('category') == 'cafeteria' ? 'selected' : ''); ?>>Cafeteria</option>
                    <option value="mosque" <?php echo e(request('category') == 'mosque' ? 'selected' : ''); ?>>Mosque</option>
                    <option value="other" <?php echo e(request('category') == 'other' ? 'selected' : ''); ?>>Other</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">Pencarian</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari nama fasilitas..." 
                       value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="<?php echo e(route('admin.content.facilities.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Facilities Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Daftar Fasilitas</h5>
    </div>
    <div class="card-body">
        <?php if($facilities->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Gambar</th>
                            <th>Nama Fasilitas</th>
                            <th>Kategori</th>
                            <th>Kapasitas</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <div class="facility-image">
                                    <?php if($facility->image): ?>
                                        <img src="<?php echo e(Storage::url($facility->image)); ?>" alt="<?php echo e($facility->name); ?>" class="rounded">
                                    <?php else: ?>
                                        <div class="image-placeholder rounded d-flex align-items-center justify-content-center">
                                            <i class="fas fa-building text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo e($facility->name); ?></strong>
                                    <?php if($facility->is_featured): ?>
                                        <i class="fas fa-star text-warning ms-1" title="Featured"></i>
                                    <?php endif; ?>
                                    <br>
                                    <small class="text-muted"><?php echo e(Str::limit($facility->description, 60)); ?></small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($facility->category == 'academic' ? 'primary' : ($facility->category == 'sports' ? 'success' : ($facility->category == 'library' ? 'info' : 'secondary'))); ?>">
                                    <?php echo e(ucfirst($facility->category)); ?>

                                </span>
                            </td>
                            <td>
                                <small><?php echo e($facility->capacity ? number_format($facility->capacity) . ' orang' : '-'); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($facility->is_available ? 'success' : 'danger'); ?>">
                                    <?php echo e($facility->is_available ? 'Tersedia' : 'Tidak Tersedia'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.content.facilities.show', $facility)); ?>" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.content.facilities.edit', $facility)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?php echo e(route('landing.facility', $facility->slug)); ?>" class="btn btn-sm btn-outline-success" title="Lihat di Website" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteFacility('<?php echo e($facility->slug); ?>', '<?php echo e(addslashes($facility->name)); ?>')"
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan <?php echo e($facilities->firstItem()); ?> - <?php echo e($facilities->lastItem()); ?> dari <?php echo e($facilities->total()); ?> fasilitas
                </div>
                <?php echo e($facilities->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada fasilitas ditemukan</h5>
                <p class="text-muted">Belum ada fasilitas yang sesuai dengan filter yang dipilih.</p>
                <a href="<?php echo e(route('admin.content.facilities.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Tambah Fasilitas Pertama
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.facility-image {
    width: 60px;
    height: 40px;
}

.facility-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-placeholder {
    width: 60px;
    height: 40px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteFacility(slug, name) {
    Swal.fire({
        title: 'Hapus Fasilitas?',
        text: `Apakah Anda yakin ingin menghapus fasilitas "${name}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus fasilitas',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send AJAX DELETE request
            $.ajax({
                url: `/admin/content/facilities/${slug}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Fasilitas berhasil dihapus.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    console.error('Delete error:', xhr);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus fasilitas: ' + (xhr.responseJSON?.message || 'Unknown error'),
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/content/facilities/index.blade.php ENDPATH**/ ?>