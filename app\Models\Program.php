<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Program extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'level',
        'type',
        'curriculum',
        'requirements',
        'subjects',
        'extracurricular',
        'duration_years',
        'fee',
        'capacity',
        'current_students',
        'is_active',
        'order',
    ];

    protected function casts(): array
    {
        return [
            'subjects' => 'array',
            'extracurricular' => 'array',
            'fee' => 'decimal:2',
            'is_active' => 'boolean',
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($program) {
            if (empty($program->slug)) {
                $program->slug = Str::slug($program->name);
            }
        });
    }

    public function admissions()
    {
        return $this->hasMany(Admission::class);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }
}
