@extends('layouts.landing')

@section('title', '<PERSON>ri')
@section('description', 'Galeri foto dan video kegiatan sekolah')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 120px 0 80px;
        text-align: center;
    }

    .page-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .gallery-filters {
        text-align: center;
        margin-bottom: 3rem;
    }

    .filter-btn {
        background: transparent;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        padding: 10px 25px;
        margin: 5px;
        border-radius: 50px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-btn:hover,
    .filter-btn.active {
        background: var(--primary-color);
        color: white;
    }

    .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 3rem;
    }

    .gallery-item {
        position: relative;
        height: 250px;
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .gallery-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    .gallery-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .gallery-item:hover img {
        transform: scale(1.1);
    }

    .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        color: white;
        text-align: center;
        padding: 20px;
    }

    .gallery-item:hover .gallery-overlay {
        opacity: 1;
    }

    .gallery-overlay h5 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .gallery-overlay p {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .lightbox {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        align-items: center;
        justify-content: center;
    }

    .lightbox.active {
        display: flex;
    }

    .lightbox-content {
        max-width: 90%;
        max-height: 90%;
        position: relative;
    }

    .lightbox-content img {
        width: 100%;
        height: auto;
        border-radius: 10px;
    }

    .lightbox-close {
        position: absolute;
        top: -40px;
        right: 0;
        color: white;
        font-size: 2rem;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .lightbox-close:hover {
        color: var(--primary-color);
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #ddd;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .gallery-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .gallery-item {
            height: 200px;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1 class="page-title" data-aos="fade-up">Galeri Sekolah</h1>
        <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            Dokumentasi kegiatan dan momen berharga di sekolah kami
        </p>
    </div>
</section>

<!-- Gallery Content -->
<section class="section">
    <div class="container">
        <!-- Gallery Filters -->
        <div class="gallery-filters" data-aos="fade-up">
            <button class="filter-btn active" data-filter="all">Semua</button>
            <button class="filter-btn" data-filter="kegiatan">Kegiatan</button>
            <button class="filter-btn" data-filter="fasilitas">Fasilitas</button>
            <button class="filter-btn" data-filter="prestasi">Prestasi</button>
            <button class="filter-btn" data-filter="event">Event</button>
            <button class="filter-btn" data-filter="lainnya">Lainnya</button>
        </div>

        <!-- Gallery Grid -->
        @if($images->isNotEmpty())
            <div class="gallery-grid" data-aos="fade-up" data-aos-delay="300">
                @foreach($images as $image)
                    <div class="gallery-item"
                         data-category="{{ $image->category }}"
                         onclick="openLightbox('{{ $image->file_path ? asset('storage/' . $image->file_path) : 'https://via.placeholder.com/400x300/667eea/ffffff?text=' . urlencode($image->title) }}', '{{ $image->title }}', '{{ $image->description }}')">
                        @if($image->file_path && file_exists(storage_path('app/public/' . $image->file_path)))
                            <img src="{{ asset('storage/' . $image->file_path) }}" alt="{{ $image->title }}">
                        @else
                            <img src="https://via.placeholder.com/400x300/667eea/ffffff?text={{ urlencode($image->title) }}" alt="{{ $image->title }}">
                        @endif
                        <div class="gallery-overlay">
                            <h5>{{ $image->title }}</h5>
                            @if($image->description)
                                <p>{{ Str::limit($image->description, 100) }}</p>
                            @endif
                            <div class="mt-2">
                                <span class="badge bg-light text-dark mb-2">{{ ucfirst($image->category) }}</span><br>
                                <i class="fas fa-search-plus fa-2x"></i>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center" data-aos="fade-up">
                {{ $images->links() }}
            </div>
        @else
            <div class="empty-state" data-aos="fade-up">
                <i class="fas fa-images"></i>
                <h3>Belum Ada Galeri</h3>
                <p>Galeri foto dan video akan segera diperbarui.</p>
            </div>
        @endif
    </div>
</section>

<!-- Lightbox -->
<div class="lightbox" id="lightbox" onclick="closeLightbox()">
    <div class="lightbox-content" onclick="event.stopPropagation()">
        <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
        <img id="lightbox-image" src="" alt="">
        <div class="text-center mt-3">
            <h5 id="lightbox-title" class="text-white"></h5>
            <p id="lightbox-description" class="text-white-50"></p>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Gallery Filter
    document.addEventListener('DOMContentLoaded', function() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        const galleryItems = document.querySelectorAll('.gallery-item');

        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                filterBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');

                galleryItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                        item.style.animation = 'fadeIn 0.5s ease';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });

    // Lightbox Functions
    function openLightbox(imageSrc, title, description) {
        const lightbox = document.getElementById('lightbox');
        const lightboxImage = document.getElementById('lightbox-image');
        const lightboxTitle = document.getElementById('lightbox-title');
        const lightboxDescription = document.getElementById('lightbox-description');

        lightboxImage.src = imageSrc;
        lightboxTitle.textContent = title;
        lightboxDescription.textContent = description || '';

        lightbox.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    function closeLightbox() {
        const lightbox = document.getElementById('lightbox');
        lightbox.classList.remove('active');
        document.body.style.overflow = 'auto';
    }

    // Close lightbox with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });

    // Add fadeIn animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;
    document.head.appendChild(style);
</script>
@endpush
