<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ParentProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'occupation',
        'education',
        'monthly_income',
        'address',
        'emergency_contact',
        'notes',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function children()
    {
        return $this->hasMany(StudentProfile::class, 'parent_id');
    }
}
