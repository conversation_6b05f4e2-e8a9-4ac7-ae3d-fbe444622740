<?php $__env->startSection('title', 'Pengaturan PPDB'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Pengaturan PPDB Online</h1>
    <div>
        <a href="<?php echo e(route('admin.ppdb.dashboard')); ?>" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-2"></i>Kembali ke Dashboard
        </a>
        <a href="<?php echo e(route('ppdb.index')); ?>" class="btn btn-outline-success" target="_blank">
            <i class="fas fa-external-link-alt me-2"></i>Lihat Halaman PPDB
        </a>
    </div>
</div>

<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>Pengaturan PPDB
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('admin.ppdb.settings.update')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    
                    <!-- Basic Settings -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="academic_year" class="form-label">Tahun Ajaran <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['academic_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="academic_year" name="academic_year" 
                                       value="<?php echo e(old('academic_year', $ppdbSetting->academic_year ?? '')); ?>" 
                                       placeholder="2024/2025" required>
                                <?php $__errorArgs = ['academic_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status PPDB <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="status" name="status" required>
                                    <option value="closed" <?php echo e(old('status', $ppdbSetting->status ?? '') == 'closed' ? 'selected' : ''); ?>>Ditutup</option>
                                    <option value="open" <?php echo e(old('status', $ppdbSetting->status ?? '') == 'open' ? 'selected' : ''); ?>>Dibuka</option>
                                    <option value="maintenance" <?php echo e(old('status', $ppdbSetting->status ?? '') == 'maintenance' ? 'selected' : ''); ?>>Maintenance</option>
                                </select>
                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Registration Period -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_start" class="form-label">Mulai Pendaftaran</label>
                                <input type="datetime-local" class="form-control <?php $__errorArgs = ['registration_start'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="registration_start" name="registration_start" 
                                       value="<?php echo e(old('registration_start', $ppdbSetting && $ppdbSetting->registration_start ? $ppdbSetting->registration_start->format('Y-m-d\TH:i') : '')); ?>">
                                <?php $__errorArgs = ['registration_start'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_end" class="form-label">Akhir Pendaftaran</label>
                                <input type="datetime-local" class="form-control <?php $__errorArgs = ['registration_end'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="registration_end" name="registration_end" 
                                       value="<?php echo e(old('registration_end', $ppdbSetting && $ppdbSetting->registration_end ? $ppdbSetting->registration_end->format('Y-m-d\TH:i') : '')); ?>">
                                <?php $__errorArgs = ['registration_end'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Document Completion Period -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_start" class="form-label">Mulai Pelengkapan Berkas</label>
                                <input type="datetime-local" class="form-control <?php $__errorArgs = ['document_start'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="document_start" name="document_start"
                                       value="<?php echo e(old('document_start', $ppdbSetting && $ppdbSetting->document_start ? $ppdbSetting->document_start->format('Y-m-d\TH:i') : '')); ?>">
                                <?php $__errorArgs = ['document_start'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <small class="text-muted">Periode dimulainya pelengkapan berkas oleh calon siswa</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_end" class="form-label">Akhir Pelengkapan Berkas</label>
                                <input type="datetime-local" class="form-control <?php $__errorArgs = ['document_end'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="document_end" name="document_end"
                                       value="<?php echo e(old('document_end', $ppdbSetting && $ppdbSetting->document_end ? $ppdbSetting->document_end->format('Y-m-d\TH:i') : '')); ?>">
                                <?php $__errorArgs = ['document_end'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <small class="text-muted">Batas akhir pelengkapan berkas oleh calon siswa</small>
                            </div>
                        </div>
                    </div>

                    <!-- Announcement Date -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="announcement_date" class="form-label">Tanggal Pengumuman</label>
                                <input type="datetime-local" class="form-control <?php $__errorArgs = ['announcement_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="announcement_date" name="announcement_date"
                                       value="<?php echo e(old('announcement_date', $ppdbSetting && $ppdbSetting->announcement_date ? $ppdbSetting->announcement_date->format('Y-m-d\TH:i') : '')); ?>">
                                <?php $__errorArgs = ['announcement_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Registration Fee -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_fee" class="form-label">Biaya Pendaftaran (Rp)</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['registration_fee'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="registration_fee" name="registration_fee" 
                                       value="<?php echo e(old('registration_fee', $ppdbSetting->registration_fee ?? 0)); ?>" 
                                       min="0" step="1000">
                                <?php $__errorArgs = ['registration_fee'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <small class="form-text text-muted">Masukkan 0 jika gratis</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status Aktif</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           <?php echo e(old('is_active', $ppdbSetting->is_active ?? false) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="is_active">
                                        Aktifkan pengaturan PPDB ini
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Requirements -->
                    <div class="mb-4">
                        <label for="requirements" class="form-label">Persyaratan Pendaftaran</label>
                        <textarea class="form-control <?php $__errorArgs = ['requirements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="requirements" name="requirements" rows="4" 
                                  placeholder="Masukkan persyaratan pendaftaran..."><?php echo e(old('requirements', $ppdbSetting->requirements ?? '')); ?></textarea>
                        <?php $__errorArgs = ['requirements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Announcement Text -->
                    <div class="mb-4">
                        <label for="announcement_text" class="form-label">Teks Pengumuman</label>
                        <textarea class="form-control <?php $__errorArgs = ['announcement_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="announcement_text" name="announcement_text" rows="3"
                                  placeholder="Teks yang akan ditampilkan di halaman PPDB..."><?php echo e(old('announcement_text', $ppdbSetting->announcement_text ?? '')); ?></textarea>
                        <?php $__errorArgs = ['announcement_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Required Documents -->
                    <div class="mb-4">
                        <label for="required_documents" class="form-label">Dokumen yang Diperlukan</label>
                        <div id="required-documents-container">
                            <?php
                                $documents = old('required_documents', $ppdbSetting->required_documents ?? []);
                                if (empty($documents)) {
                                    $documents = [''];
                                }
                            ?>
                            <?php $__currentLoopData = $documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="input-group mb-2 document-input-group">
                                <input type="text" class="form-control <?php $__errorArgs = ['required_documents.'.$index];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       name="required_documents[]" value="<?php echo e($document); ?>"
                                       placeholder="Contoh: Ijazah/SKHUN, Kartu Keluarga, dll...">
                                <button type="button" class="btn btn-outline-danger remove-document"
                                        onclick="removeDocumentField(this)" <?php echo e($index === 0 ? 'style=display:none;' : ''); ?>>
                                    <i class="fas fa-times"></i>
                                </button>
                                <?php $__errorArgs = ['required_documents.'.$index];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addDocumentField()">
                            <i class="fas fa-plus me-1"></i>Tambah Dokumen
                        </button>
                        <small class="form-text text-muted d-block mt-2">
                            Masukkan dokumen yang diperlukan untuk pendaftaran. Kosongkan jika tidak ada dokumen khusus.
                        </small>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Simpan Pengaturan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Program Quotas Section -->
<?php if($programs->isNotEmpty()): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Kuota Program
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Program</th>
                                <th>Level</th>
                                <th>Status</th>
                                <th>Kuota</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($program->name); ?></td>
                                <td>
                                    <span class="badge bg-info"><?php echo e(ucfirst($program->level)); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($program->is_active ? 'success' : 'secondary'); ?>">
                                        <?php echo e($program->is_active ? 'Aktif' : 'Tidak Aktif'); ?>

                                    </span>
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" 
                                           style="width: 100px;" min="0" 
                                           placeholder="Kuota" disabled>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Fitur pengaturan kuota akan diimplementasikan pada versi selanjutnya.
                </small>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Auto-generate academic year
    $('#academic_year').on('blur', function() {
        let value = $(this).val();
        if (value && !value.includes('/')) {
            let year = parseInt(value);
            if (year > 2000 && year < 3000) {
                $(this).val(year + '/' + (year + 1));
            }
        }
    });
    
    // Status change handler
    $('#status').change(function() {
        const status = $(this).val();
        const dateFields = $('#registration_start, #registration_end, #document_start, #document_end, #announcement_date');

        if (status === 'closed') {
            dateFields.prop('required', false);
        } else {
            $('#registration_start, #registration_end').prop('required', true);
        }
    });
});

// Function to add new document field
function addDocumentField() {
    const container = document.getElementById('required-documents-container');
    const newField = document.createElement('div');
    newField.className = 'input-group mb-2 document-input-group';
    newField.innerHTML = `
        <input type="text" class="form-control" name="required_documents[]"
               placeholder="Contoh: Ijazah/SKHUN, Kartu Keluarga, dll...">
        <button type="button" class="btn btn-outline-danger remove-document" onclick="removeDocumentField(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newField);
    updateRemoveButtons();
}

// Function to remove document field
function removeDocumentField(button) {
    button.closest('.document-input-group').remove();
    updateRemoveButtons();
}

// Function to update remove button visibility
function updateRemoveButtons() {
    const groups = document.querySelectorAll('.document-input-group');
    groups.forEach((group, index) => {
        const removeBtn = group.querySelector('.remove-document');
        if (groups.length === 1) {
            removeBtn.style.display = 'none';
        } else {
            removeBtn.style.display = 'block';
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/ppdb/settings.blade.php ENDPATH**/ ?>