<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SecurityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ip_address',
        'user_agent',
        'event_type',
        'description',
        'metadata',
        'risk_level',
        'is_resolved',
        'resolved_at',
    ];

    protected function casts(): array
    {
        return [
            'metadata' => 'array',
            'is_resolved' => 'boolean',
            'resolved_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Static methods for logging security events
     */
    public static function logEvent(string $eventType, ?int $userId = null, array $metadata = []): self
    {
        return self::create([
            'user_id' => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'event_type' => $eventType,
            'description' => self::getEventDescription($eventType),
            'metadata' => $metadata,
            'risk_level' => self::getRiskLevel($eventType),
        ]);
    }

    private static function getEventDescription(string $eventType): string
    {
        return match ($eventType) {
            'login_success' => 'User berhasil login',
            'login_failed' => 'Percobaan login gagal',
            'logout' => 'User logout',
            'password_reset' => 'Reset password',
            'account_locked' => 'Akun dikunci karena terlalu banyak percobaan login gagal',
            'suspicious_activity' => 'Aktivitas mencurigakan terdeteksi',
            'rate_limit_exceeded' => 'Batas permintaan terlampaui',
            'suspicious_file_upload' => 'Upload file mencurigakan terdeteksi',
            'data_access' => 'Akses data sensitif',
            'data_modification' => 'Modifikasi data',
            'profile_updated' => 'Profil user diperbarui',
            'password_changed' => 'Password user diubah',
            'avatar_deleted' => 'Avatar user dihapus',
            'multiple_sessions_detected' => 'Multiple sesi aktif terdeteksi',
            default => 'Event keamanan',
        };
    }

    private static function getRiskLevel(string $eventType): string
    {
        return match ($eventType) {
            'login_success', 'logout', 'profile_updated', 'avatar_deleted' => 'low',
            'login_failed', 'password_reset', 'password_changed' => 'medium',
            'account_locked', 'suspicious_activity', 'rate_limit_exceeded', 'suspicious_file_upload', 'multiple_sessions_detected' => 'high',
            'data_modification' => 'critical',
            default => 'low',
        };
    }
}
