@extends('layouts.landing')

@section('title', $news->title)
@section('description', $news->excerpt)

@section('content')
<!-- Hero Section -->
<section class="hero-section bg-gradient position-relative" style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 100px 0 50px;">
    <!-- Dark overlay for better text contrast -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: rgba(0,0,0,0.3); z-index: 1;"></div>
    <div class="container position-relative" style="z-index: 2;">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white hero-text-content">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}" class="text-white-50">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}#news" class="text-white-50">Berita</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ Str::limit($news->title, 30) }}</li>
                    </ol>
                </nav>
                
                <div class="mb-3">
                    <span class="badge bg-{{ $news->type == 'announcement' ? 'warning' : 'primary' }} fs-6 px-3 py-2">
                        {{ $news->type == 'announcement' ? 'Pengumuman' : 'Berita' }}
                    </span>
                </div>
                
                <h1 class="display-5 fw-bold mb-3">{{ $news->title }}</h1>
                
                <div class="d-flex justify-content-center align-items-center gap-4 text-white-50">
                    <span><i class="fas fa-calendar me-2"></i>{{ $news->published_at->format('d F Y') }}</span>
                    <span><i class="fas fa-user me-2"></i>{{ $news->author->name }}</span>
                    <span><i class="fas fa-eye me-2"></i>{{ $news->views }} views</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- News Content -->
<section class="section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Featured Image -->
                @if($news->featured_image)
                <div class="mb-5">
                    <img src="{{ asset('storage/' . $news->featured_image) }}" 
                         alt="{{ $news->title }}" 
                         class="img-fluid rounded shadow">
                </div>
                @endif
                
                <!-- News Content -->
                <div class="news-content">
                    <div class="lead mb-4 text-muted">{{ $news->excerpt }}</div>
                    
                    <div class="content">
                        {!! $news->content !!}
                    </div>
                </div>
                
                <!-- Tags/Categories -->
                @if($news->type)
                <div class="mt-5 pt-4 border-top">
                    <h6 class="text-muted mb-3">Kategori:</h6>
                    <span class="badge bg-{{ $news->type == 'announcement' ? 'warning' : 'primary' }} me-2">
                        {{ $news->type == 'announcement' ? 'Pengumuman' : 'Berita' }}
                    </span>
                </div>
                @endif
                
                <!-- Share Buttons -->
                <div class="mt-5 pt-4 border-top">
                    <h6 class="text-muted mb-3">Bagikan:</h6>
                    <div class="d-flex gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($news->title) }}" 
                           target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="https://wa.me/?text={{ urlencode($news->title . ' - ' . request()->fullUrl()) }}" 
                           target="_blank" class="btn btn-outline-success btn-sm">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </a>
                        <button onclick="copyToClipboard('{{ request()->fullUrl() }}')" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-link me-1"></i>Copy Link
                        </button>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="mt-5 pt-4 border-top">
                    <div class="row">
                        @if($previousNews)
                        <div class="col-md-6 mb-3">
                            <a href="{{ route('landing.news', $previousNews->slug) }}" class="text-decoration-none">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-chevron-left me-3 text-primary"></i>
                                    <div>
                                        <small class="text-muted">Berita Sebelumnya</small>
                                        <h6 class="mb-0">{{ Str::limit($previousNews->title, 40) }}</h6>
                                    </div>
                                </div>
                            </a>
                        </div>
                        @endif
                        
                        @if($nextNews)
                        <div class="col-md-6 mb-3 text-md-end">
                            <a href="{{ route('landing.news', $nextNews->slug) }}" class="text-decoration-none">
                                <div class="d-flex align-items-center justify-content-md-end">
                                    <div class="text-md-end me-md-3">
                                        <small class="text-muted">Berita Selanjutnya</small>
                                        <h6 class="mb-0">{{ Str::limit($nextNews->title, 40) }}</h6>
                                    </div>
                                    <i class="fas fa-chevron-right text-primary"></i>
                                </div>
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
                
                <!-- Back to News -->
                <div class="text-center mt-5">
                    <a href="{{ route('landing') }}#news" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>Kembali ke Berita
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related News -->
@if($relatedNews->count() > 0)
<section class="section bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h3 class="text-center mb-5">Berita Terkait</h3>
                
                <div class="row g-4">
                    @foreach($relatedNews as $related)
                    <div class="col-md-6">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="news-image">
                                @if($related->featured_image)
                                    <img src="{{ asset('storage/' . $related->featured_image) }}" alt="{{ $related->title }}">
                                @else
                                    <img src="https://via.placeholder.com/400x200/667eea/ffffff?text=News" alt="{{ $related->title }}">
                                @endif
                            </div>
                            <div class="card-body">
                                <div class="news-meta mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ $related->published_at->format('d M Y') }}
                                    </small>
                                    <span class="badge bg-{{ $related->type == 'announcement' ? 'warning' : 'primary' }} ms-2">
                                        {{ $related->type == 'announcement' ? 'Pengumuman' : 'Berita' }}
                                    </span>
                                </div>
                                <h6 class="card-title">{{ Str::limit($related->title, 60) }}</h6>
                                <p class="card-text small">{{ Str::limit($related->excerpt, 80) }}</p>
                                <a href="{{ route('landing.news', $related->slug) }}" class="btn btn-outline-primary btn-sm">
                                    Baca Selengkapnya
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
/* Hero Section Text Enhancement */
.hero-text-content {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.hero-text-content h1 {
    text-shadow: 3px 3px 6px rgba(0,0,0,0.8);
    font-weight: 700;
}

.hero-text-content .breadcrumb-item a {
    color: rgba(255,255,255,0.9) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
}

.hero-text-content .breadcrumb-item.active {
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
}

.hero-text-content .badge {
    text-shadow: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-text-content .d-flex span {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
}

.news-content .content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.news-content .content h1,
.news-content .content h2,
.news-content .content h3,
.news-content .content h4,
.news-content .content h5,
.news-content .content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.news-content .content p {
    margin-bottom: 1.5rem;
}

.news-content .content ul,
.news-content .content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.news-content .content li {
    margin-bottom: 0.5rem;
}

.news-content .content blockquote {
    border-left: 4px solid #667eea;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #666;
}

.news-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px 8px 0 0;
}
</style>
@endpush

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
@endpush
