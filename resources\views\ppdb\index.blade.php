@extends('layouts.landing')

@section('title', 'PPDB Online - Pendaftaran Peserta Didik Baru')
@section('description', 'Daftar sebagai siswa baru melalui sistem PPDB Online. Proses pendaftaran mudah dan cepat.')

@section('content')
<!-- Hero Section -->
<section class="hero-section bg-gradient position-relative" style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 100px 0 50px;">
    <!-- Background overlay for better text contrast -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: rgba(0,0,0,0.3);"></div>
    <div class="container position-relative">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}" class="text-white">Beranda</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">PPDB Online</li>
                    </ol>
                </nav>
                
                @if($ppdbSetting)
                    @php
                        $status = $ppdbSetting->getRegistrationStatus();
                    @endphp

                    @if($status === 'open')
                        <div class="mb-3">
                            <span class="badge bg-success fs-6 px-4 py-2">
                                <i class="fas fa-check-circle me-2"></i>PENDAFTARAN DIBUKA
                            </span>
                        </div>

                        <h1 class="display-4 fw-bold mb-3 text-white">📚 PPDB Online {{ $ppdbSetting->academic_year }}</h1>
                        <p class="lead mb-4 text-white">Penerimaan Peserta Didik Baru - Daftar Sekarang Juga!</p>

                        <div class="d-flex justify-content-center align-items-center gap-4 text-white mb-4">
                            <span><i class="fas fa-calendar me-2"></i>Batas: {{ $ppdbSetting->registration_end ? $ppdbSetting->registration_end->format('d F Y') : '-' }}</span>
                            <span><i class="fas fa-money-bill-wave me-2"></i>
                                @if($ppdbSetting->registration_fee > 0)
                                    Rp {{ number_format($ppdbSetting->registration_fee, 0, ',', '.') }}
                                @else
                                    GRATIS
                                @endif
                            </span>
                        </div>

                        <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                            <button onclick="showRegistrationModal()" class="btn btn-warning btn-lg px-5">
                                <i class="fas fa-user-plus me-2"></i>Daftar Sekarang
                            </button>
                            <button onclick="showCheckStatusModal()" class="btn btn-outline-light btn-lg px-4">
                                <i class="fas fa-search me-2"></i>Cek Status
                            </button>
                        </div>
                    @else
                        <div class="mb-3">
                            <span class="badge bg-warning text-dark fs-6 px-4 py-2">
                                <i class="fas fa-info-circle me-2"></i>Informasi PPDB
                            </span>
                        </div>

                        <h1 class="display-4 fw-bold mb-3 text-white">📚 PPDB Online {{ $ppdbSetting->academic_year }}</h1>
                        <p class="lead mb-4 text-white">
                            @if($status === 'not_started')
                                Pendaftaran belum dimulai.
                            @elseif($status === 'ended')
                                Periode pendaftaran telah berakhir.
                            @elseif($status === 'maintenance')
                                Sistem PPDB sedang dalam maintenance.
                            @elseif($status === 'closed')
                                Pendaftaran PPDB sedang ditutup.
                            @else
                                Pendaftaran Peserta Didik Baru sedang tidak tersedia.
                            @endif
                        </p>

                        <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                            <a href="{{ route('landing') }}" class="btn btn-light btn-lg px-4">
                                <i class="fas fa-home me-2"></i>Kembali ke Beranda
                            </a>
                            <button onclick="showCheckStatusModal()" class="btn btn-outline-light btn-lg px-4">
                                <i class="fas fa-search me-2"></i>Cek Status
                            </button>
                        </div>
                    @endif
                @else
                    <div class="mb-3">
                        <span class="badge bg-secondary fs-6 px-4 py-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>PPDB Belum Dikonfigurasi
                        </span>
                    </div>

                    <h1 class="display-4 fw-bold mb-3 text-white">📚 PPDB Online</h1>
                    <p class="lead mb-4 text-white">PPDB belum dikonfigurasi untuk tahun ajaran ini.</p>

                    <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                        <a href="{{ route('landing') }}" class="btn btn-light btn-lg px-4">
                            <i class="fas fa-home me-2"></i>Kembali ke Beranda
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- PPDB Information Section -->
<section class="section bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Announcement Text -->
                @if($ppdbSetting->announcement_text)
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h5 class="text-center mb-3 text-primary">
                            <i class="fas fa-bullhorn me-2"></i>Pengumuman PPDB
                        </h5>
                        <div class="text-center">
                            <div class="alert alert-info mb-0">
                                {!! nl2br(e($ppdbSetting->announcement_text)) !!}
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- PPDB Details -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-5">
                        <h4 class="text-center mb-4 text-dark">
                            <i class="fas fa-info-circle me-2 text-primary"></i>Informasi PPDB {{ $ppdbSetting->academic_year }}
                        </h4>
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h6 class="text-dark mb-3">📅 Jadwal Penting:</h6>
                                <div class="list-group list-group-flush">
                                    @if($ppdbSetting->registration_start && $ppdbSetting->registration_end)
                                    <div class="list-group-item border-0 px-0">
                                        <strong>Pendaftaran:</strong><br>
                                        <span class="text-muted">{{ $ppdbSetting->registration_start->format('d F Y, H:i') }} - {{ $ppdbSetting->registration_end->format('d F Y, H:i') }}</span>
                                    </div>
                                    @endif

                                    @if($ppdbSetting->document_start && $ppdbSetting->document_end)
                                    <div class="list-group-item border-0 px-0">
                                        <strong>Pelengkapan Berkas:</strong><br>
                                        <span class="text-muted">{{ $ppdbSetting->document_start->format('d F Y, H:i') }} - {{ $ppdbSetting->document_end->format('d F Y, H:i') }}</span>
                                    </div>
                                    @endif

                                    @if($ppdbSetting->announcement_date)
                                    <div class="list-group-item border-0 px-0">
                                        <strong>Pengumuman Hasil:</strong><br>
                                        <span class="text-muted">{{ $ppdbSetting->announcement_date->format('d F Y, H:i') }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <h6 class="text-dark mb-3">💰 Biaya Pendaftaran:</h6>
                                <div class="alert alert-success">
                                    <h4 class="mb-0">
                                        @if($ppdbSetting->registration_fee > 0)
                                            Rp {{ number_format($ppdbSetting->registration_fee, 0, ',', '.') }}
                                        @else
                                            <span class="text-success">GRATIS</span>
                                        @endif
                                    </h4>
                                </div>

                                @if($ppdbSetting->program_quotas)
                                <h6 class="text-dark mb-3 mt-4">🎯 Kuota Program:</h6>
                                <div class="list-group list-group-flush">
                                    @foreach($ppdbSetting->program_quotas as $program => $quota)
                                    <div class="list-group-item border-0 px-0 d-flex justify-content-between">
                                        <span>{{ $program }}</span>
                                        <span class="badge bg-primary">{{ $quota }} siswa</span>
                                    </div>
                                    @endforeach
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Requirements & Documents -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-5">
                        <h4 class="text-center mb-4 text-dark">
                            <i class="fas fa-list-check me-2 text-primary"></i>Persyaratan & Dokumen
                        </h4>
                        <div class="row">
                            @if($ppdbSetting->requirements)
                            <div class="col-md-6">
                                <h6 class="text-dark">Persyaratan Pendaftaran:</h6>
                                <div class="text-dark">{!! nl2br(e($ppdbSetting->requirements)) !!}</div>
                            </div>
                            @endif
                            <div class="{{ $ppdbSetting->requirements ? 'col-md-6' : 'col-12' }}">
                                <h6 class="text-dark">Dokumen yang Diperlukan:</h6>
                                <ul class="list-unstyled">
                                    @if($ppdbSetting->required_documents && count($ppdbSetting->required_documents) > 0)
                                        @foreach($ppdbSetting->required_documents as $doc)
                                        <li class="mb-2 text-dark">
                                            <i class="fas fa-check text-success me-2"></i>{{ $doc }}
                                        </li>
                                        @endforeach
                                    @else
                                        <li class="mb-2 text-muted">
                                            <i class="fas fa-info-circle me-2"></i>Dokumen akan diinformasikan kemudian
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Programs Section -->
<section class="section">
    <div class="container">
        <div class="section-title text-center mb-5">
            <h2 class="text-dark">Program yang Tersedia</h2>
            <p class="text-muted">Pilih program pendidikan yang sesuai dengan minat dan bakat Anda</p>
        </div>
        
        <div class="row g-4">
            @foreach($programs as $program)
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-graduation-cap text-primary"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1 text-dark">{{ $program->name }}</h5>
                                <small class="text-muted">{{ $program->level }}</small>
                            </div>
                        </div>
                        <p class="card-text text-dark">{{ $program->description }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>{{ $program->duration_years }} tahun
                                </small>
                                @if(isset($ppdbSetting->program_quotas[$program->id]))
                                <small class="text-success">
                                    <i class="fas fa-users me-1"></i>Kuota: {{ $ppdbSetting->program_quotas[$program->id] }}
                                </small>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>




@endsection

@push('scripts')
<script>
// PPDB Data
const ppdbData = {
    isOpen: {{ $ppdbSetting && $ppdbSetting->getRegistrationStatus() === 'open' ? 'true' : 'false' }},
    status: '{{ $ppdbSetting ? $ppdbSetting->getRegistrationStatus() : 'not_configured' }}',
    academicYear: '{{ $ppdbSetting ? $ppdbSetting->academic_year : '' }}',
    registrationEnd: '{{ $ppdbSetting && $ppdbSetting->registration_end ? $ppdbSetting->registration_end->format('d F Y') : '' }}',
    registrationFee: {{ $ppdbSetting ? $ppdbSetting->registration_fee : 0 }},
    programs: @json($programs->map(function($program) {
        return ['id' => $program->id, 'name' => $program->name, 'level' => $program->level];
    })),
    registerUrl: '{{ route('ppdb.register') }}',
    csrfToken: '{{ csrf_token() }}'
};

// Function to show Registration Modal
function showRegistrationModal() {
    if (!ppdbData.isOpen) {
        Swal.fire({
            icon: 'info',
            title: 'PPDB Belum Dibuka',
            text: 'Pendaftaran PPDB belum dibuka. Silakan tunggu pengumuman resmi dari sekolah.',
            confirmButtonText: 'OK',
            confirmButtonColor: '#667eea'
        });
        return;
    }

    // Create program options
    let programOptions = '<option value="">Pilih Program...</option>';
    if (ppdbData.programs && ppdbData.programs.length > 0) {
        ppdbData.programs.forEach(program => {
            programOptions += `<option value="${program.id}">${program.name} - ${program.level}</option>`;
        });
    }

    const feeText = ppdbData.registrationFee > 0
        ? `Rp ${new Intl.NumberFormat('id-ID').format(ppdbData.registrationFee)}`
        : 'GRATIS';

    Swal.fire({
        title: `📚 Form Pendaftaran PPDB ${ppdbData.academicYear}`,
        html: `
            <div class="text-start">
                <!-- Info Alert -->
                <div class="alert alert-info mb-4" style="background-color: #e3f2fd; border-color: #2196f3; color: #1976d2;">
                    <div class="row text-center">
                        <div class="col-6">
                            <i class="fas fa-calendar-alt fa-2x mb-2 text-primary"></i>
                            <h6 style="color: #1976d2; font-weight: bold;">Batas Pendaftaran</h6>
                            <small style="color: #1976d2;"><strong>${ppdbData.registrationEnd}</strong></small>
                        </div>
                        <div class="col-6">
                            <i class="fas fa-money-bill-wave fa-2x mb-2 text-success"></i>
                            <h6 style="color: #1976d2; font-weight: bold;">Biaya</h6>
                            <small style="color: #1976d2;"><strong>${feeText}</strong></small>
                        </div>
                    </div>
                </div>

                <!-- Form -->
                <form id="ppdbModalForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Nama Lengkap <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="full_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">NIK <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="nik" maxlength="16" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">No. Telepon <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Jenis Kelamin <span class="text-danger">*</span></label>
                            <select class="form-select" name="gender" required>
                                <option value="">Pilih...</option>
                                <option value="L">Laki-laki</option>
                                <option value="P">Perempuan</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Tanggal Lahir <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="birth_date" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Tempat Lahir <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="birth_place" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Agama <span class="text-danger">*</span></label>
                            <select class="form-select" name="religion" required>
                                <option value="">Pilih...</option>
                                <option value="Islam">Islam</option>
                                <option value="Kristen">Kristen</option>
                                <option value="Katolik">Katolik</option>
                                <option value="Hindu">Hindu</option>
                                <option value="Buddha">Buddha</option>
                                <option value="Konghucu">Konghucu</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Program Pilihan <span class="text-danger">*</span></label>
                            <select class="form-select" name="program_id" required>
                                ${programOptions}
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label" style="color: #333; font-weight: 600;">Alamat Lengkap <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="address" rows="2" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Nama Ayah <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="father_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" style="color: #333; font-weight: 600;">Nama Ibu <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="mother_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label" style="color: #333; font-weight: 600;">Sekolah Asal <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="previous_school" required>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" name="agreement" id="agreementCheck" required>
                        <label class="form-check-label small" style="color: #333; font-weight: 500;" for="agreementCheck">
                            Saya menyatakan bahwa data yang saya isi adalah benar dan dapat dipertanggungjawabkan. <span class="text-danger">*</span>
                        </label>
                    </div>
                </form>

                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Setelah daftar, Anda akan mendapat email untuk melengkapi data di Portal Calon Siswa
                    </small>
                </div>
            </div>
        `,
        width: '900px',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-paper-plane me-2"></i>Daftar Sekarang',
        cancelButtonText: 'Batal',
        confirmButtonColor: '#667eea',
        cancelButtonColor: '#6c757d',
        showLoaderOnConfirm: true,
        didOpen: () => {
            // Disable submit button initially
            const submitBtn = Swal.getConfirmButton();
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.5';

            // Setup form validation after modal opens
            setupModalFormValidation();
        },
        preConfirm: () => {
            return submitRegistrationForm();
        },
        allowOutsideClick: () => !Swal.isLoading()
    });

    // This will be called from didOpen callback
    // Validation setup moved to setupModalFormValidation function
}

// Function to setup modal form validation
function setupModalFormValidation() {
    // NIK validation
    const nikInput = document.querySelector('input[name="nik"]');
    if (nikInput) {
        nikInput.addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '').substring(0, 16);
            checkFormValidity();
        });
    }

    // Phone validation
    const phoneInput = document.querySelector('input[name="phone"]');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '');
            checkFormValidity();
        });
    }

    // Agreement checkbox validation
    const agreementCheck = document.querySelector('#agreementCheck');
    if (agreementCheck) {
        agreementCheck.addEventListener('change', function() {
            checkFormValidity();
        });
    }

    // Add change listeners to all required fields
    const requiredFields = document.querySelectorAll('#ppdbModalForm [required]');
    requiredFields.forEach(field => {
        field.addEventListener('input', checkFormValidity);
        field.addEventListener('change', checkFormValidity);
    });
}

// Function to check form validity and enable/disable submit button
function checkFormValidity() {
    const form = document.getElementById('ppdbModalForm');
    const submitBtn = Swal.getConfirmButton();
    const agreementCheck = document.querySelector('#agreementCheck');

    if (!form || !submitBtn || !agreementCheck) return;

    // Check if form is valid and agreement is checked
    const isFormValid = form.checkValidity();
    const isAgreementChecked = agreementCheck.checked;

    if (isFormValid && isAgreementChecked) {
        submitBtn.disabled = false;
        submitBtn.style.opacity = '1';
    } else {
        submitBtn.disabled = true;
        submitBtn.style.opacity = '0.5';
    }
}

// Function to show Check Status Modal
function showCheckStatusModal() {
    Swal.fire({
        title: '🔍 Cek Status Pendaftaran',
        html: `
            <div class="text-start">
                <p class="text-muted mb-4">Masukkan nomor pendaftaran dan NIK untuk melihat status pendaftaran PPDB Anda</p>

                <form id="checkStatusForm">
                    <div class="mb-3">
                        <label class="form-label" style="color: #333; font-weight: 600;">Nomor Pendaftaran <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="registration_number" placeholder="Contoh: PPDB20240001" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Nomor pendaftaran yang Anda terima setelah mendaftar
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label" style="color: #333; font-weight: 600;">NIK <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="nik" placeholder="16 digit NIK" maxlength="16" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            NIK yang Anda gunakan saat mendaftar
                        </div>
                    </div>
                </form>

                <div class="alert alert-light mt-3">
                    <h6 style="color: #333; font-weight: 600;"><i class="fas fa-question-circle me-2"></i>Bantuan</h6>
                    <p class="mb-2" style="color: #333;"><strong>Tidak ingat nomor pendaftaran?</strong></p>
                    <ul class="small mb-0" style="color: #333;">
                        <li>Cek email yang Anda gunakan saat mendaftar</li>
                        <li>Format nomor: PPDB[TAHUN][NOMOR URUT]</li>
                    </ul>
                </div>
            </div>
        `,
        width: '600px',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-search me-2"></i>Cek Status',
        cancelButtonText: 'Batal',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        showLoaderOnConfirm: true,
        preConfirm: () => {
            return submitCheckStatusForm();
        },
        allowOutsideClick: () => !Swal.isLoading()
    });

    // Add input validation after modal is shown
    setTimeout(() => {
        // NIK validation
        const nikInput = document.querySelector('input[name="nik"]');
        if (nikInput) {
            nikInput.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 16);
            });
        }

        // Registration number formatting
        const regInput = document.querySelector('input[name="registration_number"]');
        if (regInput) {
            regInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });
        }
    }, 100);
}

// Function to submit registration form
async function submitRegistrationForm() {
    console.log('🚀 Starting form submission...');

    const form = document.getElementById('ppdbModalForm');
    if (!form) {
        console.error('❌ Form not found!');
        Swal.showValidationMessage('Form tidak ditemukan');
        return false;
    }

    const formData = new FormData(form);

    // Add CSRF token
    formData.append('_token', ppdbData.csrfToken);

    // Debug: Log form data
    console.log('📝 Form data:');
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}: ${value}`);
    }

    console.log('🌐 Sending request to:', ppdbData.registerUrl);

    try {
        const response = await fetch(ppdbData.registerUrl, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        });

        console.log('📡 Response status:', response.status);
        console.log('📡 Response headers:', response.headers);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ HTTP Error Response:', errorText);
            throw new Error(`HTTP error! status: ${response.status} - ${errorText.substring(0, 100)}...`);
        }

        const contentType = response.headers.get('content-type');
        console.log('📄 Content-Type:', contentType);

        if (!contentType || !contentType.includes('application/json')) {
            const responseText = await response.text();
            console.error('❌ Non-JSON Response:', responseText.substring(0, 500));
            throw new Error('Server mengembalikan response yang tidak valid. Silakan coba lagi.');
        }

        const result = await response.json();
        console.log('✅ JSON Response:', result);

        if (result.success) {
            console.log('🎉 Registration successful!');
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Pendaftaran Berhasil!',
                html: `
                    <div class="text-center">
                        <h5 class="text-success mb-3">🎉 Selamat!</h5>
                        <p class="text-dark">Pendaftaran PPDB Anda telah berhasil diproses.</p>
                        <div class="bg-light rounded p-3 mb-3">
                            <strong class="text-dark">Nomor Pendaftaran:</strong><br>
                            <code class="fs-5 text-primary">${result.registration_number}</code>
                        </div>
                        <p class="small text-muted">
                            <i class="fas fa-envelope me-1"></i>
                            Silakan cek email Anda untuk informasi login ke Portal Calon Siswa
                        </p>
                    </div>
                `,
                confirmButtonText: 'Lanjutkan',
                confirmButtonColor: '#28a745',
                allowOutsideClick: false
            }).then(() => {
                if (result.redirect_url) {
                    window.location.href = result.redirect_url;
                } else {
                    window.location.reload();
                }
            });
        } else {
            console.error('❌ Registration failed:', result.message);
            throw new Error(result.message || 'Terjadi kesalahan saat mendaftar');
        }
    } catch (error) {
        console.error('💥 Submission error:', error);
        Swal.showValidationMessage(`Error: ${error.message}`);
        return false;
    }
}

// Function to submit check status form
async function submitCheckStatusForm() {
    const form = document.getElementById('checkStatusForm');
    const formData = new FormData(form);

    // Add CSRF token
    formData.append('_token', ppdbData.csrfToken);

    try {
        const response = await fetch('{{ route('ppdb.check-status.post') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // Redirect to status page
            window.location.href = result.redirect_url;
        } else {
            throw new Error(result.message || 'Data tidak ditemukan');
        }
    } catch (error) {
        Swal.showValidationMessage(`Error: ${error.message}`);
        return false;
    }
}
</script>
@endpush
