<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class BackupDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:database {--format=sql : Backup format (sql)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a backup of the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting database backup...');

        try {
            $format = $this->option('format');
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = "backup_database_{$timestamp}.{$format}";
            
            // Create backups directory if it doesn't exist
            $backupPath = storage_path('app/backups');
            if (!file_exists($backupPath)) {
                mkdir($backupPath, 0755, true);
            }

            $fullPath = $backupPath . '/' . $filename;

            if ($format === 'sql') {
                $this->createSqlBackup($fullPath);
            } else {
                $this->error('Unsupported backup format. Use: sql');
                return 1;
            }

            $this->info("Database backup created successfully: {$filename}");
            $this->info("Location: {$fullPath}");
            $this->info("Size: " . $this->formatBytes(filesize($fullPath)));

            return 0;

        } catch (\Exception $e) {
            $this->error('Backup failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Create SQL backup
     */
    private function createSqlBackup($filePath)
    {
        $dbHost = config('database.connections.mysql.host');
        $dbPort = config('database.connections.mysql.port');
        $dbName = config('database.connections.mysql.database');
        $dbUser = config('database.connections.mysql.username');
        $dbPass = config('database.connections.mysql.password');

        // Use mysqldump if available
        $mysqldumpPath = $this->findMysqldump();
        
        if ($mysqldumpPath) {
            $command = sprintf(
                '"%s" --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > "%s"',
                $mysqldumpPath,
                $dbHost,
                $dbPort,
                $dbUser,
                $dbPass,
                $dbName,
                $filePath
            );

            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                throw new \Exception('mysqldump failed with return code: ' . $returnCode);
            }
        } else {
            // Fallback to PHP-based backup
            $this->createPhpSqlBackup($filePath);
        }
    }

    /**
     * Find mysqldump executable
     */
    private function findMysqldump()
    {
        // Common paths for mysqldump
        $paths = [
            'mysqldump', // If in PATH
            'C:\xampp\mysql\bin\mysqldump.exe', // XAMPP Windows
            '/usr/bin/mysqldump', // Linux
            '/usr/local/bin/mysqldump', // macOS
        ];

        foreach ($paths as $path) {
            if (is_executable($path) || (PHP_OS_FAMILY === 'Windows' && is_file($path))) {
                return $path;
            }
        }

        return null;
    }

    /**
     * Create PHP-based SQL backup
     */
    private function createPhpSqlBackup($filePath)
    {
        $sql = "-- Database Backup\n";
        $sql .= "-- Generated on: " . Carbon::now()->format('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: " . config('database.connections.mysql.database') . "\n\n";

        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

        // Get all tables
        $tables = DB::select('SHOW TABLES');
        $dbName = config('database.connections.mysql.database');
        $tableKey = "Tables_in_{$dbName}";

        foreach ($tables as $table) {
            $tableName = $table->$tableKey;
            
            // Get table structure
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
            $sql .= "-- Table structure for `{$tableName}`\n";
            $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
            $sql .= $createTable->{'Create Table'} . ";\n\n";

            // Get table data
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $sql .= "-- Data for table `{$tableName}`\n";
                $sql .= "INSERT INTO `{$tableName}` VALUES\n";
                
                $values = [];
                foreach ($rows as $row) {
                    $rowData = [];
                    foreach ($row as $value) {
                        if (is_null($value)) {
                            $rowData[] = 'NULL';
                        } else {
                            $rowData[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(',', $rowData) . ')';
                }
                
                $sql .= implode(",\n", $values) . ";\n\n";
            }
        }

        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";

        file_put_contents($filePath, $sql);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
