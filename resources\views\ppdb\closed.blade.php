@extends('layouts.landing')

@section('title', 'PPDB Online - Pendaftaran Ditutup')
@section('description', 'Informasi status PPDB Online saat ini.')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <!-- Status Icon -->
            <div class="mb-4">
                <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                    <i class="fas fa-exclamation-triangle fa-4x text-warning"></i>
                </div>
            </div>
            
            <!-- Main Message -->
            <h1 class="display-5 fw-bold mb-3">PPDB Online</h1>
            <div class="alert alert-warning border-0 shadow-sm">
                <h4 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>Informasi PPDB
                </h4>
                <p class="mb-0 fs-5">{{ $message }}</p>
            </div>
            
            @if(isset($setting))
            <!-- PPDB Information -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">
                        <i class="fas fa-calendar-alt me-2 text-primary"></i>
                        Informasi PPDB Tahun Ajaran {{ $setting->academic_year }}
                    </h5>
                    
                    <div class="row text-start">
                        <div class="col-md-6 mb-3">
                            <strong>Status PPDB:</strong>
                            <span class="badge bg-{{ $setting->status == 'open' ? 'success' : ($setting->status == 'maintenance' ? 'warning' : 'danger') }} ms-2">
                                {{ ucfirst($setting->status) }}
                            </span>
                        </div>
                        
                        @if($setting->registration_start && $setting->registration_end)
                        <div class="col-md-6 mb-3">
                            <strong>Jadwal Pendaftaran:</strong><br>
                            <small class="text-muted">
                                {{ $setting->registration_start->format('d F Y') }} -
                                {{ $setting->registration_end->format('d F Y') }}
                            </small>
                        </div>
                        @endif

                        @if($setting->announcement_date)
                        <div class="col-md-6 mb-3">
                            <strong>Pengumuman Hasil:</strong><br>
                            <small class="text-muted">{{ $setting->announcement_date->format('d F Y') }}</small>
                        </div>
                        @endif
                    </div>
                    
                    @if($setting->announcement_text)
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6><i class="fas fa-bullhorn me-2 text-primary"></i>Pengumuman:</h6>
                        <p class="mb-0 text-muted">{!! nl2br(e($setting->announcement_text)) !!}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif
            
            <!-- Action Buttons -->
            <div class="mt-5">
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{{ route('ppdb.check-status') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>Cek Status Pendaftaran
                    </a>
                    <a href="{{ route('landing') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-home me-2"></i>Kembali ke Beranda
                    </a>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="mt-5">
                <div class="card border-0 bg-light">
                    <div class="card-body p-4">
                        <h6 class="card-title">
                            <i class="fas fa-phone me-2 text-success"></i>Butuh Bantuan?
                        </h6>
                        <p class="card-text mb-0">
                            Hubungi kami di <strong>(*************</strong> atau email ke 
                            <strong><EMAIL></strong> untuk informasi lebih lanjut.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
