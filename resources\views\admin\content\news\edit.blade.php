@extends('layouts.dashboard')

@section('title', 'Edit Berita')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Edit Berita</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.content.news.index') }}">Berita & Pengumuman</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.content.news.show', $news) }}">{{ $news->title }}</a></li>
                <li class="breadcrumb-item active">Edit</li>
            </ol>
        </nav>
    </div>
    <a href="{{ route('admin.content.news.show', $news) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i><PERSON><PERSON><PERSON>
    </a>
</div>

<form action="{{ route('admin.content.news.update', $news) }}" method="POST" enctype="multipart/form-data">
    @csrf
    @method('PUT')
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Konten Berita</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Judul <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title', $news->title) }}" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('slug') is-invalid @enderror"
                                   id="slug" name="slug" value="{{ old('slug', $news->slug) }}" required>
                            <button type="button" class="btn btn-outline-secondary" id="check-slug">
                                <i class="fas fa-search"></i> Cek
                            </button>
                        </div>
                        @error('slug')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div id="slug-feedback" class="mt-1"></div>
                        <small class="text-muted">URL-friendly version of the title</small>
                        <input type="hidden" id="current-news-id" value="{{ $news->id }}">
                    </div>

                    <div class="mb-3">
                        <label for="excerpt" class="form-label">Ringkasan</label>
                        <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                                  id="excerpt" name="excerpt" rows="3">{{ old('excerpt', $news->excerpt) }}</textarea>
                        @error('excerpt')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Ringkasan singkat yang akan ditampilkan di daftar berita</small>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">Konten <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content" name="content" rows="15" style="display: none;">{{ old('content', $news->content) }}</textarea>
                        <div id="editor-container">
                            <div id="editor"></div>
                        </div>
                        @error('content')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                        <div id="content-error" class="invalid-feedback" style="display: none;">
                            Konten berita wajib diisi.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="tags" class="form-label">Tags</label>
                        <input type="text" class="form-control @error('tags') is-invalid @enderror" 
                               id="tags" name="tags" value="{{ old('tags', $news->tags) }}" 
                               placeholder="tag1, tag2, tag3">
                        @error('tags')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Pisahkan dengan koma</small>
                    </div>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                               id="meta_title" name="meta_title" value="{{ old('meta_title', $news->meta_title) }}" 
                               maxlength="60">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Maksimal 60 karakter</small>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                  id="meta_description" name="meta_description" rows="3" 
                                  maxlength="160">{{ old('meta_description', $news->meta_description) }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Maksimal 160 karakter</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pengaturan Publikasi</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select @error('status') is-invalid @enderror" 
                                id="status" name="status" required>
                            <option value="draft" {{ old('status', $news->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="published" {{ old('status', $news->status) == 'published' ? 'selected' : '' }}>Published</option>
                            <option value="archived" {{ old('status', $news->status) == 'archived' ? 'selected' : '' }}>Archived</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Tipe <span class="text-danger">*</span></label>
                        <select class="form-select @error('type') is-invalid @enderror" 
                                id="type" name="type" required>
                            <option value="news" {{ old('type', $news->type) == 'news' ? 'selected' : '' }}>Berita</option>
                            <option value="announcement" {{ old('type', $news->type) == 'announcement' ? 'selected' : '' }}>Pengumuman</option>
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="category" class="form-label">Kategori</label>
                        <input type="text" class="form-control @error('category') is-invalid @enderror" 
                               id="category" name="category" value="{{ old('category', $news->category) }}">
                        @error('category')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="published_at" class="form-label">Tanggal Publikasi</label>
                        <input type="datetime-local" class="form-control @error('published_at') is-invalid @enderror" 
                               id="published_at" name="published_at" 
                               value="{{ old('published_at', $news->published_at ? $news->published_at->format('Y-m-d\TH:i') : '') }}">
                        @error('published_at')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Kosongkan untuk publikasi sekarang</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" 
                                   {{ old('is_featured', $news->is_featured) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_featured">
                                Featured Article
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Gambar Utama</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <input type="file" class="form-control @error('featured_image') is-invalid @enderror" 
                               id="featured_image" name="featured_image" accept="image/*">
                        @error('featured_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB. Kosongkan jika tidak ingin mengubah.</small>
                    </div>
                    
                    <!-- Current Image -->
                    @if($news->featured_image)
                        <div class="mb-3" id="current-image-section">
                            <label class="form-label">Gambar Saat Ini:</label>
                            <div class="position-relative d-inline-block">
                                <img src="{{ Storage::url($news->featured_image) }}" alt="Current Image"
                                     class="img-fluid rounded" style="max-height: 200px;">
                                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1 remove-image"
                                        data-news-id="{{ $news->id }}" title="Hapus Gambar">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Klik tombol X untuk menghapus gambar</small>
                            </div>


                        </div>
                    @endif
                    
                    <!-- Preview New Image -->
                    <div id="image-preview" class="mb-3" style="display: none;">
                        <label class="form-label">Preview Gambar Baru:</label>
                        <div>
                            <img id="preview-img" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Berita
                        </button>
                        <a href="{{ route('admin.content.news.show', $news) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Batal
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
#editor {
    height: 300px;
}

.ql-editor {
    min-height: 200px;
}

#editor-container.is-invalid .ql-container {
    border-color: #dc3545;
}

#editor-container.is-invalid .ql-toolbar {
    border-color: #dc3545;
}

.remove-image {
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.remove-image:hover {
    opacity: 1;
}

.position-relative:hover .remove-image {
    opacity: 1;
}

.image-preview {
    max-width: 200px;
    max-height: 200px;
    object-fit: cover;
    border-radius: 8px;
}

#check-slug {
    border-left: 0;
}

#slug-feedback {
    font-size: 0.875rem;
}

.input-group .form-control.is-valid {
    border-right: 0;
}

.input-group .form-control.is-invalid {
    border-right: 0;
}

.input-group .btn:not(:last-child) {
    border-right: 1px solid #dee2e6;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Quill editor
    var quill = new Quill('#editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                ['link', 'image'],
                [{ 'align': [] }],
                ['clean']
            ]
        }
    });

    // Set initial content
    var initialContent = $('#content').val();
    if (initialContent) {
        quill.root.innerHTML = initialContent;
    }

    // Update hidden textarea when form is submitted
    $('form').on('submit', function(e) {
        const content = quill.root.innerHTML.trim();
        const textContent = quill.getText().trim();
        const slug = $('#slug').val();

        // Update hidden textarea
        $('#content').val(content);

        // Custom validation for Quill editor
        if (!textContent || textContent.length === 0) {
            e.preventDefault();
            $('#editor-container').addClass('is-invalid');
            $('#content-error').show();

            // Scroll to editor
            $('html, body').animate({
                scrollTop: $('#editor-container').offset().top - 100
            }, 500);

            // Focus on editor
            quill.focus();

            return false;
        } else {
            $('#editor-container').removeClass('is-invalid');
            $('#content-error').hide();
        }

        // Validate slug before submit
        if ($('#slug').hasClass('is-invalid')) {
            e.preventDefault();

            Swal.fire({
                title: 'Slug Tidak Valid!',
                text: 'Slug yang Anda gunakan sudah ada atau tidak valid. Silakan gunakan slug yang lain.',
                icon: 'error',
                confirmButtonText: 'OK'
            }).then(() => {
                // Scroll to slug field
                $('html, body').animate({
                    scrollTop: $('#slug').offset().top - 100
                }, 500);
                $('#slug').focus();
            });

            return false;
        }
    });

    // Real-time content validation
    quill.on('text-change', function() {
        const textContent = quill.getText().trim();
        if (textContent && textContent.length > 0) {
            $('#editor-container').removeClass('is-invalid');
            $('#content-error').hide();
        }
    });
    // Auto-generate slug from title
    $('#title').on('input', function() {
        const title = $(this).val();
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .trim('-'); // Remove leading/trailing hyphens
        $('#slug').val(slug);

        // Clear previous slug feedback when title changes
        $('#slug-feedback').html('');
        $('#slug').removeClass('is-valid is-invalid');
    });

    // Check slug availability (for edit mode)
    function checkSlugAvailability(slug, callback) {
        if (!slug) {
            callback(false, 'Slug tidak boleh kosong.');
            return;
        }

        const currentNewsId = $('#current-news-id').val();

        $.ajax({
            url: '/admin/content/news/check-slug',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                slug: slug,
                exclude_id: currentNewsId
            }),
            success: function(response) {
                callback(response.available, response.message);
            },
            error: function() {
                callback(false, 'Terjadi kesalahan saat mengecek slug.');
            }
        });
    }

    // Manual slug check button
    $('#check-slug').click(function() {
        const slug = $('#slug').val();
        const button = $(this);
        const icon = button.find('i');

        // Show loading
        icon.removeClass('fa-search').addClass('fa-spinner fa-spin');
        button.prop('disabled', true);

        checkSlugAvailability(slug, function(available, message) {
            // Reset button
            icon.removeClass('fa-spinner fa-spin').addClass('fa-search');
            button.prop('disabled', false);

            // Show feedback
            const feedbackClass = available ? 'text-success' : 'text-danger';
            const iconClass = available ? 'fa-check-circle' : 'fa-exclamation-circle';

            $('#slug-feedback').html(`
                <small class="${feedbackClass}">
                    <i class="fas ${iconClass} me-1"></i>${message}
                </small>
            `);

            // Update input styling
            $('#slug').removeClass('is-valid is-invalid')
                     .addClass(available ? 'is-valid' : 'is-invalid');
        });
    });

    // Auto-check slug when user stops typing
    let slugCheckTimeout;
    $('#slug').on('input', function() {
        const slug = $(this).val();

        // Clear previous timeout
        clearTimeout(slugCheckTimeout);

        // Clear feedback
        $('#slug-feedback').html('');
        $(this).removeClass('is-valid is-invalid');

        // Set new timeout
        slugCheckTimeout = setTimeout(function() {
            if (slug) {
                checkSlugAvailability(slug, function(available, message) {
                    const feedbackClass = available ? 'text-success' : 'text-danger';
                    const iconClass = available ? 'fa-check-circle' : 'fa-exclamation-circle';

                    $('#slug-feedback').html(`
                        <small class="${feedbackClass}">
                            <i class="fas ${iconClass} me-1"></i>${message}
                        </small>
                    `);

                    $('#slug').removeClass('is-valid is-invalid')
                             .addClass(available ? 'is-valid' : 'is-invalid');
                });
            }
        }, 1000); // Check after 1 second of no typing
    });

    // Image preview
    $('#featured_image').change(function() {
        const file = this.files[0];
        if (file) {
            // Check file size (2MB = 2048KB)
            if (file.size > 2048 * 1024) {
                alert('Ukuran file terlalu besar. Maksimal 2MB.');
                $(this).val('');
                $('#image-preview').hide();
                return;
            }

            // Check file type
            if (!file.type.match('image.*')) {
                alert('File harus berupa gambar.');
                $(this).val('');
                $('#image-preview').hide();
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').hide();
        }
    });

    // Character counter for meta fields
    $('#meta_title').on('input', function() {
        const length = $(this).val().length;
        const maxLength = 60;
        const remaining = maxLength - length;
        
        // Remove existing counter
        $(this).siblings('.char-counter').remove();
        
        // Add counter
        $(this).after(`<small class="char-counter text-muted">${length}/${maxLength} karakter</small>`);
        
        if (remaining < 10) {
            $(this).siblings('.char-counter').addClass('text-warning');
        }
        if (remaining < 0) {
            $(this).siblings('.char-counter').removeClass('text-warning').addClass('text-danger');
        }
    });

    $('#meta_description').on('input', function() {
        const length = $(this).val().length;
        const maxLength = 160;
        const remaining = maxLength - length;
        
        // Remove existing counter
        $(this).siblings('.char-counter').remove();
        
        // Add counter
        $(this).after(`<small class="char-counter text-muted">${length}/${maxLength} karakter</small>`);
        
        if (remaining < 20) {
            $(this).siblings('.char-counter').addClass('text-warning');
        }
        if (remaining < 0) {
            $(this).siblings('.char-counter').removeClass('text-warning').addClass('text-danger');
        }
    });

    // Trigger character counter on page load
    $('#meta_title').trigger('input');
    $('#meta_description').trigger('input');



    // Remove image functionality
    $('.remove-image').click(function() {
        const newsId = $(this).data('news-id');

        Swal.fire({
            title: 'Konfirmasi Hapus Gambar',
            text: 'Apakah Anda yakin ingin menghapus gambar ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'Menghapus Gambar...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: `/admin/content/news/${newsId}/remove-image`,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });

                            // Remove the current image section
                            $('#current-image-section').fadeOut(300, function() {
                                $(this).remove();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Remove Image Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat menghapus gambar.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
});
</script>
@endpush
