<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class PPDBDocument extends Model
{
    use HasFactory;

    protected $table = 'ppdb_documents';

    protected $fillable = [
        'ppdb_registration_id',
        'document_type',
        'document_name',
        'file_path',
        'file_size',
        'mime_type',
        'status',
        'rejection_reason',
        'verified_by',
        'verified_at',
    ];

    protected function casts(): array
    {
        return [
            'verified_at' => 'datetime',
        ];
    }

    public function registration()
    {
        return $this->belongsTo(PPDBRegistration::class, 'ppdb_registration_id');
    }

    public function verifiedBy()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function getFileUrlAttribute()
    {
        return Storage::url($this->file_path);
    }

    public function getFileSizeFormattedAttribute()
    {
        $bytes = $this->file_size;
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Menunggu Verifikasi',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
        ];

        return $labels[$this->status] ?? 'Unknown';
    }

    public function getDocumentTypeNameAttribute()
    {
        $types = [
            'ijazah' => 'Ijazah/SKHUN',
            'kartu_keluarga' => 'Kartu Keluarga',
            'akta_kelahiran' => 'Akta Kelahiran',
            'pas_foto' => 'Pas Foto 3x4',
            'surat_sehat' => 'Surat Keterangan Sehat',
            'rapor' => 'Rapor Semester Terakhir',
            'surat_kelakuan_baik' => 'Surat Kelakuan Baik',
            'piagam_prestasi' => 'Piagam Prestasi (Opsional)',
        ];

        return $types[$this->document_type] ?? ucfirst(str_replace('_', ' ', $this->document_type));
    }
}
