<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SecurityLog;
use App\Models\User;
use Carbon\Carbon;

class SecurityController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:super_admin');
    }

    public function index(Request $request)
    {
        $query = SecurityLog::with('user')->latest();

        // Apply filters
        if ($request->filled('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%")
                  ->orWhere('event_type', 'like', "%{$search}%");
            });
        }

        $logs = $query->paginate(20);

        // Statistics
        $stats = [
            'total_logs' => SecurityLog::count(),
            'today_logs' => SecurityLog::whereDate('created_at', today())->count(),
            'high_risk' => SecurityLog::where('risk_level', 'high')->orWhere('risk_level', 'critical')->count(),
            'unresolved' => SecurityLog::where('is_resolved', false)->count(),
        ];

        // Event types for filter
        $eventTypes = SecurityLog::distinct('event_type')->pluck('event_type');

        // Users for filter
        $users = User::select('id', 'name', 'email')->get();

        return view('admin.security.index', compact('logs', 'stats', 'eventTypes', 'users'));
    }

    public function show(SecurityLog $securityLog)
    {
        $securityLog->load('user');
        return view('admin.security.show', compact('securityLog'));
    }

    public function resolve(SecurityLog $securityLog)
    {
        $securityLog->update([
            'is_resolved' => true,
            'resolved_at' => now(),
        ]);

        return redirect()->route('admin.security.index')
            ->with('success', 'Log keamanan berhasil ditandai sebagai resolved.');
    }

    public function bulkResolve(Request $request)
    {
        $request->validate([
            'log_ids' => 'required|array',
            'log_ids.*' => 'exists:security_logs,id',
        ]);

        SecurityLog::whereIn('id', $request->log_ids)
            ->update([
                'is_resolved' => true,
                'resolved_at' => now(),
            ]);

        return redirect()->route('admin.security.index')
            ->with('success', 'Log keamanan yang dipilih berhasil ditandai sebagai resolved.');
    }

    public function destroy(SecurityLog $securityLog)
    {
        $securityLog->delete();

        return response()->json([
            'success' => true,
            'message' => 'Log keamanan berhasil dihapus.'
        ]);
    }

    public function deleteAll(Request $request)
    {
        $request->validate([
            'confirm' => 'required|in:DELETE_ALL_LOGS'
        ]);

        $count = SecurityLog::count();
        SecurityLog::truncate();

        // Log this critical action
        SecurityLog::logEvent('security_logs_deleted_all', auth()->id(), [
            'deleted_count' => $count,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return response()->json([
            'success' => true,
            'message' => "Semua log keamanan ({$count} log) berhasil dihapus."
        ]);
    }

    public function dashboard()
    {
        // Real statistics from SecurityLog
        $stats = [
            'total_logs' => SecurityLog::count(),
            'today_logs' => SecurityLog::whereDate('created_at', today())->count(),
            'this_week_logs' => SecurityLog::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month_logs' => SecurityLog::whereBetween('created_at', [
                now()->startOfMonth(),
                now()->endOfMonth()
            ])->count(),
            'high_risk_logs' => SecurityLog::whereIn('risk_level', ['high', 'critical'])->count(),
            'unresolved_logs' => SecurityLog::where('is_resolved', false)->count(),
        ];

        // Recent high risk activities
        $recentHighRisk = SecurityLog::with('user')
            ->whereIn('risk_level', ['high', 'critical'])
            ->latest()
            ->limit(10)
            ->get();

        // Today's login activities
        $todayLogins = SecurityLog::with('user')
            ->where('event_type', 'login_success')
            ->whereDate('created_at', today())
            ->latest()
            ->limit(10)
            ->get();

        // Recent failed logins
        $failedLogins = SecurityLog::with('user')
            ->where('event_type', 'login_failed')
            ->latest()
            ->limit(10)
            ->get();

        // Event type statistics
        $eventTypeStats = SecurityLog::selectRaw('event_type, COUNT(*) as count')
            ->groupBy('event_type')
            ->pluck('count', 'event_type');

        // Daily activity data for the last 30 days
        $dailyActivity = collect();
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = SecurityLog::whereDate('created_at', $date->format('Y-m-d'))->count();

            $dailyActivity->push((object)[
                'date' => $date->format('Y-m-d'),
                'count' => $count
            ]);
        }

        return view('admin.security.dashboard', compact(
            'stats',
            'recentHighRisk',
            'todayLogins',
            'failedLogins',
            'eventTypeStats',
            'dailyActivity'
        ));
    }

    public function userActivity(Request $request)
    {
        $query = User::withCount('securityLogs');

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        if ($request->filled('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        $users = $query->paginate(20);

        return view('admin.security.user-activity', compact('users'));
    }

    public function userActivityDetail(User $user, Request $request)
    {
        $query = SecurityLog::where('user_id', $user->id);

        if ($request->filled('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->latest()->paginate(20);

        $stats = [
            'total_activities' => SecurityLog::where('user_id', $user->id)->count(),
            'today_activities' => SecurityLog::where('user_id', $user->id)
                ->whereDate('created_at', today())->count(),
            'failed_logins' => SecurityLog::where('user_id', $user->id)
                ->where('event_type', 'login_failed')->count(),
            'last_login' => SecurityLog::where('user_id', $user->id)
                ->where('event_type', 'login_success')
                ->latest()
                ->first(),
            'high_risk_activities' => SecurityLog::where('user_id', $user->id)
                ->whereIn('risk_level', ['high', 'critical'])->count(),
            'this_week_activities' => SecurityLog::where('user_id', $user->id)
                ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
        ];

        // Get event types for filter
        $eventTypes = SecurityLog::where('user_id', $user->id)
            ->distinct('event_type')
            ->pluck('event_type');

        return view('admin.security.user-detail', compact('user', 'logs', 'stats', 'eventTypes'));
    }

    public function export(Request $request)
    {
        $query = SecurityLog::with('user');

        // Apply same filters as index
        if ($request->filled('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->latest()->get();

        $filename = 'security_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID',
                'User',
                'Email',
                'Event Type',
                'Description',
                'IP Address',
                'Risk Level',
                'Created At',
                'Resolved'
            ]);

            // CSV data
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->user ? $log->user->name : 'N/A',
                    $log->user ? $log->user->email : 'N/A',
                    $log->event_type,
                    $log->description,
                    $log->ip_address,
                    $log->risk_level,
                    $log->created_at->format('Y-m-d H:i:s'),
                    $log->is_resolved ? 'Yes' : 'No'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
