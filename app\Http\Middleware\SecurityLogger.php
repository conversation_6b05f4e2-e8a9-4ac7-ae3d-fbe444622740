<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Models\SecurityLog;

class SecurityLogger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log sensitive data access
        if ($this->isSensitiveRoute($request)) {
            $route = $request->route();
            SecurityLog::logEvent('data_access', Auth::id(), [
                'route' => $route ? $route->getName() : 'unknown',
                'url' => $request->fullUrl(),
                'method' => $request->method(),
            ]);
        }

        $response = $next($request);

        // Log data modifications
        if ($this->isModificationRequest($request)) {
            $route = $request->route();
            SecurityLog::logEvent('data_modification', Auth::id(), [
                'route' => $route ? $route->getName() : 'unknown',
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'data' => $this->sanitizeData($request->all()),
            ]);
        }

        return $response;
    }

    private function isSensitiveRoute(Request $request): bool
    {
        $sensitiveRoutes = [
            'admin.*',
            'users.*',
            'admissions.*',
            'settings.*',
        ];

        $route = $request->route();
        if (!$route) {
            return false;
        }

        $currentRoute = $route->getName();
        if (!$currentRoute) {
            return false;
        }

        foreach ($sensitiveRoutes as $pattern) {
            if (fnmatch($pattern, $currentRoute)) {
                return true;
            }
        }

        return false;
    }

    private function isModificationRequest(Request $request): bool
    {
        return in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    private function sanitizeData(array $data): array
    {
        // Remove sensitive fields from logging
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'remember_token'];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }
}
