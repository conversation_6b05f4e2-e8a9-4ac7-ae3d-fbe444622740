<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('classes', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // X IPA 1, XI IPS 2, XII BAHASA 1
            $table->string('level'); // X, XI, XII
            $table->string('major')->nullable(); // IPA, IPS, BAHASA
            $table->integer('class_number'); // 1, 2, 3
            $table->string('academic_year'); // 2024/2025
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['level', 'major', 'class_number']);
            $table->index(['academic_year', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('classes');
    }
};
