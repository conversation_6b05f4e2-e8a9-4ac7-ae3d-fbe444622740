@extends('layouts.dashboard')

@section('title', 'Manajemen Fasilitas')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Manajemen Fasilitas</h1>
    <a href="{{ route('admin.content.facilities.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Tambah Fasilitas
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-building"></i>
            </div>
            <div class="stats-number">{{ $stats['total_facilities'] }}</div>
            <div class="stats-label">Total Fasilitas</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number">{{ $stats['available'] }}</div>
            <div class="stats-label">Tersedia</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-star"></i>
            </div>
            <div class="stats-number">{{ $stats['featured'] }}</div>
            <div class="stats-label">Featured</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.content.facilities.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="category" class="form-label">Kategori</label>
                <select name="category" id="category" class="form-select">
                    <option value="">Semua Kategori</option>
                    <option value="academic" {{ request('category') == 'academic' ? 'selected' : '' }}>Academic</option>
                    <option value="sports" {{ request('category') == 'sports' ? 'selected' : '' }}>Sports</option>
                    <option value="library" {{ request('category') == 'library' ? 'selected' : '' }}>Library</option>
                    <option value="laboratory" {{ request('category') == 'laboratory' ? 'selected' : '' }}>Laboratory</option>
                    <option value="dormitory" {{ request('category') == 'dormitory' ? 'selected' : '' }}>Dormitory</option>
                    <option value="cafeteria" {{ request('category') == 'cafeteria' ? 'selected' : '' }}>Cafeteria</option>
                    <option value="mosque" {{ request('category') == 'mosque' ? 'selected' : '' }}>Mosque</option>
                    <option value="other" {{ request('category') == 'other' ? 'selected' : '' }}>Other</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">Pencarian</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari nama fasilitas..." 
                       value="{{ request('search') }}">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ route('admin.content.facilities.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Facilities Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Daftar Fasilitas</h5>
    </div>
    <div class="card-body">
        @if($facilities->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Gambar</th>
                            <th>Nama Fasilitas</th>
                            <th>Kategori</th>
                            <th>Kapasitas</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($facilities as $facility)
                        <tr>
                            <td>
                                <div class="facility-image">
                                    @if($facility->image)
                                        <img src="{{ Storage::url($facility->image) }}" alt="{{ $facility->name }}" class="rounded">
                                    @else
                                        <div class="image-placeholder rounded d-flex align-items-center justify-content-center">
                                            <i class="fas fa-building text-muted"></i>
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $facility->name }}</strong>
                                    @if($facility->is_featured)
                                        <i class="fas fa-star text-warning ms-1" title="Featured"></i>
                                    @endif
                                    <br>
                                    <small class="text-muted">{{ Str::limit($facility->description, 60) }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ $facility->category == 'academic' ? 'primary' : ($facility->category == 'sports' ? 'success' : ($facility->category == 'library' ? 'info' : 'secondary')) }}">
                                    {{ ucfirst($facility->category) }}
                                </span>
                            </td>
                            <td>
                                <small>{{ $facility->capacity ? number_format($facility->capacity) . ' orang' : '-' }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $facility->is_available ? 'success' : 'danger' }}">
                                    {{ $facility->is_available ? 'Tersedia' : 'Tidak Tersedia' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.content.facilities.show', $facility) }}" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.content.facilities.edit', $facility) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('landing.facility', $facility->slug) }}" class="btn btn-sm btn-outline-success" title="Lihat di Website" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteFacility('{{ $facility->slug }}', '{{ addslashes($facility->name) }}')"
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan {{ $facilities->firstItem() }} - {{ $facilities->lastItem() }} dari {{ $facilities->total() }} fasilitas
                </div>
                {{ $facilities->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada fasilitas ditemukan</h5>
                <p class="text-muted">Belum ada fasilitas yang sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('admin.content.facilities.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Tambah Fasilitas Pertama
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@push('styles')
<style>
.facility-image {
    width: 60px;
    height: 40px;
}

.facility-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-placeholder {
    width: 60px;
    height: 40px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteFacility(slug, name) {
    Swal.fire({
        title: 'Hapus Fasilitas?',
        text: `Apakah Anda yakin ingin menghapus fasilitas "${name}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus fasilitas',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send AJAX DELETE request
            $.ajax({
                url: `/admin/content/facilities/${slug}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Fasilitas berhasil dihapus.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    console.error('Delete error:', xhr);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus fasilitas: ' + (xhr.responseJSON?.message || 'Unknown error'),
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>
@endpush
