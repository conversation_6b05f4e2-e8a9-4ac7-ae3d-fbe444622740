<?php $__env->startSection('title', 'Berita & Pengumuman'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Berita & Pengumuman</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item">Manajemen Konten</li>
                    <li class="breadcrumb-item active">Berita & Pengumuman</li>
                </ol>
            </nav>
        </div>
        <a href="<?php echo e(route('admin.content.news.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Tambah Berita
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle bg-primary">
                        <i class="fas fa-newspaper text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['total'])); ?></div>
                    <div class="stats-label-clean">Total Berita & Pengumuman</div>
                </div>
            </div>
        </div>

        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle bg-success">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['published'])); ?></div>
                    <div class="stats-label-clean">Published</div>
                </div>
            </div>
        </div>

        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle bg-warning">
                        <i class="fas fa-edit text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['draft'])); ?></div>
                    <div class="stats-label-clean">Draft</div>
                </div>
            </div>
        </div>

        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle bg-danger">
                        <i class="fas fa-archive text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['archived'])); ?></div>
                    <div class="stats-label-clean">Archived</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Stats Row -->
    <div class="row mb-4">
        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle bg-info">
                        <i class="fas fa-newspaper text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['news'])); ?></div>
                    <div class="stats-label-clean">Berita</div>
                </div>
            </div>
        </div>

        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle bg-secondary">
                        <i class="fas fa-bullhorn text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['announcements'])); ?></div>
                    <div class="stats-label-clean">Pengumuman</div>
                </div>
            </div>
        </div>

        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle bg-dark">
                        <i class="fas fa-star text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['featured'])); ?></div>
                    <div class="stats-label-clean">Featured</div>
                </div>
            </div>
        </div>

        <div class="col-lg col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 stats-card-clean">
                <div class="card-body text-center">
                    <div class="stats-icon-circle" style="background-color: #6f42c1;">
                        <i class="fas fa-calendar text-white"></i>
                    </div>
                    <div class="stats-number-clean"><?php echo e(number_format($stats['this_month'])); ?></div>
                    <div class="stats-label-clean">Bulan Ini</div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-newspaper me-2"></i>Daftar Berita & Pengumuman
                </h5>
            </div>
        </div>
        <div class="card-body">
            <?php if($news->count() > 0): ?>
                <!-- Table View -->
                <div id="tableView" class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Judul</th>
                                <th>Tipe</th>
                                <th>Status</th>
                                <th>Penulis</th>
                                <th>Tanggal</th>
                                <th width="120">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($item->featured_image): ?>
                                            <img src="<?php echo e(Storage::url($item->featured_image)); ?>" 
                                                 alt="<?php echo e($item->title); ?>" 
                                                 class="rounded me-3" 
                                                 style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-1"><?php echo e(Str::limit($item->title, 50)); ?></h6>
                                            <?php if($item->is_featured): ?>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-star"></i> Featured
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($item->type === 'news' ? 'primary' : 'info'); ?>">
                                        <?php echo e($item->type === 'news' ? 'Berita' : 'Pengumuman'); ?>

                                    </span>
                                </td>
                                <td>
                                    <?php if($item->status === 'published'): ?>
                                        <span class="badge bg-success">Published</span>
                                    <?php elseif($item->status === 'draft'): ?>
                                        <span class="badge bg-secondary">Draft</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Archived</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($item->author->name ?? 'Unknown'); ?></td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo e($item->created_at->format('d/m/Y H:i')); ?>

                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.content.news.show', $item)); ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Lihat">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.content.news.edit', $item)); ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger"
                                                title="Hapus"
                                                onclick="deleteNews(<?php echo e($item->id); ?>, '<?php echo e(addslashes($item->title)); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Card View -->
                <div id="cardView" class="row" style="display: none;">
                    <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm border-0">
                            <!-- Card Image -->
                            <div class="position-relative">
                                <?php if($item->featured_image): ?>
                                    <img src="<?php echo e(Storage::url($item->featured_image)); ?>"
                                         class="card-img-top"
                                         alt="<?php echo e($item->title); ?>"
                                         style="height: 200px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                         style="height: 200px;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>

                                <!-- Badges -->
                                <div class="position-absolute top-0 start-0 m-2">
                                    <span class="badge bg-<?php echo e($item->type === 'news' ? 'primary' : 'info'); ?> me-1">
                                        <?php echo e($item->type === 'news' ? 'Berita' : 'Pengumuman'); ?>

                                    </span>
                                    <?php if($item->status === 'published'): ?>
                                        <span class="badge bg-success">Published</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Draft</span>
                                    <?php endif; ?>
                                </div>

                                <?php if($item->is_featured): ?>
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-star"></i> Featured
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Card Body -->
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title"><?php echo e(Str::limit($item->title, 60)); ?></h5>

                                <?php if($item->excerpt): ?>
                                    <p class="card-text text-muted small flex-grow-1">
                                        <?php echo e(Str::limit($item->excerpt, 100)); ?>

                                    </p>
                                <?php else: ?>
                                    <p class="card-text text-muted small flex-grow-1">
                                        <?php echo e(Str::limit(strip_tags($item->content), 100)); ?>

                                    </p>
                                <?php endif; ?>

                                <!-- Meta Info -->
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center text-muted small mb-2">
                                        <span>
                                            <i class="fas fa-user me-1"></i><?php echo e($item->author->name ?? 'Unknown'); ?>

                                        </span>
                                        <span>
                                            <i class="fas fa-calendar me-1"></i><?php echo e($item->created_at->format('d/m/Y')); ?>

                                        </span>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="d-flex gap-1">
                                        <a href="<?php echo e(route('admin.content.news.show', $item)); ?>"
                                           class="btn btn-sm btn-outline-info flex-fill"
                                           title="Lihat">
                                            <i class="fas fa-eye me-1"></i>Lihat
                                        </a>
                                        <a href="<?php echo e(route('admin.content.news.edit', $item)); ?>"
                                           class="btn btn-sm btn-outline-primary flex-fill"
                                           title="Edit">
                                            <i class="fas fa-edit me-1"></i>Edit
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger"
                                                title="Hapus"
                                                onclick="deleteNews(<?php echo e($item->id); ?>, '<?php echo e(addslashes($item->title)); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($news->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada berita atau pengumuman</h5>
                    <p class="text-muted mb-4">Mulai dengan membuat berita atau pengumuman pertama Anda.</p>
                    <a href="<?php echo e(route('admin.content.news.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tambah Berita
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Hidden Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <?php echo csrf_field(); ?>
    <?php echo method_field('DELETE'); ?>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Clean Stats Cards */
.stats-card-clean {
    transition: all 0.3s ease;
    cursor: pointer;
    border-radius: 15px !important;
    background: #ffffff;
    border: 1px solid #e9ecef !important;
}

.stats-card-clean:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    border-color: #dee2e6 !important;
}

.stats-card-clean .card-body {
    padding: 2rem 1.5rem;
}

/* Icon Circle */
.stats-icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Stats Number */
.stats-number-clean {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1;
}

/* Stats Label */
.stats-label-clean {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .stats-number-clean {
        font-size: 2rem;
    }

    .stats-icon-circle {
        width: 50px;
        height: 50px;
    }

    .stats-card-clean .card-body {
        padding: 1.5rem 1rem;
    }
}

/* Animation */
.stats-number-clean {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteNews(id, title) {
    Swal.fire({
        title: 'Hapus Berita?',
        html: `Apakah Anda yakin ingin menghapus berita:<br><strong>"${title}"</strong>?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i>Ya, Hapus!',
        cancelButtonText: '<i class="fas fa-times me-2"></i>Batal',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger me-2',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Submit form
            const form = document.getElementById('deleteForm');
            form.action = `/admin/content/news/${id}`;
            form.submit();
        }
    });
}

// Show success/error messages with SweetAlert
<?php if(session('success')): ?>
    Swal.fire({
        title: 'Berhasil!',
        text: '<?php echo e(session('success')); ?>',
        icon: 'success',
        confirmButtonColor: '#198754',
        confirmButtonText: 'OK'
    });
<?php endif; ?>

<?php if(session('error')): ?>
    Swal.fire({
        title: 'Error!',
        text: '<?php echo e(session('error')); ?>',
        icon: 'error',
        confirmButtonColor: '#dc3545',
        confirmButtonText: 'OK'
    });
<?php endif; ?>
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/content/news/index.blade.php ENDPATH**/ ?>