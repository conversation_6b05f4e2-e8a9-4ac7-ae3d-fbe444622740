@extends('layouts.dashboard')

@section('title', 'Detail Log Keamanan')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Detail Log Keamanan</h1>
    <div>
        <a href="{{ route('admin.security.index') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-2"></i>Kembali
        </a>
        @if(!$securityLog->is_resolved)
        <button type="button" class="btn btn-success" onclick="resolveLog({{ $securityLog->id }})">
            <i class="fas fa-check me-2"></i>Tandai Resolved
        </button>
        @endif
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informasi Log
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Event Type:</label>
                        <div>
                            <span class="badge bg-primary">{{ $securityLog->event_type }}</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Risk Level:</label>
                        <div>
                            @php
                                $riskColors = [
                                    'low' => 'success',
                                    'medium' => 'warning',
                                    'high' => 'danger',
                                    'critical' => 'dark'
                                ];
                                $color = $riskColors[$securityLog->risk_level] ?? 'secondary';
                            @endphp
                            <span class="badge bg-{{ $color }}">{{ ucfirst($securityLog->risk_level) }}</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Tanggal & Waktu:</label>
                        <div>{{ $securityLog->created_at->format('d M Y H:i:s') }}</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Status:</label>
                        <div>
                            @if($securityLog->is_resolved)
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>Resolved
                                </span>
                                <small class="text-muted d-block">
                                    {{ $securityLog->resolved_at->format('d M Y H:i:s') }}
                                </small>
                            @else
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>Unresolved
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label fw-bold">Deskripsi:</label>
                        <div class="p-3 bg-light rounded">{{ $securityLog->description }}</div>
                    </div>
                </div>
            </div>
        </div>

        @if($securityLog->metadata && count($securityLog->metadata) > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database me-2"></i>Metadata
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Key</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($securityLog->metadata as $key => $value)
                            <tr>
                                <td><code>{{ $key }}</code></td>
                                <td>
                                    @if(is_array($value) || is_object($value))
                                        <pre class="mb-0"><code>{{ json_encode($value, JSON_PRETTY_PRINT) }}</code></pre>
                                    @else
                                        {{ $value }}
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Informasi User
                </h5>
            </div>
            <div class="card-body">
                @if($securityLog->user)
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">{{ $securityLog->user->name }}</div>
                            <small class="text-muted">{{ $securityLog->user->email }}</small>
                        </div>
                    </div>
                    <div class="mb-2">
                        <strong>User Type:</strong>
                        <span class="badge bg-info">{{ ucfirst($securityLog->user->user_type) }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>Username:</strong> {{ $securityLog->user->username ?? 'N/A' }}
                    </div>
                    <div class="mb-2">
                        <strong>Status:</strong>
                        @if($securityLog->user->email_verified_at)
                            <span class="badge bg-success">Verified</span>
                        @else
                            <span class="badge bg-warning">Unverified</span>
                        @endif
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('admin.security.user-activity.detail', $securityLog->user) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-history me-1"></i>Lihat Aktivitas User
                        </a>
                    </div>
                @else
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-user-slash fa-2x mb-2"></i>
                        <div>User tidak ditemukan</div>
                        <small>Mungkin user telah dihapus</small>
                    </div>
                @endif
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-network-wired me-2"></i>Informasi Teknis
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">IP Address:</label>
                    <div>
                        <code>{{ $securityLog->ip_address }}</code>
                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $securityLog->ip_address }}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">User Agent:</label>
                    <div class="small text-break">{{ $securityLog->user_agent }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Log ID:</label>
                    <div><code>{{ $securityLog->id }}</code></div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

pre code {
    font-size: 0.8rem;
    max-height: 200px;
    overflow-y: auto;
}
</style>
@endpush

@push('scripts')
<script>
function resolveLog(logId) {
    Swal.fire({
        title: 'Tandai sebagai Resolved?',
        text: 'Log ini akan ditandai sebagai resolved dan tidak akan muncul di daftar unresolved.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Resolve!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/security/logs/${logId}/resolve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: data.message,
                        timer: 3000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    throw new Error(data.message || 'Terjadi kesalahan');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal!',
                    text: error.message || 'Terjadi kesalahan saat resolve log.',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        Swal.fire({
            icon: 'success',
            title: 'Copied!',
            text: 'IP address berhasil disalin ke clipboard.',
            timer: 2000,
            showConfirmButton: false
        });
    });
}
</script>
@endpush
