<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SchoolSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'school_name',
        'school_logo',
        'school_address',
        'school_phone',
        'school_email',
        'school_website',
        'school_description',
        'vision',
        'mission',
        'values',
        'history',
        'principal_name',
        'principal_photo',
        'principal_message',
        'accreditation',
        'established_year',
        'total_students',
        'total_teachers',
        'social_media',
        'contact_info',
        'is_active',
        'maintenance_mode',
        'maintenance_message',
    ];

    protected function casts(): array
    {
        return [
            'social_media' => 'array',
            'contact_info' => 'array',
            'is_active' => 'boolean',
            'maintenance_mode' => 'boolean',
        ];
    }
}
