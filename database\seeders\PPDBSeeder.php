<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PPDBSetting;
use App\Models\Program;
use App\Models\News;
use App\Models\User;
use Illuminate\Support\Str;

class PPDBSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create PPDB Setting for current academic year
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $academicYear = $currentYear . '/' . $nextYear;

        // Get program IDs for quota setting
        $programs = Program::all();
        $programQuotas = [];
        foreach ($programs as $program) {
            $programQuotas[$program->id] = 50; // Default quota 50 per program
        }

        PPDBSetting::firstOrCreate(
            ['academic_year' => $academicYear],
            [
                'status' => 'open', // Set to open for testing
                'registration_start' => now()->subDays(7), // Started 7 days ago
                'registration_end' => now()->addDays(30), // Ends in 30 days
                'test_date' => now()->addDays(45), // Test in 45 days
                'announcement_date' => now()->addDays(60), // Announcement in 60 days
                'registration_fee' => 0, // Free registration
                'required_documents' => [
                    'Ijazah/SKHUN',
                    'Kartu Keluarga',
                    'Akta Kelahiran',
                    'Pas Foto 3x4 (2 lembar)',
                    'Surat Keterangan Sehat',
                    'Rapor Semester Terakhir',
                ],
                'program_quotas' => $programQuotas,
                'requirements' => "Persyaratan Umum PPDB:\n\n1. Lulusan SMP/MTs atau sederajat\n2. Usia maksimal 18 tahun pada 1 Juli " . $nextYear . "\n3. Sehat jasmani dan rohani\n4. Tidak buta warna untuk program tertentu\n5. Mengikuti tes masuk yang diadakan sekolah\n\nPersyaratan Khusus:\n- Program IPA: Nilai Matematika dan IPA minimal 75\n- Program IPS: Nilai Bahasa Indonesia dan IPS minimal 75\n- Program Bahasa: Nilai Bahasa Indonesia dan Bahasa Inggris minimal 75",
                'announcement_text' => "PENGUMUMAN PPDB ONLINE TAHUN AJARAN " . $academicYear . "\n\nDengan ini kami informasikan bahwa pendaftaran peserta didik baru untuk tahun ajaran " . $academicYear . " telah dibuka.\n\nJadwal Penting:\n- Pendaftaran: " . now()->subDays(7)->format('d F Y') . " - " . now()->addDays(30)->format('d F Y') . "\n- Tes Masuk: " . now()->addDays(45)->format('d F Y') . "\n- Pengumuman: " . now()->addDays(60)->format('d F Y') . "\n\nSilakan daftar melalui website resmi sekolah atau datang langsung ke sekolah.\n\nTerima kasih.",
                'is_active' => true,
            ]
        );

        // Auto-generate PPDB announcement news
        $admin = User::where('email', '<EMAIL>')->first();
        if ($admin) {
            $newsTitle = "PPDB Online Tahun Ajaran {$academicYear} Telah Dibuka!";
            $newsSlug = Str::slug($newsTitle);

            News::firstOrCreate(
                ['slug' => $newsSlug],
                [
                    'title' => $newsTitle,
                    'slug' => $newsSlug,
                    'excerpt' => "Pendaftaran Peserta Didik Baru (PPDB) Online untuk tahun ajaran {$academicYear} telah resmi dibuka. Daftar sekarang juga!",
                    'content' => "
                        <h3>PENGUMUMAN PPDB ONLINE TAHUN AJARAN {$academicYear}</h3>

                        <p>Dengan ini kami informasikan bahwa <strong>Penerimaan Peserta Didik Baru (PPDB) Online</strong> untuk tahun ajaran {$academicYear} telah resmi dibuka.</p>

                        <h4>📅 Jadwal Penting:</h4>
                        <ul>
                            <li><strong>Pendaftaran Online:</strong> " . now()->subDays(7)->format('d F Y') . " - " . now()->addDays(30)->format('d F Y') . "</li>
                            <li><strong>Tes Masuk:</strong> " . now()->addDays(45)->format('d F Y') . "</li>
                            <li><strong>Pengumuman Hasil:</strong> " . now()->addDays(60)->format('d F Y') . "</li>
                        </ul>

                        <h4>💰 Biaya Pendaftaran:</h4>
                        <p><strong>GRATIS</strong> - Tidak dipungut biaya apapun untuk pendaftaran online.</p>

                        <h4>📋 Persyaratan:</h4>
                        <ul>
                            <li>Lulusan SMP/MTs atau sederajat</li>
                            <li>Usia maksimal 18 tahun pada 1 Juli {$nextYear}</li>
                            <li>Sehat jasmani dan rohani</li>
                            <li>Mengikuti tes masuk yang diadakan sekolah</li>
                        </ul>

                        <h4>📄 Dokumen yang Diperlukan:</h4>
                        <ul>
                            <li>Ijazah/SKHUN</li>
                            <li>Kartu Keluarga</li>
                            <li>Akta Kelahiran</li>
                            <li>Pas Foto 3x4 (2 lembar)</li>
                            <li>Surat Keterangan Sehat</li>
                            <li>Rapor Semester Terakhir</li>
                        </ul>

                        <h4>🎓 Program yang Tersedia:</h4>
                        <ul>
                            <li>Program IPA (Kuota: 50 siswa)</li>
                            <li>Program IPS (Kuota: 50 siswa)</li>
                            <li>Program Bahasa (Kuota: 50 siswa)</li>
                        </ul>

                        <h4>📱 Cara Mendaftar:</h4>
                        <ol>
                            <li>Kunjungi website resmi sekolah</li>
                            <li>Klik menu 'PPDB Online'</li>
                            <li>Isi formulir pendaftaran dengan lengkap</li>
                            <li>Submit dan dapatkan nomor pendaftaran</li>
                            <li>Login ke Portal Calon Siswa untuk upload dokumen</li>
                        </ol>

                        <p><strong>Jangan lewatkan kesempatan emas ini!</strong> Bergabunglah dengan keluarga besar sekolah kami dan raih masa depan yang cerah.</p>

                        <p>Untuk informasi lebih lanjut, hubungi:</p>
                        <ul>
                            <li>📞 Telepon: (*************</li>
                            <li>📧 Email: <EMAIL></li>
                            <li>🕐 Jam Layanan: 08:00 - 16:00 WIB</li>
                        </ul>
                    ",
                    'type' => 'announcement',
                    'status' => 'published',
                    'is_featured' => true,
                    'author_id' => $admin->id,
                    'published_at' => now(),
                    'views' => 0,
                ]
            );
        }
    }
}
