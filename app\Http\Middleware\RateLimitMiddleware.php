<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SecurityLog;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $type = 'general'): Response
    {
        $key = $this->resolveRequestSignature($request, $type);
        $maxAttempts = $this->getMaxAttempts($type);
        $decayMinutes = $this->getDecayMinutes($type);

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $this->logRateLimitExceeded($request, $type, $key);
            
            return $this->buildResponse($key, $maxAttempts, $type);
        }

        RateLimiter::hit($key, $decayMinutes * 60);

        $response = $next($request);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts)
        );
    }

    /**
     * Resolve request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request, string $type): string
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        
        switch ($type) {
            case 'login':
                // More strict for login attempts
                $loginField = $request->input('login', $request->input('email', ''));
                return "login:{$ip}:{$loginField}:" . md5($userAgent);
                
            case 'register':
                return "register:{$ip}:" . md5($userAgent);
                
            case 'password-reset':
                $email = $request->input('email', '');
                return "password-reset:{$ip}:{$email}";
                
            case 'api':
                $userId = auth()->id() ?? 'guest';
                return "api:{$ip}:{$userId}";
                
            case 'upload':
                $userId = auth()->id() ?? 'guest';
                return "upload:{$ip}:{$userId}";
                
            default:
                return "general:{$ip}:" . md5($userAgent);
        }
    }

    /**
     * Get maximum attempts for rate limit type
     */
    protected function getMaxAttempts(string $type): int
    {
        return match ($type) {
            'login' => 5,           // 5 login attempts
            'register' => 3,        // 3 registration attempts
            'password-reset' => 3,  // 3 password reset attempts
            'api' => 60,           // 60 API requests
            'upload' => 10,        // 10 file uploads
            default => 30,         // 30 general requests
        };
    }

    /**
     * Get decay minutes for rate limit type
     */
    protected function getDecayMinutes(string $type): int
    {
        return match ($type) {
            'login' => 15,          // 15 minutes lockout
            'register' => 60,       // 1 hour lockout
            'password-reset' => 60, // 1 hour lockout
            'api' => 1,            // 1 minute window
            'upload' => 10,        // 10 minutes window
            default => 1,          // 1 minute window
        };
    }

    /**
     * Calculate remaining attempts
     */
    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        return RateLimiter::remaining($key, $maxAttempts);
    }

    /**
     * Add rate limit headers to response
     */
    protected function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
        ]);

        return $response;
    }

    /**
     * Build rate limit exceeded response
     */
    protected function buildResponse(string $key, int $maxAttempts, string $type): Response
    {
        $retryAfter = RateLimiter::availableIn($key);
        
        $message = $this->getRateLimitMessage($type, $retryAfter);
        
        if (request()->expectsJson()) {
            return response()->json([
                'message' => $message,
                'retry_after' => $retryAfter,
                'type' => 'rate_limit_exceeded'
            ], 429);
        }

        return response()->view('errors.rate-limit', [
            'message' => $message,
            'retryAfter' => $retryAfter,
            'type' => $type
        ], 429);
    }

    /**
     * Get rate limit message based on type
     */
    protected function getRateLimitMessage(string $type, int $retryAfter): string
    {
        $minutes = ceil($retryAfter / 60);
        
        return match ($type) {
            'login' => "Terlalu banyak percobaan login. Silakan coba lagi dalam {$minutes} menit.",
            'register' => "Terlalu banyak percobaan registrasi. Silakan coba lagi dalam {$minutes} menit.",
            'password-reset' => "Terlalu banyak permintaan reset password. Silakan coba lagi dalam {$minutes} menit.",
            'upload' => "Terlalu banyak upload file. Silakan coba lagi dalam {$minutes} menit.",
            default => "Terlalu banyak permintaan. Silakan coba lagi dalam {$minutes} menit.",
        };
    }

    /**
     * Log rate limit exceeded event
     */
    protected function logRateLimitExceeded(Request $request, string $type, string $key): void
    {
        SecurityLog::logEvent('rate_limit_exceeded', auth()->id(), [
            'type' => $type,
            'key' => $key,
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip(),
        ]);
    }
}
