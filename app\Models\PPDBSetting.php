<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PPDBSetting extends Model
{
    use HasFactory;

    protected $table = 'ppdb_settings';

    protected $fillable = [
        'academic_year',
        'status',
        'registration_start',
        'registration_end',
        'document_start',
        'document_end',
        'test_date',
        'announcement_date',
        'registration_fee',
        'required_documents',
        'program_quotas',
        'requirements',
        'announcement_text',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'registration_start' => 'datetime',
            'registration_end' => 'datetime',
            'document_start' => 'datetime',
            'document_end' => 'datetime',
            'test_date' => 'datetime',
            'announcement_date' => 'datetime',
            'registration_fee' => 'decimal:2',
            'required_documents' => 'array',
            'program_quotas' => 'array',
            'is_active' => 'boolean',
        ];
    }

    public function registrations()
    {
        return $this->hasMany(PPDBRegistration::class);
    }

    public function isOpen(): bool
    {
        return $this->status === 'open' &&
               $this->is_active &&
               $this->registration_start &&
               $this->registration_end &&
               now()->between($this->registration_start, $this->registration_end);
    }

    public function isClosed(): bool
    {
        return $this->status === 'closed' || !$this->is_active;
    }

    public function isRegistrationPeriod(): bool
    {
        return $this->registration_start &&
               $this->registration_end &&
               now()->between($this->registration_start, $this->registration_end);
    }

    public function canShowRegistration(): bool
    {
        return $this->status === 'open' && $this->is_active;
    }

    public function getRegistrationStatus(): string
    {
        if ($this->status === 'maintenance') {
            return 'maintenance';
        }

        if ($this->status === 'closed' || !$this->is_active) {
            return 'closed';
        }

        if (!$this->registration_start || !$this->registration_end) {
            return 'not_configured';
        }

        if (now()->lt($this->registration_start)) {
            return 'not_started';
        }

        if (now()->gt($this->registration_end)) {
            return 'ended';
        }

        return 'open';
    }

    public static function getActive()
    {
        return self::where('is_active', true)->first();
    }
}
