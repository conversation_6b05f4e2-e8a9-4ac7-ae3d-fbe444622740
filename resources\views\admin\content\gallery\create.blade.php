@extends('layouts.dashboard')

@section('title', 'Tambah Galeri')

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Tambah Galeri</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="#">Manajemen Konten</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.content.gallery.index') }}"><PERSON>ri</a></li>
                    <li class="breadcrumb-item active">Tambah Galeri</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.content.gallery.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>Form Tambah Galeri
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.content.gallery.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <div class="mb-3">
                            <label for="title" class="form-label">Judul <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="file_type" class="form-label">Tipe File <span class="text-danger">*</span></label>
                                    <select class="form-select @error('file_type') is-invalid @enderror" id="file_type" name="file_type" required>
                                        <option value="">Pilih Tipe File</option>
                                        <option value="image" {{ old('file_type') == 'image' ? 'selected' : '' }}>Gambar</option>
                                        <option value="video" {{ old('file_type') == 'video' ? 'selected' : '' }}>Video</option>
                                    </select>
                                    @error('file_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Kategori</label>
                                    <select class="form-select @error('category') is-invalid @enderror" id="category" name="category">
                                        <option value="">Pilih Kategori</option>
                                        <option value="kegiatan" {{ old('category') == 'kegiatan' ? 'selected' : '' }}>Kegiatan</option>
                                        <option value="fasilitas" {{ old('category') == 'fasilitas' ? 'selected' : '' }}>Fasilitas</option>
                                        <option value="prestasi" {{ old('category') == 'prestasi' ? 'selected' : '' }}>Prestasi</option>
                                        <option value="acara" {{ old('category') == 'acara' ? 'selected' : '' }}>Acara</option>
                                        <option value="pembelajaran" {{ old('category') == 'pembelajaran' ? 'selected' : '' }}>Pembelajaran</option>
                                        <option value="ekstrakurikuler" {{ old('category') == 'ekstrakurikuler' ? 'selected' : '' }}>Ekstrakurikuler</option>
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="album" class="form-label">Album</label>
                            <div class="input-group">
                                <select class="form-select @error('album') is-invalid @enderror" id="album" name="album">
                                    <option value="">Pilih Album atau Buat Baru</option>
                                    @foreach($albums as $album)
                                        <option value="{{ $album }}" {{ old('album') == $album ? 'selected' : '' }}>
                                            {{ $album }}
                                        </option>
                                    @endforeach
                                </select>
                                <button class="btn btn-outline-secondary" type="button" id="toggleAlbumInput" title="Buat Album Baru">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <input type="text" class="form-control mt-2 @error('album') is-invalid @enderror"
                                   id="albumInput" name="album_new" value="{{ old('album_new') }}"
                                   placeholder="Nama album baru..." style="display: none;">
                            @error('album')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Pilih album yang sudah ada atau buat album baru</small>
                        </div>

                        <div class="mb-3" id="file-upload-section">
                            <label for="file" class="form-label">File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control @error('file') is-invalid @enderror"
                                   id="file" name="file" required>
                            @error('file')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted" id="file-help">
                                Format yang didukung: JPG, PNG, GIF, MP4, AVI, MOV. Maksimal 10MB.
                            </small>
                        </div>

                        <div class="mb-3">
                            <label for="alt_text" class="form-label">Alt Text</label>
                            <input type="text" class="form-control @error('alt_text') is-invalid @enderror" 
                                   id="alt_text" name="alt_text" value="{{ old('alt_text') }}" 
                                   placeholder="Deskripsi singkat untuk aksesibilitas">
                            @error('alt_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" 
                                               {{ old('is_featured') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            Tampilkan di Beranda
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="order" class="form-label">Urutan</label>
                                    <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                           id="order" name="order" value="{{ old('order', 0) }}" min="0">
                                    @error('order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('admin.content.gallery.index') }}" class="btn btn-secondary me-2">Batal</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Simpan Galeri
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview & Help -->
        <div class="col-lg-4">
            <!-- Preview Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Preview
                    </h6>
                </div>
                <div class="card-body">
                    <div id="file-preview" style="display: none;">
                        <img id="image-preview" src="" alt="Preview" class="img-fluid rounded shadow-sm" style="max-height: 200px; display: none;">
                        <video id="video-preview" controls class="w-100 rounded shadow-sm" style="max-height: 200px; display: none;">
                            <source src="" type="video/mp4">
                            Browser Anda tidak mendukung video.
                        </video>
                    </div>
                    <div id="no-preview" class="text-center text-muted">
                        <i class="fas fa-image fa-3x mb-2"></i>
                        <p>Pilih file untuk melihat preview</p>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Bantuan
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="mb-0 ps-3">
                            <li>Judul harus deskriptif dan menarik</li>
                            <li>Pilih tipe file sesuai dengan konten yang akan diupload</li>
                            <li>Kategori membantu pengorganisasian galeri</li>
                            <li>Album untuk mengelompokkan file terkait</li>
                            <li>Alt text penting untuk aksesibilitas</li>
                            <li>Centang "Tampilkan di Beranda" untuk konten unggulan</li>
                            <li>Urutan menentukan posisi tampil (0 = paling atas)</li>
                        </ul>
                    </small>
                </div>
            </div>

            <!-- File Info -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-file me-2"></i>Info File
                    </h6>
                </div>
                <div class="card-body">
                    <div id="file-info" style="display: none;">
                        <div class="mb-2">
                            <strong>Nama:</strong> <span id="file-name">-</span>
                        </div>
                        <div class="mb-2">
                            <strong>Ukuran:</strong> <span id="file-size">-</span>
                        </div>
                        <div class="mb-2">
                            <strong>Tipe:</strong> <span id="file-type-info">-</span>
                        </div>
                    </div>
                    <div id="no-file-info" class="text-center text-muted">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <p>Pilih file untuk melihat informasi</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Album input toggle
    $('#toggleAlbumInput').click(function() {
        const albumSelect = $('#album');
        const albumInput = $('#albumInput');
        const button = $(this);

        if (albumInput.is(':visible')) {
            // Switch back to select
            albumInput.hide().attr('name', 'album_new').val('');
            albumSelect.show().attr('name', 'album');
            button.html('<i class="fas fa-plus"></i>').attr('title', 'Buat Album Baru');
        } else {
            // Switch to input
            albumSelect.hide().attr('name', 'album_old');
            albumInput.show().attr('name', 'album').focus();
            button.html('<i class="fas fa-list"></i>').attr('title', 'Pilih Album yang Ada');
        }
    });

    // File type change handler
    $('#file_type').change(function() {
        const fileType = $(this).val();
        const fileInput = $('#file');
        const helpText = $('#file-help');

        if (fileType === 'image') {
            fileInput.attr('accept', 'image/*');
            helpText.text('Format gambar: JPG, PNG, GIF. Maksimal 10MB.');
        } else if (fileType === 'video') {
            fileInput.attr('accept', 'video/*');
            helpText.text('Format video: MP4, AVI, MOV. Maksimal 50MB.');
        } else {
            fileInput.removeAttr('accept');
            helpText.text('Format yang didukung: JPG, PNG, GIF, MP4, AVI, MOV. Maksimal 10MB.');
        }

        // Clear file input and preview
        fileInput.val('');
        resetPreview();
    });

    // File input change handler
    $('#file').change(function() {
        const file = this.files[0];
        if (file) {
            showFileInfo(file);
            showFilePreview(file);
        } else {
            resetPreview();
        }
    });

    function showFileInfo(file) {
        $('#file-name').text(file.name);
        $('#file-size').text(formatFileSize(file.size));
        $('#file-type-info').text(file.type);
        $('#file-info').show();
        $('#no-file-info').hide();
    }

    function showFilePreview(file) {
        const reader = new FileReader();
        
        if (file.type.startsWith('image/')) {
            reader.onload = function(e) {
                $('#image-preview').attr('src', e.target.result).show();
                $('#video-preview').hide();
                $('#file-preview').show();
                $('#no-preview').hide();
            };
            reader.readAsDataURL(file);
        } else if (file.type.startsWith('video/')) {
            reader.onload = function(e) {
                $('#video-preview').find('source').attr('src', e.target.result);
                $('#video-preview')[0].load();
                $('#video-preview').show();
                $('#image-preview').hide();
                $('#file-preview').show();
                $('#no-preview').hide();
            };
            reader.readAsDataURL(file);
        } else {
            resetPreview();
        }
    }

    function resetPreview() {
        $('#file-preview').hide();
        $('#no-preview').show();
        $('#file-info').hide();
        $('#no-file-info').show();
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>
@endpush
