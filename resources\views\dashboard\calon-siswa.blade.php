@extends('layouts.dashboard')

@section('title', 'Portal Calon Siswa')
@section('page-title', 'Portal Calon Siswa - Selamat Datang di Portal Pendaftaran')

@section('sidebar-menu')
<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link active" href="{{ route('dashboard') }}">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-user"></i>
            Profil Saya
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-file-alt"></i>
            Form Pendaftaran
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-upload"></i>
            Upload Dokumen
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-clipboard-check"></i>
            Status Pendaftaran
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-calendar-alt"></i>
            Jadwal Tes
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-bullhorn"></i>
            Pengumuman
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-graduation-cap"></i>
            Program Tersedia
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-question-circle"></i>
            Bantuan
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="{{ route('landing') }}" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning border-0" style="background: linear-gradient(135deg, #ffc107, #fd7e14); color: white;">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-user-plus fa-2x"></i>
                </div>
                <div>
                    <h5 class="mb-1">📝 Selamat Datang di Portal Pendaftaran!</h5>
                    <p class="mb-0">
                        Halo, <strong>{{ Auth::user()->name }}</strong>! 
                        Status pendaftaran Anda: <span class="badge bg-light text-dark">{{ ucfirst($stats['application_status']) }}</span>
                        <br><small>Lengkapi semua tahapan pendaftaran untuk menjadi siswa baru di sekolah kami.</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admission Progress -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list-ol me-2"></i>Progress Pendaftaran
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($admissionSteps as $step)
                    <div class="col-md-2 text-center mb-3">
                        <div class="position-relative">
                            <div class="rounded-circle d-inline-flex align-items-center justify-content-center mb-2
                                @if($step['status'] == 'completed') bg-success
                                @elseif($step['status'] == 'pending') bg-warning
                                @else bg-secondary
                                @endif" 
                                style="width: 60px; height: 60px;">
                                @if($step['status'] == 'completed')
                                    <i class="fas fa-check text-white fa-lg"></i>
                                @else
                                    <span class="text-white fw-bold">{{ $step['step'] }}</span>
                                @endif
                            </div>
                            <h6 class="small">{{ $step['title'] }}</h6>
                            <span class="badge bg-{{ $step['status'] == 'completed' ? 'success' : ($step['status'] == 'pending' ? 'warning' : 'secondary') }}">
                                {{ ucfirst($step['status']) }}
                            </span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stats-number">{{ $stats['uploaded_documents'] }}/{{ $stats['required_documents'] }}</div>
            <div class="stats-label">Dokumen Terupload</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stats-number">{{ $stats['total_programs'] }}</div>
            <div class="stats-label">Program Tersedia</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ ucfirst($stats['admission_phase']) }}</div>
            <div class="stats-label">Fase Pendaftaran</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-clipboard-check"></i>
            </div>
            <div class="stats-number">{{ ucfirst($stats['application_status']) }}</div>
            <div class="stats-label">Status Aplikasi</div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bullhorn me-2"></i>Pengumuman Pendaftaran
            </div>
            <div class="card-body">
                @forelse($admissionNews as $news)
                    <div class="d-flex align-items-start mb-3">
                        @if($news->featured_image)
                            <img src="{{ asset('storage/' . $news->featured_image) }}" alt="News" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                        @else
                            <div class="rounded bg-gradient-warning d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-bullhorn text-white"></i>
                            </div>
                        @endif
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ Str::limit($news->title, 60) }}</h6>
                            <p class="text-muted small mb-2">{{ Str::limit($news->excerpt, 100) }}</p>
                            <small class="text-muted">
                                {{ $news->created_at->diffForHumans() }}
                            </small>
                            <span class="badge bg-warning ms-2">Pengumuman</span>
                        </div>
                    </div>
                    @if(!$loop->last)<hr>@endif
                @empty
                    <p class="text-muted text-center">Belum ada pengumuman terbaru</p>
                @endforelse
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-upload me-2"></i>Dokumen yang Diperlukan
            </div>
            <div class="card-body">
                @foreach($requiredDocuments as $doc)
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-alt me-2 text-{{ $doc['uploaded'] ? 'success' : 'muted' }}"></i>
                            <span class="small">{{ $doc['name'] }}</span>
                        </div>
                        @if($doc['uploaded'])
                            <span class="badge bg-success">
                                <i class="fas fa-check"></i>
                            </span>
                        @else
                            <span class="badge bg-danger">
                                <i class="fas fa-times"></i>
                            </span>
                        @endif
                    </div>
                @endforeach
                
                <div class="mt-3">
                    <a href="#" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-upload me-2"></i>Upload Dokumen
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Available Programs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-graduation-cap me-2"></i>Program Pendidikan Tersedia
            </div>
            <div class="card-body">
                <div class="row">
                    @forelse($availablePrograms as $program)
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">{{ $program->name }}</h6>
                                <p class="card-text small text-muted">{{ Str::limit($program->description, 80) }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-primary">{{ $program->level }}</span>
                                    <small class="text-muted">{{ $program->duration_years }} tahun</small>
                                </div>
                                <div class="mt-2">
                                    <small class="text-success">
                                        <i class="fas fa-users me-1"></i>
                                        {{ $program->current_students }}/{{ $program->capacity }} siswa
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="col-12 text-center">
                        <p class="text-muted">Belum ada program tersedia</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>Aksi Cepat
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-file-alt me-2"></i>Lengkapi Form
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-upload me-2"></i>Upload Dokumen
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-calendar-alt me-2"></i>Cek Jadwal Tes
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-danger w-100">
                            <i class="fas fa-question-circle me-2"></i>Bantuan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
