<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Models\SecurityLog;

class CheckAccountLock
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();

            if ($user->isLocked()) {
                SecurityLog::logEvent('suspicious_activity', $user->id, [
                    'reason' => 'Attempted access while account locked',
                    'url' => $request->fullUrl(),
                ]);

                Auth::logout();

                return redirect()->route('login')->with('error',
                    'Akun Anda dikunci karena terlalu banyak percobaan login yang gagal. Silakan coba lagi nanti atau hubungi administrator.'
                );
            }
        }

        return $next($request);
    }
}
