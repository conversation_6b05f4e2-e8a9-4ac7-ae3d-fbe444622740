<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class NewsManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of news.
     */
    public function index()
    {
        $news = News::with('author')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Statistics
        $stats = [
            'total' => News::count(),
            'published' => News::where('status', 'published')->count(),
            'draft' => News::where('status', 'draft')->count(),
            'archived' => News::where('status', 'archived')->count(),
            'news' => News::where('type', 'news')->count(),
            'announcements' => News::where('type', 'announcement')->count(),
            'featured' => News::where('is_featured', true)->count(),
            'this_month' => News::whereMonth('created_at', now()->month)
                               ->whereYear('created_at', now()->year)
                               ->count(),
        ];

        return view('admin.content.news.index', compact('news', 'stats'));
    }

    /**
     * Show the form for creating a new news.
     */
    public function create()
    {
        return view('admin.content.news.create');
    }

    /**
     * Store a newly created news in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:news,slug',
            'content' => 'required|string',
            'type' => 'required|in:news,announcement',
            'status' => 'required|in:draft,published,archived',
        ]);

        $data = [
            'title' => $request->title,
            'slug' => $request->slug,
            'excerpt' => $request->excerpt,
            'content' => $request->content,
            'type' => $request->type,
            'status' => $request->status,
            'category' => $request->category,
            'tags' => $request->tags,
            'author_id' => Auth::id(),
            'is_featured' => $request->has('is_featured') ? 1 : 0,
            'published_at' => $request->status === 'published' ? now() : null,
        ];

        // Handle image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('news', 'public');
        }

        News::create($data);

        return redirect()->route('admin.content.news.index')
            ->with('success', 'Berita berhasil dibuat!');
    }

    /**
     * Display the specified news.
     */
    public function show(News $news)
    {
        return view('admin.content.news.show', compact('news'));
    }

    /**
     * Show the form for editing the specified news.
     */
    public function edit(News $news)
    {
        return view('admin.content.news.edit', compact('news'));
    }

    /**
     * Update the specified news in storage.
     */
    public function update(Request $request, News $news)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:news,slug,' . $news->id,
            'content' => 'required|string',
            'type' => 'required|in:news,announcement',
            'status' => 'required|in:draft,published,archived',
        ]);

        $data = [
            'title' => $request->title,
            'slug' => $request->slug,
            'excerpt' => $request->excerpt,
            'content' => $request->content,
            'type' => $request->type,
            'status' => $request->status,
            'category' => $request->category,
            'tags' => $request->tags,
            'is_featured' => $request->has('is_featured') ? 1 : 0,
            'published_at' => $request->status === 'published' ? ($news->published_at ?? now()) : null,
        ];

        // Handle image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($news->featured_image) {
                Storage::disk('public')->delete($news->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('news', 'public');
        }

        $news->update($data);

        return redirect()->route('admin.content.news.index')
            ->with('success', 'Berita berhasil diperbarui!');
    }

    /**
     * Remove the specified news from storage.
     */
    public function destroy(News $news)
    {
        // Delete image
        if ($news->featured_image) {
            Storage::disk('public')->delete($news->featured_image);
        }

        $news->delete();

        return redirect()->route('admin.content.news.index')
            ->with('success', 'Berita berhasil dihapus!');
    }

    /**
     * Remove featured image from news
     */
    public function removeImage(News $news)
    {
        try {
            if ($news->featured_image) {
                Storage::disk('public')->delete($news->featured_image);
                $news->update(['featured_image' => null]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil dihapus!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus gambar: ' . $e->getMessage()
            ], 500);
        }
    }
}
