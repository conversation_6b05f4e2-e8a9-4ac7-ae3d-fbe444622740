<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StaffProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'employee_id',
        'position',
        'subject',
        'qualifications',
        'degree',
        'experience',
        'certifications',
        'join_date',
        'employment_status',
        'salary',
        'bio',
        'achievements',
        'is_featured',
        'order',
    ];

    protected function casts(): array
    {
        return [
            'certifications' => 'array',
            'achievements' => 'array',
            'join_date' => 'date',
            'salary' => 'decimal:2',
            'is_featured' => 'boolean',
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
