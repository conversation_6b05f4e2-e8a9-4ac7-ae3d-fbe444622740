<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PPDBSetting;
use App\Models\PPDBRegistration;
use App\Models\PPDBDocument;
use App\Models\Program;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class PPDBManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Add role middleware here if needed
    }

    public function dashboard()
    {
        // Get real PPDB setting data
        $ppdbSetting = PPDBSetting::getActive();

        // Get real statistics
        $stats = [
            'total_registrations' => PPDBRegistration::count(),
            'pending_registrations' => PPDBRegistration::where('status', 'pending')->count(),
            'verified_registrations' => PPDBRegistration::where('status', 'verified')->count(),
            'approved_registrations' => PPDBRegistration::where('status', 'approved')->count(),
            'rejected_registrations' => PPDBRegistration::where('status', 'rejected')->count(),
            'pending_documents' => PPDBDocument::where('status', 'pending')->count(),
        ];

        // Get recent registrations
        $recentRegistrations = PPDBRegistration::with(['user', 'program'])
            ->latest()
            ->limit(10)
            ->get();

        // Get registrations by program
        $registrationsByProgram = PPDBRegistration::with('program')
            ->selectRaw('program_id, COUNT(*) as count')
            ->groupBy('program_id')
            ->get();

        return view('admin.ppdb.dashboard', compact(
            'ppdbSetting',
            'stats',
            'recentRegistrations',
            'registrationsByProgram'
        ));
    }

    public function settings()
    {
        $ppdbSetting = PPDBSetting::getActive();
        $programs = Program::where('is_active', true)->get();

        return view('admin.ppdb.settings', compact('ppdbSetting', 'programs'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'academic_year' => 'required|string|max:20',
            'status' => 'required|in:closed,open,maintenance',
            'registration_start' => 'nullable|date',
            'registration_end' => 'nullable|date|after:registration_start',
            'document_start' => 'nullable|date',
            'document_end' => 'nullable|date|after:document_start',
            'test_date' => 'nullable|date',
            'announcement_date' => 'nullable|date',
            'registration_fee' => 'nullable|numeric|min:0',
            'requirements' => 'nullable|string',
            'announcement_text' => 'nullable|string',
            'required_documents' => 'nullable|array',
            'required_documents.*' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Process required_documents - filter out empty values
        $requiredDocuments = [];
        if ($request->has('required_documents')) {
            $requiredDocuments = array_filter($request->required_documents, function($doc) {
                return !empty(trim($doc));
            });
            $requiredDocuments = array_values($requiredDocuments); // Re-index array
        }

        $data = [
            'academic_year' => $request->academic_year,
            'status' => $request->status,
            'registration_start' => $request->registration_start,
            'registration_end' => $request->registration_end,
            'document_start' => $request->document_start,
            'document_end' => $request->document_end,
            'test_date' => $request->test_date,
            'announcement_date' => $request->announcement_date,
            'registration_fee' => $request->registration_fee ?? 0,
            'requirements' => $request->requirements,
            'announcement_text' => $request->announcement_text,
            'required_documents' => $requiredDocuments,
            'is_active' => $request->boolean('is_active'),
        ];

        $ppdbSetting = PPDBSetting::getActive();

        if ($ppdbSetting) {
            $ppdbSetting->update($data);
        } else {
            PPDBSetting::create($data);
        }

        return redirect()->route('admin.ppdb.settings')
            ->with('success', 'Pengaturan PPDB berhasil diperbarui.');
    }

    public function registrations(Request $request)
    {
        $query = PPDBRegistration::with(['user', 'program']);

        // Filter by status
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        // Filter by program
        if ($request->has('program_id') && $request->program_id != '') {
            $query->where('program_id', $request->program_id);
        }

        // Search by name or registration number
        if ($request->has('search') && $request->search != '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('registration_number', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $registrations = $query->orderBy('created_at', 'desc')->paginate(20);
        $programs = Program::where('is_active', true)->get();

        return view('admin.ppdb.registrations', compact('registrations', 'programs'));
    }

    public function showRegistration($id)
    {
        $registration = PPDBRegistration::with(['user', 'program', 'documents.verifiedBy'])
            ->findOrFail($id);

        return view('admin.ppdb.registration-detail', compact('registration'));
    }

    public function updateRegistrationStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,verified,approved,rejected,enrolled',
            'notes' => 'nullable|string',
            'test_score' => 'nullable|numeric|min:0|max:100',
        ]);

        $registration = PPDBRegistration::findOrFail($id);
        $registration->status = $request->status;
        $registration->notes = $request->notes;
        $registration->test_score = $request->test_score;

        if ($request->status == 'verified') {
            $registration->verified_at = now();
        } elseif ($request->status == 'approved') {
            $registration->approved_at = now();
        }

        $registration->save();

        return redirect()->back()
            ->with('success', 'Status pendaftaran berhasil diperbarui.');
    }
}
