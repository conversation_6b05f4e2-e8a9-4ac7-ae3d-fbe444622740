@extends('layouts.landing')

@section('title', 'Fasilitas Sekolah')

@section('content')
<!-- Hero Section -->
<section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 120px 0 80px;">
    <div class="container">
        <div class="row justify-content-center text-center text-white">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4" data-aos="fade-up">Fasilitas Sekolah</h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Fasilitas modern dan lengkap untuk mendukung proses pembelajaran yang optimal dan pengembangan potensi siswa
                </p>
                <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="200">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}" class="text-white-50">Beranda</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Fasilitas</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Facilities Grid Section -->
<section class="section">
    <div class="container">
        <div class="row g-4">
            @forelse($facilities as $facility)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="facility-card h-100">
                        @if($facility->image)
                            <div class="facility-image">
                                <img src="{{ asset('storage/' . $facility->image) }}" alt="{{ $facility->name }}" class="w-100">
                                <div class="facility-overlay">
                                    <div class="facility-overlay-content">
                                        <h4 class="text-white mb-2">{{ $facility->name }}</h4>
                                        <p class="text-white-50 mb-3">{{ Str::limit($facility->description, 100) }}</p>
                                        <a href="{{ route('landing.facility', $facility->slug) }}" class="btn btn-light btn-sm">
                                            <i class="fas fa-eye me-1"></i>Lihat Detail
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="facility-image placeholder-container placeholder-facility">
                                <div class="text-center">
                                    <i class="fas fa-building fa-3x placeholder-icon"></i>
                                    <div class="mt-2">{{ $facility->name }}</div>
                                </div>
                                <div class="facility-overlay">
                                    <div class="facility-overlay-content">
                                        <h4 class="text-white mb-2">{{ $facility->name }}</h4>
                                        <p class="text-white-50 mb-3">{{ Str::limit($facility->description, 100) }}</p>
                                        <a href="{{ route('landing.facility', $facility->slug) }}" class="btn btn-light btn-sm">
                                            <i class="fas fa-eye me-1"></i>Lihat Detail
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        <div class="facility-content">
                            <h5 class="facility-title">{{ $facility->name }}</h5>
                            <p class="facility-description">{{ Str::limit($facility->description, 120) }}</p>
                            
                            @if($facility->features && is_array($facility->features) && count($facility->features) > 0)
                                <div class="facility-features mb-3">
                                    <h6 class="text-primary mb-2"><i class="fas fa-check-circle me-1"></i>Fitur Utama:</h6>
                                    <ul class="list-unstyled">
                                        @foreach(array_slice($facility->features, 0, 3) as $feature)
                                            <li><i class="fas fa-chevron-right text-primary me-2"></i>{{ $feature }}</li>
                                        @endforeach
                                        @if(count($facility->features) > 3)
                                            <li class="text-muted"><i class="fas fa-plus me-2"></i>{{ count($facility->features) - 3 }} fitur lainnya</li>
                                        @endif
                                    </ul>
                                </div>
                            @endif
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="facility-status">
                                    @if($facility->is_available)
                                        <span class="badge bg-success"><i class="fas fa-check me-1"></i>Tersedia</span>
                                    @else
                                        <span class="badge bg-warning"><i class="fas fa-clock me-1"></i>Dalam Perbaikan</span>
                                    @endif
                                </div>
                                <a href="{{ route('landing.facility', $facility->slug) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>Detail
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Belum Ada Fasilitas</h4>
                        <p class="text-muted">Informasi fasilitas akan segera diperbarui.</p>
                        <a href="{{ route('landing') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Kembali ke Beranda
                        </a>
                    </div>
                </div>
            @endforelse
        </div>
        
        <!-- Pagination -->
        @if($facilities->hasPages())
            <div class="d-flex justify-content-center mt-5">
                {{ $facilities->links() }}
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="section bg-light">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h3 class="mb-4">Tertarik dengan Fasilitas Kami?</h3>
                <p class="text-muted mb-4">Kunjungi sekolah kami untuk melihat langsung fasilitas yang tersedia dan rasakan pengalaman belajar yang nyaman.</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="#contact" class="btn btn-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>Hubungi Kami
                    </a>
                    <a href="{{ route('landing') }}#ppdb" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Daftar PPDB
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.facility-card {
    background: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.facility-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.facility-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.facility-image img {
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.facility-card:hover .facility-image img {
    transform: scale(1.05);
}

.facility-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.facility-card:hover .facility-overlay {
    opacity: 1;
}

.facility-overlay-content {
    text-align: center;
    padding: 2rem;
}

.facility-content {
    padding: 1.5rem;
}

.facility-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.facility-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.facility-features ul li {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 0.25rem;
}

.placeholder-container.placeholder-facility {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 250px;
}

.placeholder-facility .placeholder-icon {
    opacity: 0.7;
}

/* Responsive */
@media (max-width: 768px) {
    .facility-image {
        height: 200px;
    }
    
    .facility-overlay-content {
        padding: 1rem;
    }
    
    .facility-content {
        padding: 1rem;
    }
}
</style>
@endpush
