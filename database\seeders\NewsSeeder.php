<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\News;
use App\Models\User;
use Illuminate\Support\Str;

class NewsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            return;
        }

        $newsData = [
            [
                'title' => 'Penerimaan Siswa Baru Tahun Ajaran 2024/2025',
                'excerpt' => 'Pendaftaran siswa baru untuk tahun ajaran 2024/2025 telah dibuka. Daftar sekarang juga!',
                'content' => 'Sekolah kami dengan bangga mengumumkan pembukaan pendaftaran siswa baru untuk tahun ajaran 2024/2025. Pendaftaran dapat dilakukan secara online melalui portal pendaftaran kami.',
                'type' => 'announcement',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
            ],
            [
                'title' => 'Prestasi Siswa dalam Olimpiade Matematika',
                'excerpt' => 'Siswa-siswi kami meraih juara dalam kompetisi olimpiade matematika tingkat nasional.',
                'content' => 'Kami bangga mengumumkan bahwa siswa-siswi kami berhasil meraih prestasi gemilang dalam olimpiade matematika tingkat nasional yang diselenggarakan bulan lalu.',
                'type' => 'news',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now()->subDays(2),
            ],
            [
                'title' => 'Kegiatan Ekstrakurikuler Semester Baru',
                'excerpt' => 'Berbagai kegiatan ekstrakurikuler menarik tersedia untuk semester baru ini.',
                'content' => 'Semester baru telah dimulai dan kami menawarkan berbagai kegiatan ekstrakurikuler yang menarik untuk mengembangkan bakat dan minat siswa.',
                'type' => 'news',
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now()->subDays(5),
            ],
            [
                'title' => 'Pengumuman Libur Semester',
                'excerpt' => 'Informasi jadwal libur semester dan kegiatan selama masa libur.',
                'content' => 'Dengan ini kami informasikan jadwal libur semester yang akan dimulai pada tanggal 15 Desember 2024 hingga 2 Januari 2025.',
                'type' => 'announcement',
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now()->subDays(7),
            ],
            [
                'title' => 'Workshop Teknologi untuk Guru',
                'excerpt' => 'Pelatihan teknologi terbaru untuk meningkatkan kualitas pembelajaran.',
                'content' => 'Sekolah mengadakan workshop teknologi untuk para guru guna meningkatkan kualitas pembelajaran dengan memanfaatkan teknologi terkini.',
                'type' => 'event',
                'status' => 'published',
                'is_featured' => false,
                'event_date' => now()->addDays(10),
                'event_location' => 'Aula Sekolah',
                'published_at' => now()->subDays(3),
            ],
        ];

        foreach ($newsData as $data) {
            $data['slug'] = Str::slug($data['title']);
            $data['author_id'] = $admin->id;
            $data['views'] = rand(50, 500);

            News::firstOrCreate(
                ['slug' => $data['slug']],
                $data
            );
        }
    }
}
