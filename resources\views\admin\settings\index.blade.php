@extends('layouts.dashboard')

@section('title', 'Pengaturan Sekolah')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Pengaturan Sekolah</h1>
    <div>
        <a href="{{ route('admin.settings.hero-sections') }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-image me-2"></i>Kelola Hero Sections
        </a>
        <a href="{{ route('landing') }}" class="btn btn-outline-success" target="_blank">
            <i class="fas fa-external-link-alt me-2"></i>Lihat Website
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Sekolah</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.settings.school-info.update') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="school_name" class="form-label">Nama Sekolah <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('school_name') is-invalid @enderror" 
                                   id="school_name" name="school_name" 
                                   value="{{ old('school_name', $schoolSetting->school_name ?? '') }}" required>
                            @error('school_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="established_year" class="form-label">Tahun Berdiri</label>
                            <input type="number" class="form-control @error('established_year') is-invalid @enderror" 
                                   id="established_year" name="established_year" 
                                   value="{{ old('established_year', $schoolSetting->established_year ?? '') }}" 
                                   min="1900" max="{{ date('Y') }}">
                            @error('established_year')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="school_email" class="form-label">Email Sekolah <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('school_email') is-invalid @enderror" 
                                   id="school_email" name="school_email" 
                                   value="{{ old('school_email', $schoolSetting->school_email ?? '') }}" required>
                            @error('school_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="school_phone" class="form-label">Telepon Sekolah <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('school_phone') is-invalid @enderror" 
                                   id="school_phone" name="school_phone" 
                                   value="{{ old('school_phone', $schoolSetting->school_phone ?? '') }}" required>
                            @error('school_phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="school_address" class="form-label">Alamat Sekolah <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('school_address') is-invalid @enderror" 
                                  id="school_address" name="school_address" rows="3" required>{{ old('school_address', $schoolSetting->school_address ?? '') }}</textarea>
                        @error('school_address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="school_description" class="form-label">Deskripsi Sekolah <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('school_description') is-invalid @enderror" 
                                  id="school_description" name="school_description" rows="4" required>{{ old('school_description', $schoolSetting->school_description ?? '') }}</textarea>
                        @error('school_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="vision" class="form-label">Visi <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('vision') is-invalid @enderror" 
                                  id="vision" name="vision" rows="3" required>{{ old('vision', $schoolSetting->vision ?? '') }}</textarea>
                        @error('vision')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="mission" class="form-label">Misi <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('mission') is-invalid @enderror" 
                                  id="mission" name="mission" rows="4" required>{{ old('mission', $schoolSetting->mission ?? '') }}</textarea>
                        @error('mission')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="values" class="form-label">Nilai-nilai</label>
                        <textarea class="form-control @error('values') is-invalid @enderror" 
                                  id="values" name="values" rows="3">{{ old('values', $schoolSetting->values ?? '') }}</textarea>
                        @error('values')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">Informasi Kepala Sekolah</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="principal_name" class="form-label">Nama Kepala Sekolah <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('principal_name') is-invalid @enderror" 
                                   id="principal_name" name="principal_name" 
                                   value="{{ old('principal_name', $schoolSetting->principal_name ?? '') }}" required>
                            @error('principal_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="accreditation" class="form-label">Akreditasi</label>
                            <select class="form-select @error('accreditation') is-invalid @enderror" 
                                    id="accreditation" name="accreditation">
                                <option value="">Pilih Akreditasi</option>
                                <option value="A" {{ old('accreditation', $schoolSetting->accreditation ?? '') == 'A' ? 'selected' : '' }}>A</option>
                                <option value="B" {{ old('accreditation', $schoolSetting->accreditation ?? '') == 'B' ? 'selected' : '' }}>B</option>
                                <option value="C" {{ old('accreditation', $schoolSetting->accreditation ?? '') == 'C' ? 'selected' : '' }}>C</option>
                            </select>
                            @error('accreditation')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="principal_message" class="form-label">Pesan Kepala Sekolah</label>
                        <textarea class="form-control @error('principal_message') is-invalid @enderror" 
                                  id="principal_message" name="principal_message" rows="4">{{ old('principal_message', $schoolSetting->principal_message ?? '') }}</textarea>
                        @error('principal_message')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">Statistik</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="total_students" class="form-label">Total Siswa</label>
                            <input type="number" class="form-control @error('total_students') is-invalid @enderror" 
                                   id="total_students" name="total_students" 
                                   value="{{ old('total_students', $schoolSetting->total_students ?? '') }}" min="0">
                            @error('total_students')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="total_teachers" class="form-label">Total Guru</label>
                            <input type="number" class="form-control @error('total_teachers') is-invalid @enderror" 
                                   id="total_teachers" name="total_teachers" 
                                   value="{{ old('total_teachers', $schoolSetting->total_teachers ?? '') }}" min="0">
                            @error('total_teachers')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">Upload Gambar</h6>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="school_logo" class="form-label">Logo Sekolah</label>
                            <input type="file" class="form-control @error('school_logo') is-invalid @enderror"
                                   id="school_logo" name="school_logo" accept="image/*">
                            @error('school_logo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB.</small>
                            @if($schoolSetting && $schoolSetting->school_logo)
                                <div class="mt-2">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>Logo sudah diupload
                                    </small>
                                </div>
                            @endif
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="principal_photo" class="form-label">Foto Kepala Sekolah</label>
                            <input type="file" class="form-control @error('principal_photo') is-invalid @enderror"
                                   id="principal_photo" name="principal_photo" accept="image/*">
                            @error('principal_photo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB.</small>
                            @if($schoolSetting && $schoolSetting->principal_photo)
                                <div class="mt-2">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>Foto sudah diupload
                                    </small>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Logo Sekolah -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Logo Sekolah</h5>
            </div>
            <div class="card-body text-center">
                @if($schoolSetting && $schoolSetting->school_logo)
                    <img src="{{ Storage::url($schoolSetting->school_logo) }}" alt="Logo Sekolah" class="img-fluid mb-3" style="max-height: 150px;">
                @elseif(file_exists(public_path('images/Logo.png')))
                    <img src="{{ asset('images/Logo.png') }}" alt="Logo Sekolah" class="img-fluid mb-3" style="max-height: 150px;">
                    <div class="alert alert-info alert-sm">
                        <small><i class="fas fa-info-circle me-1"></i>Menggunakan logo default</small>
                    </div>
                @else
                    <div class="bg-light p-4 mb-3 rounded">
                        <i class="fas fa-school fa-3x text-muted"></i>
                        <p class="text-muted mt-2">Belum ada logo</p>
                    </div>
                @endif

            </div>
        </div>

        <!-- Foto Kepala Sekolah -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Foto Kepala Sekolah</h5>
            </div>
            <div class="card-body text-center">
                @if($schoolSetting && $schoolSetting->principal_photo)
                    <img src="{{ Storage::url($schoolSetting->principal_photo) }}" alt="Foto Kepala Sekolah" class="img-fluid rounded mb-3" style="max-height: 150px;">
                @else
                    <div class="bg-light p-4 mb-3 rounded">
                        <i class="fas fa-user fa-3x text-muted"></i>
                        <p class="text-muted mt-2">Belum ada foto</p>
                    </div>
                @endif

            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Aksi Cepat</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.settings.hero-sections') }}" class="btn btn-outline-primary">
                        <i class="fas fa-image me-2"></i>Kelola Hero Sections
                    </a>
                    <a href="{{ route('admin.settings.system') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-server me-2"></i>Pengaturan Sistem
                    </a>
                    <a href="{{ route('landing') }}" class="btn btn-outline-success" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>Preview Website
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
