@extends('layouts.dashboard')

@section('title', 'Edit Hero Section')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Edit Hero Section</h1>
        <p class="text-muted">Edit banner utama yang tampil di halaman depan website</p>
    </div>
    <div>
        <a href="{{ route('admin.settings.hero-sections') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Kembali
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Form Edit Hero Section
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.settings.hero-sections.update', $heroSection) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Judul <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" 
                                   value="{{ old('title', $heroSection->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="order" class="form-label">Urutan</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                   id="order" name="order" 
                                   value="{{ old('order', $heroSection->order ?? 0) }}" min="0">
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Urutan tampil (0 = paling atas)</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subjudul</label>
                        <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" name="subtitle" 
                               value="{{ old('subtitle', $heroSection->subtitle) }}" 
                               placeholder="Subjudul opsional">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Deskripsi</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" 
                                  placeholder="Deskripsi hero section...">{{ old('description', $heroSection->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="image" class="form-label">Gambar Background</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Format: JPG, PNG, GIF. Maksimal 2MB. Kosongkan jika tidak ingin mengubah gambar.</small>
                    </div>

                    <div class="mb-3">
                        <label for="video_url" class="form-label">URL Video</label>
                        <input type="url" class="form-control @error('video_url') is-invalid @enderror" 
                               id="video_url" name="video_url" 
                               value="{{ old('video_url', $heroSection->video_url) }}" 
                               placeholder="https://youtube.com/watch?v=...">
                        @error('video_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">URL video YouTube atau platform lainnya</small>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">Tombol Call-to-Action</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="button_text" class="form-label">Teks Tombol</label>
                            <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                   id="button_text" name="button_text" 
                                   value="{{ old('button_text', $heroSection->button_text) }}" 
                                   placeholder="Pelajari Lebih Lanjut">
                            @error('button_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="button_link" class="form-label">Link Tombol</label>
                            <input type="text" class="form-control @error('button_link') is-invalid @enderror" 
                                   id="button_link" name="button_link" 
                                   value="{{ old('button_link', $heroSection->button_link) }}" 
                                   placeholder="/tentang-kami atau https://...">
                            @error('button_link')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', $heroSection->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Aktifkan hero section ini
                            </label>
                        </div>
                        <small class="form-text text-muted">Hero section yang tidak aktif tidak akan ditampilkan di website</small>
                    </div>

                    <div class="d-flex justify-content-end">
                        <a href="{{ route('admin.settings.hero-sections') }}" class="btn btn-secondary me-2">
                            <i class="fas fa-times me-2"></i>Batal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Preview Current Image -->
        @if($heroSection->image)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Gambar Saat Ini</h5>
            </div>
            <div class="card-body text-center">
                <img src="{{ Storage::url($heroSection->image) }}" alt="{{ $heroSection->title }}" 
                     class="img-fluid rounded mb-3" style="max-height: 200px;">
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-danger btn-sm" id="removeImageBtn">
                        <i class="fas fa-trash me-1"></i>Hapus Gambar
                    </button>
                </div>
            </div>
        </div>
        @endif
        
        <!-- Preview Info -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Status:</small><br>
                    <span class="badge bg-{{ $heroSection->is_active ? 'success' : 'secondary' }}">
                        {{ $heroSection->is_active ? 'Aktif' : 'Nonaktif' }}
                    </span>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Dibuat:</small><br>
                    <small>{{ $heroSection->created_at->format('d M Y H:i') }}</small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Terakhir diubah:</small><br>
                    <small>{{ $heroSection->updated_at->format('d M Y H:i') }}</small>
                </div>
                
                @if($heroSection->video_url)
                <div class="mb-3">
                    <small class="text-muted">Video:</small><br>
                    <a href="{{ $heroSection->video_url }}" target="_blank" class="text-decoration-none">
                        <i class="fas fa-external-link-alt"></i> Lihat Video
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Remove image functionality
    $('#removeImageBtn').click(function() {
        Swal.fire({
            title: 'Hapus Gambar?',
            text: 'Gambar akan dihapus secara permanen.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("admin.settings.hero-sections.remove-image", $heroSection) }}',
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function(xhr) {
                        Swal.fire('Error!', 'Terjadi kesalahan saat menghapus gambar.', 'error');
                    }
                });
            }
        });
    });
    
    // Preview image on file select
    $('#image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // You can add image preview functionality here if needed
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush
