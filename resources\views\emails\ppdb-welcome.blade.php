<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selamat Datang di PPDB Online</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .content {
            padding: 30px 20px;
        }
        .registration-info {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .login-info {
            background-color: #e8f5e8;
            border: 1px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .login-credentials {
            background-color: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        .steps {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .steps li {
            margin-bottom: 10px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .contact-info {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Selamat Datang di PPDB Online!</h1>
            <p>Pendaftaran Anda telah berhasil diproses</p>
        </div>

        <!-- Content -->
        <div class="content">
            <h2>Halo, {{ $registration->full_name }}!</h2>
            
            <p>Terima kasih telah mendaftar di sistem PPDB Online kami. Pendaftaran Anda telah berhasil diproses dan berikut adalah informasi penting yang perlu Anda ketahui:</p>

            <!-- Registration Info -->
            <div class="registration-info">
                <h3>📋 Informasi Pendaftaran</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 5px 0; font-weight: bold;">Nomor Pendaftaran:</td>
                        <td style="padding: 5px 0; color: #667eea; font-weight: bold;">{{ $registration->registration_number }}</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; font-weight: bold;">Program Pilihan:</td>
                        <td style="padding: 5px 0;">{{ $registration->program->name }} - {{ $registration->program->level }}</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; font-weight: bold;">Status:</td>
                        <td style="padding: 5px 0; color: #ffc107; font-weight: bold;">Menunggu Verifikasi</td>
                    </tr>
                    <tr>
                        <td style="padding: 5px 0; font-weight: bold;">Tanggal Daftar:</td>
                        <td style="padding: 5px 0;">{{ $registration->created_at->format('d F Y, H:i') }} WIB</td>
                    </tr>
                </table>
            </div>

            <!-- Login Info -->
            <div class="login-info">
                <h3>🔐 Informasi Login Portal Calon Siswa</h3>
                <p>Kami telah membuatkan akun untuk Anda di Portal Calon Siswa. Gunakan informasi login berikut:</p>
                
                <div class="login-credentials">
                    <strong>Email:</strong> {{ $user->email }}<br>
                    <strong>Password Sementara:</strong> {{ $temporaryPassword }}
                </div>

                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; margin: 15px 0; border-radius: 0 5px 5px 0;">
                    <strong>📧 Verifikasi Email:</strong><br>
                    Kami telah mengirimkan email verifikasi terpisah ke alamat email Anda.
                    <strong>Silakan verifikasi email terlebih dahulu sebelum login</strong> dengan mengklik link yang ada di email verifikasi tersebut.
                </div>

                <div class="warning">
                    <strong>⚠️ Penting:</strong><br>
                    • <strong>Verifikasi email terlebih dahulu</strong> sebelum login<br>
                    • Segera ganti password setelah login pertama kali untuk keamanan akun
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <a href="{{ route('login') }}" class="button">Login ke Portal Calon Siswa</a>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="steps">
                <h3>📝 Langkah Selanjutnya</h3>
                <ol>
                    <li><strong>Login ke Portal Calon Siswa</strong> menggunakan email dan password di atas</li>
                    <li><strong>Lengkapi profil Anda</strong> dan ganti password untuk keamanan</li>
                    <li><strong>Upload dokumen yang diperlukan:</strong>
                        <ul style="margin-top: 10px;">
                            <li>Ijazah/SKHUN</li>
                            <li>Kartu Keluarga</li>
                            <li>Akta Kelahiran</li>
                            <li>Pas Foto 3x4 (2 lembar)</li>
                            <li>Surat Keterangan Sehat</li>
                            <li>Rapor Semester Terakhir</li>
                        </ul>
                    </li>
                    <li><strong>Tunggu proses verifikasi</strong> dari tim kami</li>
                    <li><strong>Ikuti tes masuk</strong> sesuai jadwal yang akan diinformasikan</li>
                </ol>
            </div>

            <!-- Important Notes -->
            <div class="warning">
                <h4>📌 Catatan Penting:</h4>
                <ul style="margin: 10px 0;">
                    <li>Simpan nomor pendaftaran Anda dengan baik</li>
                    <li>Pastikan semua dokumen yang diupload jelas dan sesuai ketentuan</li>
                    <li>Pantau status pendaftaran Anda secara berkala melalui portal</li>
                    <li>Hubungi kami jika mengalami kesulitan dalam proses pendaftaran</li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="contact-info">
                <h4>📞 Butuh Bantuan?</h4>
                <p>
                    <strong>Telepon:</strong> (*************<br>
                    <strong>Email:</strong> <EMAIL><br>
                    <strong>Jam Layanan:</strong> Senin - Jumat, 08:00 - 16:00 WIB
                </p>
            </div>

            <p style="margin-top: 30px;">Sekali lagi, selamat datang di keluarga besar sekolah kami. Kami berharap Anda dapat bergabung dan meraih prestasi terbaik bersama kami!</p>

            <p style="margin-top: 20px;">
                Salam hangat,<br>
                <strong>Tim PPDB Online<br>
                {{ config('app.name') }}</strong>
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Email ini dikirim secara otomatis oleh sistem PPDB Online. Mohon tidak membalas email ini.</p>
            <p>© {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
