<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('galleries', function (Blueprint $table) {
            // Change category from enum to varchar to allow more flexibility
            $table->string('category', 50)->default('kegiatan')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('galleries', function (Blueprint $table) {
            // Revert back to enum
            $table->enum('category', ['kegiatan', 'fasilitas', 'prestasi', 'event', 'lainnya'])->default('kegiatan')->change();
        });
    }
};
