<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('email_verification_token')->nullable()->after('email_verified_at');
            $table->timestamp('email_verification_sent_at')->nullable()->after('email_verification_token');
            $table->timestamp('email_verification_expires_at')->nullable()->after('email_verification_sent_at');
            $table->string('previous_user_type')->nullable()->after('user_type')->comment('Store previous role for rollback');
            $table->timestamp('role_changed_at')->nullable()->after('previous_user_type');
            $table->unsignedBigInteger('role_changed_by')->nullable()->after('role_changed_at');
            $table->text('role_change_reason')->nullable()->after('role_changed_by');

            $table->foreign('role_changed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['role_changed_by']);
            $table->dropColumn([
                'email_verification_token',
                'email_verification_sent_at',
                'email_verification_expires_at',
                'previous_user_type',
                'role_changed_at',
                'role_changed_by',
                'role_change_reason'
            ]);
        });
    }
};
