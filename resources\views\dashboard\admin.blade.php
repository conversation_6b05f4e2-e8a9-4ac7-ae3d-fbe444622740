@extends('layouts.dashboard')

@section('title', 'Dashboard Admin')
@section('page-title', 'Dashboard Admin')

@section('sidebar-menu')
<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" href="{{ route('profile.show') }}">
            <i class="fas fa-user-circle"></i>
            Profil Saya
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-user-graduate"></i>
            Data Siswa
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-chalkboard-teacher"></i>
            Data Guru
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-users-cog"></i>
            Data Orang Tua
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-newspaper"></i>
            Berita & Pengumuman
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-graduation-cap"></i>
            Program Pendidikan
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-building"></i>
            Fasilitas
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#">
            <i class="fas fa-images"></i>
            Galeri
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="{{ route('landing') }}" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-success">
            <i class="fas fa-user-shield me-2"></i>
            Selamat datang, <strong>{{ Auth::user()->name }}</strong>! Anda login sebagai Admin dengan akses manajemen konten.
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="stats-number">{{ $stats['total_students'] }}</div>
            <div class="stats-label">Total Siswa</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-chalkboard-teacher"></i>
            </div>
            <div class="stats-number">{{ $stats['total_teachers'] }}</div>
            <div class="stats-label">Total Guru</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-newspaper"></i>
            </div>
            <div class="stats-number">{{ $stats['total_news'] }}</div>
            <div class="stats-label">Total Berita</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stats-number">{{ $stats['total_programs'] }}</div>
            <div class="stats-label">Program Pendidikan</div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-newspaper me-2"></i>Berita Terbaru
            </div>
            <div class="card-body">
                @forelse($recentNews as $news)
                    <div class="d-flex align-items-start mb-3">
                        @if($news->featured_image)
                            <img src="{{ asset('storage/' . $news->featured_image) }}" alt="News" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                        @else
                            <div class="rounded bg-gradient-primary d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-newspaper text-white"></i>
                            </div>
                        @endif
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ Str::limit($news->title, 50) }}</h6>
                            <small class="text-muted">
                                {{ $news->created_at->diffForHumans() }}
                            </small>
                            <br>
                            <span class="badge bg-{{ $news->status === 'published' ? 'success' : 'warning' }}">
                                {{ $news->status === 'published' ? 'Published' : ucfirst($news->status) }}
                            </span>
                        </div>
                    </div>
                @empty
                    <p class="text-muted text-center">Belum ada berita terbaru</p>
                @endforelse
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-graduate me-2"></i>Siswa Terbaru
            </div>
            <div class="card-body">
                @forelse($recentStudents as $student)
                    <div class="d-flex align-items-center mb-3">
                        @if($student->avatar)
                            <img src="{{ asset('storage/' . $student->avatar) }}" alt="Avatar" class="rounded-circle me-3" width="40" height="40">
                        @else
                            <div class="rounded-circle bg-gradient-success d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">{{ strtoupper(substr($student->name, 0, 1)) }}</span>
                            </div>
                        @endif
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ $student->name }}</h6>
                            <small class="text-muted">
                                NISN: {{ $student->nisn ?? '-' }} • {{ $student->created_at->diffForHumans() }}
                            </small>
                        </div>
                        <span class="badge bg-{{ $student->is_active ? 'success' : 'danger' }}">
                            {{ $student->is_active ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </div>
                @empty
                    <p class="text-muted text-center">Belum ada siswa terbaru</p>
                @endforelse
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>Aksi Cepat
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-graduate me-2"></i>Tambah Siswa
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-newspaper me-2"></i>Buat Berita
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-graduation-cap me-2"></i>Kelola Program
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-danger w-100">
                            <i class="fas fa-images me-2"></i>Kelola Galeri
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Management Overview -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>Ringkasan Konten
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-newspaper fa-2x text-primary mb-2"></i>
                            <h5>{{ $stats['total_news'] }}</h5>
                            <small class="text-muted">Berita & Pengumuman</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-graduation-cap fa-2x text-success mb-2"></i>
                            <h5>{{ $stats['total_programs'] }}</h5>
                            <small class="text-muted">Program Pendidikan</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-user-graduate fa-2x text-warning mb-2"></i>
                            <h5>{{ $stats['total_students'] }}</h5>
                            <small class="text-muted">Data Siswa</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-chalkboard-teacher fa-2x text-danger mb-2"></i>
                            <h5>{{ $stats['total_teachers'] }}</h5>
                            <small class="text-muted">Data Guru</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
