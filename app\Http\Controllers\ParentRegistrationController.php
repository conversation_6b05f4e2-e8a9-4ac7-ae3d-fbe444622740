<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Models\ParentRegistration;
use App\Models\User;
use App\Models\SchoolClass;

class ParentRegistrationController extends Controller
{
    /**
     * Show parent registration form
     */
    public function index()
    {
        $classes = SchoolClass::active()
            ->currentYear()
            ->orderBy('level')
            ->orderBy('major')
            ->orderBy('class_number')
            ->get();

        return view('parent-registration.index', compact('classes'));
    }

    /**
     * Handle parent registration
     */
    public function register(Request $request)
    {
        try {
            \Log::info('=== PARENT REGISTRATION START ===');
            \Log::info('Request data: ' . json_encode($request->except(['_token'])));

            // Validate request
            $validator = Validator::make($request->all(), [
                'parent_name' => 'required|string|max:255',
                'parent_email' => 'required|email|unique:parent_registrations,parent_email|unique:users,email',
                'parent_phone' => 'required|string|max:20',
                'parent_type' => 'required|in:ayah,ibu,wali',
                'parent_nik' => 'required|string|size:16|unique:parent_registrations,parent_nik',
                'parent_birth_date' => 'required|date',
                'parent_occupation' => 'nullable|string|max:255',
                'parent_address' => 'required|string',
                'student_name' => 'required_if:verification_method,nisn|string|max:255',
                'student_nisn' => 'nullable|string|size:10',
                'student_class' => 'nullable|string|max:50',
                'relationship' => 'required|in:ayah,ibu,wali,kakek,nenek,paman,bibi,lainnya',
                'student_verification_code' => 'required_if:verification_method,code|string|max:20',
                'verification_method' => 'required|in:code,nisn',
            ]);

            if ($validator->fails()) {
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Data tidak valid',
                        'errors' => $validator->errors()
                    ], 422);
                }
                return back()->withErrors($validator)->withInput();
            }

            // Generate registration number
            $registrationNumber = ParentRegistration::generateRegistrationNumber();

            // Create parent registration
            $registration = ParentRegistration::create([
                'registration_number' => $registrationNumber,
                'parent_name' => $request->parent_name,
                'parent_email' => $request->parent_email,
                'parent_phone' => $request->parent_phone,
                'parent_type' => $request->parent_type,
                'parent_nik' => $request->parent_nik,
                'parent_birth_date' => $request->parent_birth_date,
                'parent_occupation' => $request->parent_occupation,
                'parent_address' => $request->parent_address,
                'student_name' => $request->student_name,
                'student_nisn' => $request->student_nisn,
                'student_class' => $request->student_class,
                'relationship' => $request->relationship,
                'student_verification_code' => $request->student_verification_code,
                'status' => 'pending',
            ]);

            \Log::info('Parent registration created: ' . $registration->registration_number);

            // Try to auto-verify based on verification method
            if ($request->verification_method === 'code' && $request->student_verification_code) {
                $student = User::where('student_verification_code', $request->student_verification_code)
                    ->where('user_type', 'siswa')
                    ->first();

                if ($student && $registration->verifyStudent($student)) {
                    \Log::info('Parent registration auto-verified with student code: ' . $student->name);
                }
            } elseif ($request->verification_method === 'nisn') {
                // Try to find student by NISN first
                $student = null;

                if ($request->student_nisn) {
                    $student = User::where('nisn', $request->student_nisn)
                        ->where('user_type', 'siswa')
                        ->first();
                }

                // If not found by NISN, try by name similarity
                if (!$student && $request->student_name) {
                    $students = User::where('user_type', 'siswa')
                        ->get();

                    foreach ($students as $potentialStudent) {
                        $similarity = 0;
                        similar_text(
                            strtolower(trim($request->student_name)),
                            strtolower(trim($potentialStudent->name)),
                            $similarity
                        );

                        if ($similarity >= 80) { // 80% similarity threshold
                            $student = $potentialStudent;
                            \Log::info("Found student by name similarity: {$similarity}% - {$potentialStudent->name}");
                            break;
                        }
                    }
                }

                if ($student && $registration->verifyStudent($student)) {
                    \Log::info('Parent registration auto-verified with student NISN/name: ' . $student->name);
                } else {
                    \Log::info('Student not found or verification failed - will require manual admin verification');
                }
            }

            // Send confirmation email
            // TODO: Implement email sending

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pendaftaran orang tua berhasil! Silakan tunggu verifikasi dari admin.',
                    'registration_number' => $registration->registration_number,
                    'redirect_url' => route('parent-registration.success', $registration->registration_number)
                ]);
            }

            return redirect()->route('parent-registration.success', $registration->registration_number)
                ->with('success', 'Pendaftaran orang tua berhasil! Silakan tunggu verifikasi dari admin.');

        } catch (\Exception $e) {
            \Log::error('=== PARENT REGISTRATION ERROR ===');
            \Log::error('Error: ' . $e->getMessage());
            \Log::error('File: ' . $e->getFile() . ' Line: ' . $e->getLine());

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.'
                ], 500);
            }

            return back()->with('error', 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.')
                ->withInput();
        }
    }

    /**
     * Show success page
     */
    public function success($registrationNumber)
    {
        $registration = ParentRegistration::where('registration_number', $registrationNumber)->firstOrFail();

        return view('parent-registration.success', compact('registration'));
    }

    /**
     * Check student verification code
     */
    public function checkStudentCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:20'
        ]);

        $student = User::where('student_verification_code', $request->code)
            ->where('user_type', 'siswa')
            ->first();

        if ($student) {
            return response()->json([
                'success' => true,
                'student' => [
                    'name' => $student->name,
                    'nisn' => $student->nisn,
                    'class' => $student->student_class ?? 'Belum ditentukan'
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Kode verifikasi tidak ditemukan'
        ]);
    }
}
