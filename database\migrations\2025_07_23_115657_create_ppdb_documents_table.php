<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ppdb_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ppdb_registration_id')->constrained()->onDelete('cascade');
            $table->string('document_type'); // ijazah, kartu_keluarga, akta_kelahiran, dll
            $table->string('document_name'); // Nama file asli
            $table->string('file_path'); // Path file di storage
            $table->string('file_size')->nullable(); // Ukuran file
            $table->string('mime_type')->nullable(); // Tipe file
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('rejection_reason')->nullable(); // Alasan ditolak
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('verified_at')->nullable();
            $table->timestamps();

            $table->index(['ppdb_registration_id', 'document_type']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ppdb_documents');
    }
};
