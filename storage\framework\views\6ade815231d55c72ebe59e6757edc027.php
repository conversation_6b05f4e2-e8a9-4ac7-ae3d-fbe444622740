<?php $__env->startSection('title', $facility->name . ' - Fasilitas'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section bg-gradient" style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 100px 0 50px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('landing')); ?>" class="text-white-50">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('landing')); ?>#facilities" class="text-white-50">Fasilitas</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e($facility->name); ?></li>
                    </ol>
                </nav>
                <h1 class="display-4 fw-bold mb-3"><?php echo e($facility->name); ?></h1>
                <p class="lead mb-0"><?php echo e($facility->description); ?></p>
            </div>
        </div>
    </div>
</section>

<!-- Facility Content -->
<section class="section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Featured Image -->
                <?php if($facility->image): ?>
                <div class="mb-5" data-aos="fade-up">
                    <img src="<?php echo e(asset('storage/' . $facility->image)); ?>" 
                         alt="<?php echo e($facility->name); ?>" 
                         class="img-fluid rounded shadow-lg w-100"
                         style="max-height: 500px; object-fit: cover;">
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Main Content -->
                    <div class="col-lg-8">
                        <!-- Facility Description -->
                        <div class="facility-content mb-5" data-aos="fade-up">
                            <h3 class="mb-4">Tentang <?php echo e($facility->name); ?></h3>
                            <div class="content">
                                <?php echo nl2br(e($facility->description)); ?>

                            </div>
                        </div>

                        <!-- Specifications -->
                        <?php if($facility->specifications): ?>
                        <div class="facility-specs mb-5" data-aos="fade-up" data-aos-delay="100">
                            <h3 class="mb-4">Spesifikasi</h3>
                            <div class="content">
                                <?php echo nl2br(e($facility->specifications)); ?>

                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Features -->
                        <?php if($facility->features && is_array($facility->features) && count($facility->features) > 0): ?>
                        <div class="facility-features mb-5" data-aos="fade-up" data-aos-delay="200">
                            <h3 class="mb-4">Fitur-fitur</h3>
                            <div class="row">
                                <?php $__currentLoopData = $facility->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3">
                                            <i class="fas fa-check-circle text-success"></i>
                                        </div>
                                        <span><?php echo e($feature); ?></span>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Facility Info -->
                        <div class="facility-info-card mb-4" data-aos="fade-up" data-aos-delay="300">
                            <h4 class="mb-4">Informasi Fasilitas</h4>
                            
                            <div class="info-item mb-3">
                                <div class="info-label">Kategori</div>
                                <div class="info-value">
                                    <?php switch($facility->category):
                                        case ('academic'): ?>
                                            <span class="badge bg-primary">Akademik</span>
                                            <?php break; ?>
                                        <?php case ('sports'): ?>
                                            <span class="badge bg-success">Olahraga</span>
                                            <?php break; ?>
                                        <?php case ('library'): ?>
                                            <span class="badge bg-info">Perpustakaan</span>
                                            <?php break; ?>
                                        <?php case ('laboratory'): ?>
                                            <span class="badge bg-warning">Laboratorium</span>
                                            <?php break; ?>
                                        <?php case ('dormitory'): ?>
                                            <span class="badge bg-secondary">Asrama</span>
                                            <?php break; ?>
                                        <?php case ('cafeteria'): ?>
                                            <span class="badge bg-danger">Kantin</span>
                                            <?php break; ?>
                                        <?php case ('mosque'): ?>
                                            <span class="badge bg-dark">Masjid</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-light text-dark">Lainnya</span>
                                    <?php endswitch; ?>
                                </div>
                            </div>

                            <?php if($facility->capacity): ?>
                            <div class="info-item mb-3">
                                <div class="info-label">Kapasitas</div>
                                <div class="info-value"><?php echo e(number_format($facility->capacity)); ?> orang</div>
                            </div>
                            <?php endif; ?>

                            <div class="info-item mb-3">
                                <div class="info-label">Status</div>
                                <div class="info-value">
                                    <?php if($facility->is_available): ?>
                                        <span class="badge bg-success">Tersedia</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Tidak Tersedia</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if($facility->is_featured): ?>
                            <div class="info-item">
                                <div class="info-label">Status</div>
                                <div class="info-value">
                                    <span class="badge bg-warning text-dark">Fasilitas Unggulan</span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Contact Info -->
                        <div class="contact-info-card" data-aos="fade-up" data-aos-delay="400">
                            <h4 class="mb-4">Informasi Lebih Lanjut</h4>
                            <p class="mb-3">Untuk informasi lebih lanjut tentang fasilitas ini, silakan hubungi kami:</p>
                            
                            <div class="contact-item mb-3">
                                <i class="fas fa-phone text-primary me-2"></i>
                                <span>(*************</span>
                            </div>
                            
                            <div class="contact-item mb-3">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span><EMAIL></span>
                            </div>
                            
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <span>Jl. Pendidikan No. 123, Jakarta</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Facilities -->
<section class="section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">Fasilitas Lainnya</h2>
                <p class="section-subtitle">Jelajahi fasilitas unggulan lainnya yang kami miliki</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php
                $relatedFacilities = App\Models\Facility::where('is_available', true)
                    ->where('is_featured', true)
                    ->where('id', '!=', $facility->id)
                    ->take(3)
                    ->get();
            ?>
            
            <?php $__empty_1 = true; $__currentLoopData = $relatedFacilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                <div class="facility-card">
                    <?php if($related->image): ?>
                        <img src="<?php echo e(asset('storage/' . $related->image)); ?>" alt="<?php echo e($related->name); ?>" class="facility-bg">
                    <?php else: ?>
                        <div class="facility-bg placeholder-container placeholder-facility">
                            <div>
                                <i class="fas fa-building fa-2x placeholder-icon"></i>
                                <div><?php echo e($related->name); ?></div>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="facility-overlay">
                        <div>
                            <h4 class="facility-title"><?php echo e($related->name); ?></h4>
                            <p class="mb-3"><?php echo e(Str::limit($related->description, 80)); ?></p>
                            <a href="<?php echo e(route('landing.facility', $related->slug)); ?>" class="btn btn-outline-light btn-sm">
                                Lihat Detail <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12 text-center">
                <p class="text-muted">Tidak ada fasilitas lainnya untuk ditampilkan.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.facility-content .content,
.facility-specs .content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
}

.facility-info-card,
.contact-info-card {
    background: #fff;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.info-item {
    border-bottom: 1px solid #f8f9fa;
    padding-bottom: 0.75rem;
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-value {
    font-weight: 600;
    color: #495057;
}

.contact-item {
    display: flex;
    align-items: center;
}

.feature-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.facility-card {
    position: relative;
    height: 300px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.facility-card:hover {
    transform: translateY(-5px);
}

.facility-bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.facility-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    color: white;
    padding: 2rem;
    display: flex;
    align-items: end;
}

.facility-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.placeholder-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #6c757d;
    text-align: center;
}

.placeholder-facility {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.landing', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/landing/facility-detail.blade.php ENDPATH**/ ?>