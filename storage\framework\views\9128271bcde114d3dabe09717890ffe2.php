<?php $__env->startSection('title', 'Aktivitas User'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Aktivitas User</h1>
        <p class="text-muted">Monitor aktivitas semua user dalam sistem</p>
    </div>
    <div>
        <a href="<?php echo e(route('admin.security.dashboard')); ?>" class="btn btn-outline-info me-2">
            <i class="fas fa-chart-line me-2"></i>Dashboard Keamanan
        </a>
        <a href="<?php echo e(route('admin.security.index')); ?>" class="btn btn-outline-primary">
            <i class="fas fa-list me-2"></i>Log <PERSON>
        </a>
    </div>
</div>

<!-- Search Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.security.user-activity')); ?>" class="row g-3">
            <div class="col-md-8">
                <label for="search" class="form-label">Cari User</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari nama atau email user..." 
                       value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Cari
                </button>
                <a href="<?php echo e(route('admin.security.user-activity')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- User Activity List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Daftar User dan Aktivitas (30 Hari Terakhir)</h5>
    </div>
    <div class="card-body">
        <?php if($users->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Tipe User</th>
                            <th>Total Aktivitas</th>
                            <th>Login Terakhir</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-3">
                                        <?php if($user->avatar): ?>
                                            <img src="<?php echo e(Storage::url($user->avatar)); ?>" alt="<?php echo e($user->name); ?>" class="rounded-circle">
                                        <?php else: ?>
                                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                                                <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <strong><?php echo e($user->name); ?></strong>
                                        <br><small class="text-muted"><?php echo e($user->email); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($user->user_type == 'super_admin' ? 'danger' : ($user->user_type == 'admin' ? 'warning' : ($user->user_type == 'guru' ? 'info' : ($user->user_type == 'siswa' ? 'success' : 'secondary')))); ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $user->user_type))); ?>

                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary fs-6 me-2"><?php echo e($user->security_logs_count); ?></span>
                                    <div class="progress flex-grow-1" style="height: 8px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: <?php echo e(min(($user->security_logs_count / 100) * 100, 100)); ?>%">
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php if($user->last_login_at): ?>
                                    <div>
                                        <small><?php echo e($user->last_login_at->format('d M Y H:i')); ?></small>
                                        <br><small class="text-muted"><?php echo e($user->last_login_at->diffForHumans()); ?></small>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Belum pernah login</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($user->is_active): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Aktif
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times-circle me-1"></i>Nonaktif
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.security.user-activity.detail', $user)); ?>" class="btn btn-sm btn-outline-primary" title="Detail Aktivitas">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.users.show', $user)); ?>" class="btn btn-sm btn-outline-info" title="Profile User">
                                        <i class="fas fa-user"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan <?php echo e($users->firstItem()); ?> - <?php echo e($users->lastItem()); ?> dari <?php echo e($users->total()); ?> user
                </div>
                <div class="pagination-wrapper">
                    <?php echo e($users->links()); ?>

                </div>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada user ditemukan</h5>
                <p class="text-muted">Belum ada user yang sesuai dengan pencarian yang dilakukan.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Activity Summary -->
<div class="row mt-4">
    <div class="col-lg-4 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-3"></i>
                <h4><?php echo e($users->total()); ?></h4>
                <p class="text-muted">Total User</p>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-user-check fa-2x text-success mb-3"></i>
                <h4><?php echo e($users->where('is_active', true)->count()); ?></h4>
                <p class="text-muted">User Aktif</p>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                <h4><?php echo e($users->where('last_login_at', '>=', now()->subDays(7))->count()); ?></h4>
                <p class="text-muted">Login 7 Hari Terakhir</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-sm img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    background-color: #6c757d;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background-color: #007bff;
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

/* Fix for pagination SVG icons that are too large */
.pagination-wrapper .pagination svg {
    width: 16px !important;
    height: 16px !important;
}

.pagination-wrapper .pagination .page-link svg {
    width: 14px !important;
    height: 14px !important;
}

/* Override Tailwind classes that might cause large icons */
.pagination-wrapper .w-5 {
    width: 16px !important;
}

.pagination-wrapper .h-5 {
    height: 16px !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Add tooltip to progress bars
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Auto-refresh every 5 minutes (reduced from 60 seconds to prevent fetch errors)
    setInterval(function() {
        if (!$('.modal').hasClass('show') && !document.hidden) {
            // Use fetch API with proper error handling
            fetch(window.location.href, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'text/html'
                }
            }).then(response => {
                if (response.ok) {
                    location.reload();
                }
            }).catch(error => {
                console.log('Auto-refresh failed:', error);
                // Don't reload if fetch fails
            });
        }
    }, 300000); // 5 minutes
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/security/user-activity.blade.php ENDPATH**/ ?>