<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\SchoolSetting;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share school settings with all views
        View::composer('*', function ($view) {
            $schoolSettings = SchoolSetting::where('is_active', true)->first();
            $view->with('schoolSettings', $schoolSettings);
        });
    }
}
