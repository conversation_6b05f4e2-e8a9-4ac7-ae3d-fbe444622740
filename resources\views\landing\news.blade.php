@extends('layouts.landing')

@section('title', 'Berita & Pengumuman')

@section('content')
<!-- Hero Section -->
<section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 120px 0 80px;">
    <div class="container">
        <div class="row justify-content-center text-center text-white">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4" data-aos="fade-up">Berita & Pengumuman</h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Informasi terkini seputar kegiatan, pencapaian, dan pengumuman penting sekolah
                </p>
                <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="200">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}" class="text-white-50">Beranda</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Berita</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- News Grid Section -->
<section class="section">
    <div class="container">
        <div class="row g-4">
            @forelse($news as $article)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    @php
                        // Check if this is PPDB related news for special styling
                        $isPPDBNews = str_contains(strtolower($article->title), 'ppdb') || str_contains(strtolower($article->title), 'penerimaan peserta didik baru');
                    @endphp

                    {{-- Featured articles get special styling and badge --}}
                    <div class="card news-card h-100 {{ $article->is_featured ? 'border-warning shadow-lg' : '' }}">
                        @if($article->is_featured)
                            <div class="position-absolute top-0 start-0 m-2" style="z-index: 10;">
                                {{-- PPDB news get special PPDB ONLINE badge --}}
                                @if($isPPDBNews)
                                    <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                        <i class="fas fa-star me-1"></i>PPDB ONLINE
                                    </span>
                                {{-- Other featured news get generic FEATURED badge --}}
                                @else
                                    <span class="badge bg-primary text-white fs-6 px-3 py-2">
                                        <i class="fas fa-star me-1"></i>FEATURED
                                    </span>
                                @endif
                            </div>
                        @endif

                        <div class="news-image">
                            @if($article->featured_image)
                                <img src="{{ asset('storage/' . $article->featured_image) }}" alt="{{ $article->title }}">
                            @elseif($isPPDBNews)
                                <div class="placeholder-container placeholder-news ppdb">
                                    <div class="text-center">
                                        <i class="fas fa-graduation-cap fa-2x placeholder-icon"></i>
                                        <div>PPDB ONLINE</div>
                                    </div>
                                </div>
                            @else
                                <div class="placeholder-container placeholder-news">
                                    <div class="text-center">
                                        <i class="fas fa-newspaper fa-2x placeholder-icon"></i>
                                        <div>{{ Str::limit($article->title, 20) }}</div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <span class="badge bg-primary">{{ $article->published_at->format('d M Y') }}</span>
                                @if($article->type == 'announcement')
                                    <span class="badge bg-warning text-dark ms-2">Pengumuman</span>
                                @endif
                            </div>
                            <h5 class="card-title {{ $article->is_featured ? 'fw-bold' : '' }} {{ $isPPDBNews ? 'text-warning' : '' }}">{{ Str::limit($article->title, 60) }}</h5>
                            <p class="card-text">{{ Str::limit($article->excerpt, 100) }}</p>

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ $article->author->name ?? 'Admin' }}
                                </small>
                                <a href="{{ route('landing.news', $article->slug) }}" class="btn btn-primary btn-sm">
                                    Baca Selengkapnya
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Belum Ada Berita</h4>
                        <p class="text-muted">Berita dan pengumuman akan segera diperbarui.</p>
                        <a href="{{ route('landing') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Kembali ke Beranda
                        </a>
                    </div>
                </div>
            @endforelse
        </div>
        
        <!-- Pagination -->
        @if($news->hasPages())
            <div class="d-flex justify-content-center mt-5">
                {{ $news->links() }}
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="section bg-light">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h3 class="mb-4">Tetap Terhubung dengan Kami</h3>
                <p class="text-muted mb-4">Ikuti terus perkembangan dan informasi terbaru dari sekolah kami.</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="#contact" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>Subscribe Newsletter
                    </a>
                    <a href="{{ route('landing') }}#ppdb" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Daftar PPDB
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.news-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.news-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.placeholder-container.placeholder-news {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
}

.placeholder-news.ppdb {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
    color: #000 !important;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
}

.placeholder-news .placeholder-icon {
    opacity: 0.7;
}

/* Responsive */
@media (max-width: 768px) {
    .news-image {
        height: 180px;
    }
}
</style>
@endpush
