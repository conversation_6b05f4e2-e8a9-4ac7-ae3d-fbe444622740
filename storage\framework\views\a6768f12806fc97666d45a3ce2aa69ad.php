<?php $__env->startSection('title', 'Dashboard Administrator'); ?>
<?php $__env->startSection('page-title', 'Dashboard Administrator'); ?>

<?php $__env->startSection('sidebar-menu'); ?>
<ul class="nav flex-column">
    <li class="nav-item">
        <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </li>

    <!-- User Management -->
    <li class="nav-item">
        <a class="nav-link <?php echo e(request()->routeIs('admin.users.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.users.index')); ?>">
            <i class="fas fa-users"></i>
            Manajemen User
        </a>
    </li>

    <!-- Content Management -->
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.content.*') ? 'active' : ''); ?>" href="#"
           data-bs-toggle="collapse" data-bs-target="#contentMenu" aria-expanded="false">
            <i class="fas fa-edit"></i>
            Manajemen Konten
        </a>
        <div class="collapse <?php echo e(request()->routeIs('admin.content.*') ? 'show' : ''); ?>" id="contentMenu">
            <ul class="nav flex-column ms-3">
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.content.news.*') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.content.news.index')); ?>">
                        <i class="fas fa-newspaper"></i>
                        Berita & Pengumuman
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.content.programs.*') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.content.programs.index')); ?>">
                        <i class="fas fa-graduation-cap"></i>
                        Program Pendidikan
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.content.facilities.*') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.content.facilities.index')); ?>">
                        <i class="fas fa-building"></i>
                        Fasilitas
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.content.gallery.*') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.content.gallery.index')); ?>">
                        <i class="fas fa-images"></i>
                        Galeri
                    </a>
                </li>
            </ul>
        </div>
    </li>

    <!-- PPDB Management -->
    <li class="nav-item">
        <a class="nav-link <?php echo e(request()->routeIs('admin.ppdb.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.ppdb.dashboard')); ?>">
            <i class="fas fa-user-graduate"></i>
            PPDB Online
        </a>
    </li>

    <!-- School Settings -->
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.settings.*') ? 'active' : ''); ?>" href="#"
           data-bs-toggle="collapse" data-bs-target="#settingsMenu" aria-expanded="false">
            <i class="fas fa-cog"></i>
            Pengaturan Sekolah
        </a>
        <div class="collapse <?php echo e(request()->routeIs('admin.settings.*') ? 'show' : ''); ?>" id="settingsMenu">
            <ul class="nav flex-column ms-3">
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.settings.index') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.settings.index')); ?>">
                        <i class="fas fa-school"></i>
                        Info Sekolah
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.settings.hero-sections*') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.settings.hero-sections')); ?>">
                        <i class="fas fa-image"></i>
                        Hero Sections
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.settings.system*') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.settings.system')); ?>">
                        <i class="fas fa-server"></i>
                        Sistem
                    </a>
                </li>
            </ul>
        </div>
    </li>

    <!-- Security & Logs -->
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.security.*') ? 'active' : ''); ?>" href="#"
           data-bs-toggle="collapse" data-bs-target="#securityMenu" aria-expanded="false">
            <i class="fas fa-shield-alt"></i>
            Keamanan & Log
        </a>
        <div class="collapse <?php echo e(request()->routeIs('admin.security.*') ? 'show' : ''); ?>" id="securityMenu">
            <ul class="nav flex-column ms-3">
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.security.dashboard') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.security.dashboard')); ?>">
                        <i class="fas fa-chart-line"></i>
                        Dashboard Keamanan
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.security.index') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.security.index')); ?>">
                        <i class="fas fa-list"></i>
                        Log Keamanan
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.security.user-activity*') ? 'active' : ''); ?>"
                       href="<?php echo e(route('admin.security.user-activity')); ?>">
                        <i class="fas fa-user-clock"></i>
                        Aktivitas User
                    </a>
                </li>
            </ul>
        </div>
    </li>

    <li class="nav-item mt-3">
        <hr class="sidebar-divider">
    </li>

    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('landing')); ?>" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Lihat Website
        </a>
    </li>
</ul>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.sidebar-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

.nav-link.dropdown-toggle {
    position: relative;
}

.nav-link.dropdown-toggle::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.nav-link.dropdown-toggle[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}

.nav-link.dropdown-toggle::before {
    display: none;
}

.collapse .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.collapse .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.collapse .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
}

.collapse .nav-link i {
    width: 20px;
    margin-right: 8px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Selamat datang, <strong><?php echo e(Auth::user()->name); ?></strong>! Anda login sebagai SuperAdmin dengan akses penuh ke sistem.
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_users']); ?></div>
            <div class="stats-label">Total Pengguna</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_students']); ?></div>
            <div class="stats-label">Total Siswa</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-chalkboard-teacher"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_teachers']); ?></div>
            <div class="stats-label">Total Guru</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-users-cog"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_parents']); ?></div>
            <div class="stats-label">Total Orang Tua</div>
        </div>
    </div>
</div>

<!-- Content Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-newspaper"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_news']); ?></div>
            <div class="stats-label">Total Berita</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_programs']); ?></div>
            <div class="stats-label">Program Pendidikan</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-building"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_facilities']); ?></div>
            <div class="stats-label">Fasilitas</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-images"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_gallery']); ?></div>
            <div class="stats-label">Galeri</div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-users me-2"></i>Pengguna Terbaru
            </div>
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $recentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="d-flex align-items-center mb-3">
                        <?php if($user->avatar): ?>
                            <img src="<?php echo e(asset('storage/' . $user->avatar)); ?>" alt="Avatar" class="rounded-circle me-3" width="40" height="40">
                        <?php else: ?>
                            <div class="rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold"><?php echo e(strtoupper(substr($user->name, 0, 1))); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo e($user->name); ?></h6>
                            <small class="text-muted">
                                <?php echo e(ucfirst($user->user_type)); ?> • <?php echo e($user->created_at->diffForHumans()); ?>

                            </small>
                        </div>
                        <span class="badge bg-<?php echo e($user->is_active ? 'success' : 'danger'); ?>">
                            <?php echo e($user->is_active ? 'Aktif' : 'Nonaktif'); ?>

                        </span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-muted text-center">Belum ada pengguna terbaru</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-newspaper me-2"></i>Berita Terbaru
            </div>
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $recentNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="d-flex align-items-start mb-3">
                        <?php if($news->featured_image): ?>
                            <img src="<?php echo e(asset('storage/' . $news->featured_image)); ?>" alt="News" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="rounded bg-gradient-primary d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-newspaper text-white"></i>
                            </div>
                        <?php endif; ?>
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo e(Str::limit($news->title, 50)); ?></h6>
                            <small class="text-muted">
                                <?php echo e($news->created_at->diffForHumans()); ?>

                            </small>
                            <br>
                            <span class="badge bg-<?php echo e($news->status === 'published' ? 'success' : 'warning'); ?>">
                                <?php echo e($news->status === 'published' ? 'Published' : ucfirst($news->status)); ?>

                            </span>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-muted text-center">Belum ada berita terbaru</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>Aksi Cepat
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus me-2"></i>Tambah User
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-newspaper me-2"></i>Buat Berita
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-graduation-cap me-2"></i>Tambah Program
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-danger w-100">
                            <i class="fas fa-cog me-2"></i>Pengaturan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/dashboard/superadmin.blade.php ENDPATH**/ ?>