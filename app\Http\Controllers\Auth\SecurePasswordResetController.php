<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\SecurityLog;
use App\Rules\StrongPassword;
use App\Mail\PasswordResetMail;

class SecurePasswordResetController extends Controller
{
    /**
     * Show the password reset request form
     */
    public function showLinkRequestForm()
    {
        return view('auth.passwords.email');
    }

    /**
     * Send password reset link
     */
    public function sendResetLinkEmail(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email', 'exists:users,email'],
        ], [
            'email.required' => 'Email harus diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.exists' => 'Email tidak terdaftar dalam sistem.',
        ]);

        $user = User::where('email', $request->email)->first();

        // Check if user account is locked
        if ($user->isAccountLocked()) {
            SecurityLog::logEvent('suspicious_activity', $user->id, [
                'type' => 'password_reset_attempt_locked_account',
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'email' => 'Akun Anda terkunci. Silakan hubungi administrator.',
            ]);
        }

        // Check rate limiting for password reset
        if (!$user->canRequestPasswordReset()) {
            SecurityLog::logEvent('suspicious_activity', $user->id, [
                'type' => 'password_reset_rate_limit_exceeded',
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'email' => 'Anda sudah meminta reset password. Silakan tunggu 15 menit sebelum mencoba lagi.',
            ]);
        }

        // Generate secure reset token
        $token = $user->generatePasswordResetToken();

        // Send password reset email
        try {
            Mail::to($user->email)->send(new PasswordResetMail($user, $token));

            SecurityLog::logEvent('password_reset', $user->id, [
                'type' => 'password_reset_link_sent',
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->with('status', 'Link reset password telah dikirim ke email Anda.');
        } catch (\Exception $e) {
            \Log::error('Failed to send password reset email: ' . $e->getMessage());

            SecurityLog::logEvent('suspicious_activity', $user->id, [
                'type' => 'password_reset_email_failed',
                'email' => $request->email,
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'email' => 'Gagal mengirim email reset password. Silakan coba lagi.',
            ]);
        }
    }

    /**
     * Show the password reset form
     */
    public function showResetForm(Request $request, $token)
    {
        return view('auth.passwords.reset', [
            'token' => $token,
            'email' => $request->email,
        ]);
    }

    /**
     * Reset the password
     */
    public function reset(Request $request)
    {
        $request->validate([
            'token' => ['required', 'string'],
            'email' => ['required', 'email', 'exists:users,email'],
            'password' => ['required', 'string', 'confirmed', new StrongPassword()],
            'password_confirmation' => ['required', 'string'],
        ], [
            'token.required' => 'Token reset password tidak valid.',
            'email.required' => 'Email harus diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.exists' => 'Email tidak terdaftar dalam sistem.',
            'password.required' => 'Password baru harus diisi.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'password_confirmation.required' => 'Konfirmasi password harus diisi.',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            SecurityLog::logEvent('suspicious_activity', null, [
                'type' => 'password_reset_invalid_email',
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'email' => 'Email tidak terdaftar dalam sistem.',
            ]);
        }

        // Verify the reset token
        if (!$user->verifyPasswordResetToken($request->token)) {
            SecurityLog::logEvent('suspicious_activity', $user->id, [
                'type' => 'password_reset_invalid_token',
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'token' => 'Token reset password tidak valid atau sudah kedaluwarsa.',
            ]);
        }

        // Check if new password is different from current password
        if (Hash::check($request->password, $user->password)) {
            return back()->withErrors([
                'password' => 'Password baru harus berbeda dari password lama.',
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
            'password_reset_token' => null,
            'password_reset_sent_at' => null,
            'password_reset_expires_at' => null,
            'password_changed_at' => now(),
        ]);

        // Clear all sessions for this user (force re-login)
        $user->clearAllSessions();

        SecurityLog::logEvent('password_reset', $user->id, [
            'type' => 'password_reset_success',
            'email' => $request->email,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return redirect()->route('login')->with('status', 'Password berhasil direset. Silakan login dengan password baru Anda.');
    }
}
