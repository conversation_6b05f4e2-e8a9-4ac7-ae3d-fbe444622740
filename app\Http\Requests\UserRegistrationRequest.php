<?php

namespace App\Http\Requests;

use App\Rules\StrongPassword;
use Illuminate\Validation\Rule;

class UserRegistrationRequest extends SecureFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $commonRules = $this->getCommonRules();
        
        return [
            'name' => $commonRules['name'],
            'email' => array_merge($commonRules['email'], ['unique:users,email']),
            'username' => array_merge($commonRules['username'], ['unique:users,username']),
            'password' => ['required', 'string', 'confirmed', new StrongPassword()],
            'password_confirmation' => ['required', 'string'],
            'user_type' => ['required', 'string', 'in:super_admin,admin,guru,siswa,orang_tua'],
            'phone' => $commonRules['phone'],
            'address' => $commonRules['address'],
            'birth_date' => $commonRules['birth_date'],
            'gender' => $commonRules['gender'],
            'nisn' => array_merge($commonRules['nisn'], ['unique:users,nisn']),
            'nip' => array_merge($commonRules['nip'], ['unique:users,nip']),
            'avatar' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif',
                'max:2048', // 2MB
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Check file extension
                        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
                        $extension = strtolower($value->getClientOriginalExtension());
                        
                        if (!in_array($extension, $allowedExtensions)) {
                            $fail('File harus berupa gambar dengan format: jpg, jpeg, png, gif.');
                        }
                        
                        // Check MIME type
                        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif'];
                        if (!in_array($value->getMimeType(), $allowedMimes)) {
                            $fail('File harus berupa gambar yang valid.');
                        }
                        
                        // Check file size (additional check)
                        if ($value->getSize() > 2097152) { // 2MB in bytes
                            $fail('Ukuran file maksimal 2MB.');
                        }
                    }
                }
            ],
            'is_active' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'user_type.in' => 'Tipe pengguna yang dipilih tidak valid.',
            'avatar.image' => 'File harus berupa gambar.',
            'avatar.mimes' => 'Gambar harus berformat: jpeg, png, jpg, gif.',
            'avatar.max' => 'Ukuran gambar maksimal 2MB.',
        ]);
    }

    /**
     * Configure validation rules based on user type
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $userType = $this->input('user_type');
            
            // Validate NISN for students
            if ($userType === 'siswa' && !$this->input('nisn')) {
                $validator->errors()->add('nisn', 'NISN wajib diisi untuk siswa.');
            }
            
            // Validate NIP for teachers/staff
            if (in_array($userType, ['guru', 'admin']) && !$this->input('nip')) {
                $validator->errors()->add('nip', 'NIP wajib diisi untuk guru/admin.');
            }
            
            // Validate username for admin/staff
            if (in_array($userType, ['super_admin', 'admin', 'guru']) && !$this->input('username')) {
                $validator->errors()->add('username', 'Username wajib diisi untuk admin/guru.');
            }
            
            // Check for suspicious patterns in all text fields
            $textFields = ['name', 'username', 'address'];
            foreach ($textFields as $field) {
                $value = $this->input($field);
                if ($value) {
                    if ($this->hasSqlInjection($value)) {
                        $validator->errors()->add($field, 'Input mengandung karakter yang tidak diizinkan.');
                    }
                    if ($this->hasXss($value)) {
                        $validator->errors()->add($field, 'Input mengandung karakter yang tidak diizinkan.');
                    }
                    if ($this->hasCommandInjection($value)) {
                        $validator->errors()->add($field, 'Input mengandung karakter yang tidak diizinkan.');
                    }
                }
            }
        });
    }
}
