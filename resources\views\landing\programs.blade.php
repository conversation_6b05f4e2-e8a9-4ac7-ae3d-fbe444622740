@extends('layouts.landing')

@section('title', 'Program Pendidikan')

@section('content')
<!-- Hero Section -->
<section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 120px 0 80px;">
    <div class="container">
        <div class="row justify-content-center text-center text-white">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4" data-aos="fade-up">Program Pendidikan</h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Berbagai program unggulan yang dirancang untuk mengembangkan potensi siswa secara optimal
                </p>
                <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="200">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}" class="text-white-50">Beranda</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Program</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Programs Grid Section -->
<section class="section">
    <div class="container">
        <div class="row g-4">
            @forelse($programs as $program)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    <div class="card program-card h-100">
                        @if($program->image)
                            <div class="program-image">
                                <img src="{{ asset('storage/' . $program->image) }}" class="card-img-top" alt="{{ $program->name }}">
                                <div class="program-overlay">
                                    <div class="program-overlay-content">
                                        <h4 class="text-white mb-2">{{ $program->name }}</h4>
                                        <p class="text-white-50 mb-3">{{ Str::limit($program->description, 100) }}</p>
                                        <a href="{{ route('landing.program', $program->slug) }}" class="btn btn-light btn-sm">
                                            <i class="fas fa-eye me-1"></i>Lihat Detail
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="program-image placeholder-container placeholder-program">
                                <div class="text-center">
                                    <i class="fas fa-graduation-cap fa-3x placeholder-icon"></i>
                                    <div class="mt-2">{{ $program->name }}</div>
                                </div>
                                <div class="program-overlay">
                                    <div class="program-overlay-content">
                                        <h4 class="text-white mb-2">{{ $program->name }}</h4>
                                        <p class="text-white-50 mb-3">{{ Str::limit($program->description, 100) }}</p>
                                        <a href="{{ route('landing.program', $program->slug) }}" class="btn btn-light btn-sm">
                                            <i class="fas fa-eye me-1"></i>Lihat Detail
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        <div class="card-body">
                            <div class="program-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <h5 class="card-title text-dark">{{ $program->name }}</h5>
                            <p class="card-text text-dark">{{ Str::limit($program->description, 120) }}</p>
                            
                            <div class="program-details mb-3">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="detail-item">
                                            <i class="fas fa-layer-group text-primary"></i>
                                            <small class="d-block text-muted">Tingkat</small>
                                            <strong>{{ ucfirst($program->level) }}</strong>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="detail-item">
                                            <i class="fas fa-clock text-primary"></i>
                                            <small class="d-block text-muted">Durasi</small>
                                            <strong>{{ $program->duration ?? '3 Tahun' }}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="program-status">
                                    @if($program->is_active)
                                        <span class="badge bg-success"><i class="fas fa-check me-1"></i>Aktif</span>
                                    @else
                                        <span class="badge bg-secondary"><i class="fas fa-pause me-1"></i>Tidak Aktif</span>
                                    @endif
                                    <span class="badge bg-primary ms-1">{{ ucfirst($program->level) }}</span>
                                </div>
                                <a href="{{ route('landing.program', $program->slug) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>Detail
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-graduation-cap fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Belum Ada Program</h4>
                        <p class="text-muted">Program pendidikan akan segera diumumkan.</p>
                        <a href="{{ route('landing') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Kembali ke Beranda
                        </a>
                    </div>
                </div>
            @endforelse
        </div>
        
        <!-- Pagination -->
        @if($programs->hasPages())
            <div class="d-flex justify-content-center mt-5">
                {{ $programs->links() }}
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="section bg-light">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h3 class="mb-4">Tertarik dengan Program Kami?</h3>
                <p class="text-muted mb-4">Bergabunglah dengan program pendidikan unggulan kami dan wujudkan masa depan yang cerah.</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('landing') }}#ppdb" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Daftar PPDB
                    </a>
                    <a href="#contact" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>Hubungi Kami
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.program-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.program-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.program-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.program-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.program-card:hover .program-image img {
    transform: scale(1.05);
}

.program-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.program-card:hover .program-overlay {
    opacity: 1;
}

.program-overlay-content {
    text-align: center;
    padding: 2rem;
}

.program-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.program-details .detail-item {
    padding: 0.5rem;
}

.program-details .detail-item i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.placeholder-container.placeholder-program {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 250px;
}

.placeholder-program .placeholder-icon {
    opacity: 0.7;
}

/* Responsive */
@media (max-width: 768px) {
    .program-image {
        height: 200px;
    }
    
    .program-overlay-content {
        padding: 1rem;
    }
}
</style>
@endpush
