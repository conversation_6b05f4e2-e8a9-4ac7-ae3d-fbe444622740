@extends('layouts.dashboard')

@section('title', 'Detail Berita')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Detail Berita</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.content.news.index') }}">Berita & Pengumuman</a></li>
                <li class="breadcrumb-item active">{{ $news->title }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ route('admin.content.news.edit', $news) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-2"></i>Edit
        </a>
        <a href="{{ route('admin.content.news.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i><PERSON><PERSON><PERSON>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Main Content -->
        <div class="card">
            <div class="card-body">
                <!-- Featured Image -->
                @if($news->featured_image)
                    <div class="mb-4" id="featured-image-section">
                        <div class="position-relative d-inline-block">
                            <img src="{{ Storage::url($news->featured_image) }}"
                                 alt="{{ $news->title }}"
                                 class="img-fluid rounded">
                            <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-2 remove-featured-image"
                                    data-news-id="{{ $news->id }}" title="Hapus Gambar">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                @endif

                <!-- Title -->
                <h1 class="mb-3">{{ $news->title }}</h1>

                <!-- Meta Info -->
                <div class="d-flex flex-wrap align-items-center mb-4 text-muted">
                    <div class="me-4">
                        <i class="fas fa-user me-1"></i>
                        {{ $news->author->name ?? 'Unknown' }}
                    </div>
                    <div class="me-4">
                        <i class="fas fa-calendar me-1"></i>
                        {{ $news->created_at->format('d F Y') }}
                    </div>
                    <div class="me-4">
                        <i class="fas fa-clock me-1"></i>
                        {{ $news->created_at->format('H:i') }}
                    </div>
                    @if(isset($news->category) && $news->category)
                        <div class="me-4">
                            <i class="fas fa-tag me-1"></i>
                            <span class="badge bg-primary">{{ $news->category }}</span>
                        </div>
                    @endif
                </div>

                <!-- Excerpt -->
                @if($news->excerpt)
                    <div class="alert alert-info">
                        <strong>Ringkasan:</strong> {{ $news->excerpt }}
                    </div>
                @endif

                <!-- Content -->
                <div class="content">
                    {!! $news->content !!}
                </div>

                <!-- Tags -->
                @if(isset($news->tags) && $news->tags)
                    <div class="mt-4">
                        <h6>Tags:</h6>
                        @foreach(explode(',', $news->tags) as $tag)
                            <span class="badge bg-secondary me-1">{{ trim($tag) }}</span>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>

        <!-- Comments Section (if needed) -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Statistik</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="stat-item">
                            <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                            <h4>{{ $news->views ?? 0 }}</h4>
                            <p class="text-muted">Views</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <i class="fas fa-share fa-2x text-success mb-2"></i>
                            <h4>{{ $news->shares ?? 0 }}</h4>
                            <p class="text-muted">Shares</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <i class="fas fa-heart fa-2x text-danger mb-2"></i>
                            <h4>{{ $news->likes ?? 0 }}</h4>
                            <p class="text-muted">Likes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- News Info -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Berita</h5>
            </div>
            <div class="card-body">
                <div class="info-item mb-3">
                    <label class="form-label text-muted">Status</label>
                    <p>
                        <span class="badge bg-{{ $news->status == 'published' ? 'success' : ($news->status == 'draft' ? 'warning' : 'secondary') }} fs-6">
                            {{ ucfirst($news->status) }}
                        </span>
                    </p>
                </div>

                <div class="info-item mb-3">
                    <label class="form-label text-muted">Tipe</label>
                    <p>
                        <span class="badge bg-{{ $news->type == 'news' ? 'info' : 'primary' }} fs-6">
                            {{ $news->type == 'news' ? 'Berita' : 'Pengumuman' }}
                        </span>
                    </p>
                </div>

                @if($news->is_featured)
                    <div class="info-item mb-3">
                        <label class="form-label text-muted">Featured</label>
                        <p>
                            <span class="badge bg-warning fs-6">
                                <i class="fas fa-star me-1"></i>Featured
                            </span>
                        </p>
                    </div>
                @endif

                <div class="info-item mb-3">
                    <label class="form-label text-muted">Slug</label>
                    <p class="font-monospace">{{ $news->slug }}</p>
                </div>

                @if($news->published_at)
                    <div class="info-item mb-3">
                        <label class="form-label text-muted">Tanggal Publikasi</label>
                        <p>{{ $news->published_at->format('d F Y H:i') }}</p>
                    </div>
                @endif

                <div class="info-item mb-3">
                    <label class="form-label text-muted">Dibuat</label>
                    <p>{{ $news->created_at->format('d F Y H:i') }}</p>
                </div>

                <div class="info-item mb-3">
                    <label class="form-label text-muted">Diupdate</label>
                    <p>{{ $news->updated_at->format('d F Y H:i') }}</p>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Aksi</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.content.news.edit', $news) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Berita
                    </a>
                    
                    @if($news->status == 'published')
                        <a href="{{ route('landing.news', $news->slug) }}" class="btn btn-outline-success" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Lihat di Website
                        </a>
                    @endif

                    <button type="button" class="btn btn-outline-danger delete-news" 
                            data-news-id="{{ $news->id }}" 
                            data-news-title="{{ $news->title }}">
                        <i class="fas fa-trash me-2"></i>Hapus Berita
                    </button>
                </div>
            </div>
        </div>

        <!-- SEO Info -->
        @if((isset($news->meta_title) && $news->meta_title) || (isset($news->meta_description) && $news->meta_description))
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">SEO Information</h5>
            </div>
            <div class="card-body">
                @if(isset($news->meta_title) && $news->meta_title)
                    <div class="info-item mb-3">
                        <label class="form-label text-muted">Meta Title</label>
                        <p>{{ $news->meta_title }}</p>
                    </div>
                @endif

                @if(isset($news->meta_description) && $news->meta_description)
                    <div class="info-item mb-3">
                        <label class="form-label text-muted">Meta Description</label>
                        <p>{{ $news->meta_description }}</p>
                    </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>


@endsection

@push('styles')
<style>
.content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
}

.content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.stat-item {
    padding: 1rem;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.info-item label {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin-bottom: 0;
}

.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.remove-featured-image {
    border-radius: 50%;
    width: 35px;
    height: 35px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.remove-featured-image:hover {
    opacity: 1;
}

#featured-image-section:hover .remove-featured-image {
    opacity: 1;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Delete news with AJAX
    $('.delete-news').click(function() {
        const newsId = $(this).data('news-id');
        const newsTitle = $(this).data('news-title');

        console.log('Delete News Request:', {
            newsId: newsId,
            newsTitle: newsTitle
        });

        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: `Apakah Anda yakin ingin menghapus berita "${newsTitle}"?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('Making AJAX DELETE request to:', `/admin/content/news/${newsId}`);

                // Show loading
                Swal.fire({
                    title: 'Menghapus Berita...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: `/admin/content/news/${newsId}`,
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        console.log('Delete News Success Response:', response);

                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Berita berhasil dihapus.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            // Redirect to news index
                            window.location.href = '/admin/content/news';
                        });
                    },
                    error: function(xhr, status, error) {
                        console.log('Delete News Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat menghapus berita.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Remove featured image
    $('.remove-featured-image').click(function() {
        const newsId = $(this).data('news-id');

        Swal.fire({
            title: 'Konfirmasi Hapus Gambar',
            text: 'Apakah Anda yakin ingin menghapus gambar utama ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'Menghapus Gambar...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: `/admin/content/news/${newsId}/remove-image`,
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });

                            // Remove the featured image section
                            $('#featured-image-section').fadeOut(300, function() {
                                $(this).remove();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        let message = 'Terjadi kesalahan saat menghapus gambar.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
});
</script>
@endpush
