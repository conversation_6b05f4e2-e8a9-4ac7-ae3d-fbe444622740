<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CleanupSecurityTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:cleanup-tokens';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired security tokens and sessions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Cleaning up expired security tokens...');

        try {
            $deletedCount = 0;

            // Clean up expired password reset tokens
            $expiredPasswordResets = DB::table('password_reset_tokens')
                ->where('created_at', '<', Carbon::now()->subHours(1))
                ->count();
            
            if ($expiredPasswordResets > 0) {
                DB::table('password_reset_tokens')
                    ->where('created_at', '<', Carbon::now()->subHours(1))
                    ->delete();
                $deletedCount += $expiredPasswordResets;
                $this->line("Deleted {$expiredPasswordResets} expired password reset tokens");
            }

            // Clean up old sessions
            $expiredSessions = DB::table('sessions')
                ->where('last_activity', '<', Carbon::now()->subDays(7)->timestamp)
                ->count();
            
            if ($expiredSessions > 0) {
                DB::table('sessions')
                    ->where('last_activity', '<', Carbon::now()->subDays(7)->timestamp)
                    ->delete();
                $deletedCount += $expiredSessions;
                $this->line("Deleted {$expiredSessions} expired sessions");
            }

            // Clean up old failed jobs
            if (DB::getSchemaBuilder()->hasTable('failed_jobs')) {
                $oldFailedJobs = DB::table('failed_jobs')
                    ->where('failed_at', '<', Carbon::now()->subDays(30))
                    ->count();
                
                if ($oldFailedJobs > 0) {
                    DB::table('failed_jobs')
                        ->where('failed_at', '<', Carbon::now()->subDays(30))
                        ->delete();
                    $deletedCount += $oldFailedJobs;
                    $this->line("Deleted {$oldFailedJobs} old failed jobs");
                }
            }

            // Clean up old job batches
            if (DB::getSchemaBuilder()->hasTable('job_batches')) {
                $oldJobBatches = DB::table('job_batches')
                    ->where('created_at', '<', Carbon::now()->subDays(7))
                    ->count();
                
                if ($oldJobBatches > 0) {
                    DB::table('job_batches')
                        ->where('created_at', '<', Carbon::now()->subDays(7))
                        ->delete();
                    $deletedCount += $oldJobBatches;
                    $this->line("Deleted {$oldJobBatches} old job batches");
                }
            }

            if ($deletedCount === 0) {
                $this->info('No expired tokens found to clean up.');
            } else {
                $this->info("Successfully cleaned up {$deletedCount} expired tokens and records.");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('Token cleanup failed: ' . $e->getMessage());
            return 1;
        }
    }
}
