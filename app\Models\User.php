<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Str;
use Carbon\Carbon;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'username',
        'nisn',
        'student_verification_code',
        'nip',
        'parent_id',
        'user_type',
        'phone',
        'address',
        'birth_date',
        'gender',
        'avatar',
        'password',
        'is_active',
        'last_login_at',
        'last_login_ip',
        'email_verified_at',
        'email_verification_token',
        'email_verification_sent_at',
        'email_verification_expires_at',
        'password_reset_token',
        'password_reset_sent_at',
        'password_reset_expires_at',
        'password_changed_at',
        'previous_user_type',
        'role_changed_at',
        'role_changed_by',
        'role_change_reason',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'failed_login_attempts',
        'locked_until',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'birth_date' => 'date',
            'is_active' => 'boolean',
            'locked_until' => 'datetime',
            'last_login_at' => 'datetime',
            'email_verification_sent_at' => 'datetime',
            'email_verification_expires_at' => 'datetime',
            'password_reset_sent_at' => 'datetime',
            'password_reset_expires_at' => 'datetime',
            'password_changed_at' => 'datetime',
            'role_changed_at' => 'datetime',
        ];
    }

    /**
     * Role Management Methods
     */
    public function hasUserType($userType)
    {
        return $this->user_type === $userType;
    }

    public function hasAnyUserType($userTypes)
    {
        if (is_string($userTypes)) {
            $userTypes = [$userTypes];
        }

        return in_array($this->user_type, $userTypes);
    }

    public function isSuperAdmin()
    {
        return $this->user_type === 'super_admin';
    }

    public function isAdmin()
    {
        return in_array($this->user_type, ['super_admin', 'admin']);
    }

    /**
     * Check if account has failed login attempts
     */
    public function hasFailedAttempts()
    {
        return $this->failed_login_attempts > 0;
    }

    /**
     * Get lock status text
     */
    public function getLockStatusAttribute()
    {
        if ($this->isLocked()) {
            return 'Terkunci hingga ' . $this->locked_until->format('d M Y H:i');
        }

        if ($this->hasFailedAttempts()) {
            return $this->failed_login_attempts . ' percobaan gagal';
        }

        return 'Normal';
    }

    /**
     * Get avatar URL or default placeholder
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar && Storage::disk('public')->exists($this->avatar)) {
            return Storage::url($this->avatar);
        }

        // Return default avatar or placeholder
        return null;
    }

    /**
     * Get avatar initials for placeholder
     */
    public function getAvatarInitialsAttribute()
    {
        $words = explode(' ', $this->name);
        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        }
        return strtoupper(substr($this->name, 0, 2));
    }

    /**
     * Email Verification Methods
     */
    public function generateEmailVerificationToken(): string
    {
        // Generate cryptographically secure token
        $token = bin2hex(random_bytes(32)); // 64 character hex string

        // Hash the token before storing (similar to password hashing)
        $hashedToken = hash('sha256', $token);

        $this->update([
            'email_verification_token' => $hashedToken,
            'email_verification_sent_at' => now(),
            'email_verification_expires_at' => now()->addHours(2), // Reduced to 2 hours for security
        ]);

        // Return the plain token for email link
        return $token;
    }

    public function verifyEmail(string $token): bool
    {
        // Hash the provided token to compare with stored hash
        $hashedToken = hash('sha256', $token);

        \Log::info('=== EMAIL VERIFICATION DEBUG ===');
        \Log::info('User ID: ' . $this->id);
        \Log::info('User Email: ' . $this->email);
        \Log::info('Stored Token Hash: ' . substr($this->email_verification_token, 0, 10) . '...');
        \Log::info('Provided Token Hash: ' . substr($hashedToken, 0, 10) . '...');
        \Log::info('Token Match: ' . ($this->email_verification_token === $hashedToken ? 'YES' : 'NO'));
        \Log::info('Expires At: ' . $this->email_verification_expires_at);
        \Log::info('Is Expired: ' . ($this->email_verification_expires_at && $this->email_verification_expires_at->isPast() ? 'YES' : 'NO'));

        // Check if token is valid (constant time comparison)
        if (!hash_equals($this->email_verification_token, $hashedToken)) {
            \Log::warning('Token mismatch - verification failed');

            // Log security event
            \App\Models\SecurityLog::logEvent('suspicious_activity', $this->id, [
                'type' => 'invalid_email_verification_token',
                'email' => $this->email,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return false;
        }

        // Check if token is expired
        if ($this->email_verification_expires_at && $this->email_verification_expires_at->isPast()) {
            \Log::warning('Token expired - verification failed');

            // Log security event
            \App\Models\SecurityLog::logEvent('suspicious_activity', $this->id, [
                'type' => 'expired_email_verification_token',
                'email' => $this->email,
                'expired_at' => $this->email_verification_expires_at,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return false;
        }

        \Log::info('Updating user verification status...');
        $result = $this->update([
            'email_verified_at' => now(),
            'email_verification_token' => null,
            'email_verification_sent_at' => null,
            'email_verification_expires_at' => null,
        ]);

        // Log successful verification
        \App\Models\SecurityLog::logEvent('login_success', $this->id, [
            'type' => 'email_verification_success',
            'email' => $this->email,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        \Log::info('Update result: ' . ($result ? 'SUCCESS' : 'FAILED'));
        \Log::info('=== EMAIL VERIFICATION DEBUG END ===');

        return true;
    }

    public function isEmailVerificationExpired(): bool
    {
        return $this->email_verification_expires_at && $this->email_verification_expires_at->isPast();
    }

    public function canResendEmailVerification(): bool
    {
        if (!$this->email_verification_sent_at) {
            return true;
        }

        // Allow resend after 5 minutes
        return $this->email_verification_sent_at->addMinutes(5)->isPast();
    }

    /**
     * Role Management Methods
     */
    public function changeRole(string $newRole, User $changedBy, string $reason = null): bool
    {
        $validRoles = ['super_admin', 'admin', 'guru', 'siswa', 'orang_tua', 'calon_siswa'];

        if (!in_array($newRole, $validRoles)) {
            return false;
        }

        $this->update([
            'previous_user_type' => $this->user_type,
            'user_type' => $newRole,
            'role_changed_at' => now(),
            'role_changed_by' => $changedBy->id,
            'role_change_reason' => $reason,
        ]);

        return true;
    }

    public function rollbackRole(): bool
    {
        if (!$this->previous_user_type) {
            return false;
        }

        $this->update([
            'user_type' => $this->previous_user_type,
            'previous_user_type' => null,
            'role_changed_at' => now(),
            'role_change_reason' => 'Role rollback',
        ]);

        return true;
    }

    public function canBeDeleted(): bool
    {
        // Calon siswa yang tidak diterima bisa dihapus setelah 1 tahun
        if ($this->user_type === 'calon_siswa') {
            $ppdbRegistration = $this->ppdbRegistration;
            if ($ppdbRegistration && $ppdbRegistration->status === 'rejected') {
                return $ppdbRegistration->created_at->addYear()->isPast();
            }
            // Jika pending lebih dari 6 bulan
            if ($ppdbRegistration && $ppdbRegistration->status === 'pending') {
                return $ppdbRegistration->created_at->addMonths(6)->isPast();
            }
        }

        return false;
    }

    /**
     * Security Methods
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    public function lockAccount(int $minutes = 30): void
    {
        $this->update([
            'locked_until' => Carbon::now()->addMinutes($minutes),
            'failed_login_attempts' => $this->failed_login_attempts + 1,
        ]);
    }

    public function unlockAccount(): void
    {
        $this->update([
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);
    }

    public function incrementFailedAttempts(): void
    {
        $this->increment('failed_login_attempts');

        if ($this->failed_login_attempts >= 3) {
            $this->lockAccount();
        }
    }

    public function resetFailedAttempts(): void
    {
        $this->update(['failed_login_attempts' => 0]);
    }

    /**
     * Login identifier methods
     */
    public function getLoginIdentifier(): string
    {
        switch ($this->user_type) {
            case 'siswa':
                return $this->nisn ?? $this->email;
            case 'guru':
            case 'admin':
                return $this->nip ?? $this->username ?? $this->email;
            case 'orang_tua':
                return $this->parent_id ?? $this->email;
            default:
                return $this->email;
        }
    }

    /**
     * Relationships
     */
    public function staffProfile()
    {
        return $this->hasOne(StaffProfile::class);
    }

    public function studentProfile()
    {
        return $this->hasOne(StudentProfile::class);
    }

    public function parentProfile()
    {
        return $this->hasOne(ParentProfile::class);
    }

    public function admissions()
    {
        return $this->hasMany(Admission::class);
    }

    public function galleries()
    {
        return $this->hasMany(Gallery::class, 'uploaded_by');
    }

    public function news()
    {
        return $this->hasMany(News::class, 'author_id');
    }

    public function securityLogs()
    {
        return $this->hasMany(SecurityLog::class);
    }

    public function ppdbRegistration()
    {
        return $this->hasOne(\App\Models\PPDBRegistration::class);
    }

    public function roleChanger()
    {
        return $this->belongsTo(User::class, 'role_changed_by');
    }

    public function parentRegistrations()
    {
        return $this->hasMany(\App\Models\ParentRegistration::class, 'student_id');
    }

    /**
     * Student Verification Code Methods
     */
    public function generateStudentVerificationCode(): string
    {
        if ($this->user_type !== 'siswa') {
            throw new \Exception('Only students can have verification codes');
        }

        $code = strtoupper(Str::random(6) . substr($this->nisn ?? $this->id, -4));

        $this->update(['student_verification_code' => $code]);

        return $code;
    }

    public function getStudentVerificationCode(): ?string
    {
        if ($this->user_type !== 'siswa') {
            return null;
        }

        if (!$this->student_verification_code) {
            return $this->generateStudentVerificationCode();
        }

        return $this->student_verification_code;
    }

    /**
     * Password Reset Methods
     */
    public function generatePasswordResetToken(): string
    {
        // Generate cryptographically secure token
        $token = bin2hex(random_bytes(32)); // 64 character hex string

        // Hash the token before storing
        $hashedToken = hash('sha256', $token);

        $this->update([
            'password_reset_token' => $hashedToken,
            'password_reset_sent_at' => now(),
            'password_reset_expires_at' => now()->addMinutes(30), // 30 minutes expiry
        ]);

        // Return the plain token for email link
        return $token;
    }

    public function verifyPasswordResetToken(string $token): bool
    {
        // Hash the provided token to compare with stored hash
        $hashedToken = hash('sha256', $token);

        // Check if token is valid (constant time comparison)
        if (!hash_equals($this->password_reset_token, $hashedToken)) {
            return false;
        }

        // Check if token is expired
        if ($this->password_reset_expires_at && $this->password_reset_expires_at->isPast()) {
            return false;
        }

        return true;
    }

    public function canRequestPasswordReset(): bool
    {
        if (!$this->password_reset_sent_at) {
            return true;
        }

        // Allow new request after 15 minutes
        return $this->password_reset_sent_at->addMinutes(15)->isPast();
    }

    public function clearAllSessions(): void
    {
        // This would require implementing session management
        // For now, we'll just log the action
        SecurityLog::logEvent('login_success', $this->id, [
            'type' => 'all_sessions_cleared',
            'reason' => 'password_reset',
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}
