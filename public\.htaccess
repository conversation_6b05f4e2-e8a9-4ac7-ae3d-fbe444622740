<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    # Security Headers
    <IfModule mod_headers.c>
        # Remove server information
        Header unset Server
        Header unset X-Powered-By

        # Prevent clickjacking
        Header always set X-Frame-Options "DENY"

        # Prevent MIME sniffing
        Header always set X-Content-Type-Options "nosniff"

        # Enable XSS protection
        Header always set X-XSS-Protection "1; mode=block"

        # Referrer Policy
        Header always set Referrer-Policy "strict-origin-when-cross-origin"

        # Force HTTPS (uncomment when using SSL)
        # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

        # Prevent directory browsing
        Options -Indexes
    </IfModule>

    RewriteEngine On

    # Block access to sensitive files
    RewriteRule ^\.env$ - [F,L]
    RewriteRule ^\.git - [F,L]
    RewriteRule ^composer\.(json|lock)$ - [F,L]
    RewriteRule ^package(-lock)?\.json$ - [F,L]
    RewriteRule ^artisan$ - [F,L]
    RewriteRule ^storage/logs/ - [F,L]

    # Block access to common attack files
    RewriteRule \.(sql|bak|backup|log|old|orig|save|swo|swp|tmp)$ - [F,L]
    RewriteRule ^(wp-|wordpress|admin|phpmyadmin|pma) - [F,L]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle X-XSRF-Token Header
    RewriteCond %{HTTP:x-xsrf-token} .
    RewriteRule .* - [E=HTTP_X_XSRF_TOKEN:%{HTTP:X-XSRF-Token}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
