<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('staff_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('employee_id')->unique()->nullable();
            $table->enum('position', ['kepala_sekolah', 'wakil_kepala', 'guru', 'staff_tu', 'konselor', 'pustakawan', 'satpam', 'cleaning_service'])->default('guru');
            $table->string('subject')->nullable(); // For teachers
            $table->text('qualifications')->nullable();
            $table->text('experience')->nullable();
            $table->json('certifications')->nullable(); // Store certifications as JSON
            $table->date('join_date')->nullable();
            $table->enum('employment_status', ['tetap', 'kontrak', 'honorer'])->default('tetap');
            $table->decimal('salary', 15, 2)->nullable();
            $table->text('bio')->nullable();
            $table->json('achievements')->nullable(); // Store achievements as JSON
            $table->boolean('is_featured')->default(false);
            $table->integer('order')->default(0);
            $table->timestamps();

            $table->index(['position', 'is_featured']);
            $table->index('employment_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('staff_profiles');
    }
};
