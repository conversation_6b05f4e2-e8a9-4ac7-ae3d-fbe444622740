<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PPDBSetting;
use App\Models\PPDBRegistration;
use App\Models\Program;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Mail\PPDBWelcomeMail;
use App\Mail\EmailVerificationMail;

class PPDBController extends Controller
{
    public function index()
    {
        $ppdbSetting = PPDBSetting::getActive();

        if (!$ppdbSetting) {
            return view('ppdb.closed', [
                'message' => 'PPDB belum dibuka untuk tahun ajaran ini.'
            ]);
        }

        // Always show the main PPDB page with information, regardless of status
        // The page will handle different states (open, closed, not started, ended)
        $programs = Program::where('is_active', true)->get();

        return view('ppdb.index', compact('ppdbSetting', 'programs'));
    }

    public function register(Request $request)
    {
        try {
            \Log::info('=== PPDB REGISTRATION START ===');
            \Log::info('Request method: ' . $request->method());
            \Log::info('Request is AJAX: ' . ($request->ajax() ? 'true' : 'false'));
            \Log::info('Request wants JSON: ' . ($request->wantsJson() ? 'true' : 'false'));
            \Log::info('Request URL: ' . $request->url());
            \Log::info('Request data: ' . json_encode($request->except(['_token'])));

            $ppdbSetting = PPDBSetting::getActive();

        if (!$ppdbSetting || !$ppdbSetting->isOpen()) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pendaftaran PPDB sedang tidak tersedia.'
                ], 400);
            }
            return redirect()->route('ppdb.index')
                ->with('error', 'Pendaftaran PPDB sedang tidak tersedia.');
        }

        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'nik' => 'required|string|size:16|unique:ppdb_registrations,nik',
            'email' => 'required|email|unique:users,email',
            'phone' => 'required|string|max:20',
            'gender' => 'required|in:L,P',
            'birth_date' => 'required|date|before:today',
            'birth_place' => 'required|string|max:255',
            'religion' => 'required|string|max:50',
            'address' => 'required|string',
            'program_id' => 'required|exists:programs,id',
            'father_name' => 'required|string|max:255',
            'mother_name' => 'required|string|max:255',
            'previous_school' => 'required|string|max:255',
            'final_grade' => 'nullable|numeric|min:0|max:100',
        ], [
            'nik.unique' => 'NIK sudah terdaftar dalam sistem.',
            'email.unique' => 'Email sudah terdaftar dalam sistem.',
            'birth_date.before' => 'Tanggal lahir harus sebelum hari ini.',
        ]);

        // Convert gender from L/P to male/female for database storage
        $genderMapping = [
            'L' => 'male',
            'P' => 'female'
        ];
        $convertedGender = $genderMapping[$request->gender] ?? $request->gender;

        if ($validator->fails()) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Generate temporary password
            $temporaryPassword = Str::random(8);
            \Log::info('Generated temporary password: ' . $temporaryPassword);

            // Create user account
            $user = User::create([
                'name' => $request->full_name,
                'email' => $request->email,
                'user_type' => 'calon_siswa',
                'phone' => $request->phone,
                'gender' => $convertedGender,
                'birth_date' => $request->birth_date,
                'password' => Hash::make($temporaryPassword),
                'is_active' => true,
            ]);

            $user->assignRole('calon_siswa');

            // Create PPDB registration
            $registration = PPDBRegistration::create([
                'registration_number' => PPDBRegistration::generateRegistrationNumber(),
                'user_id' => $user->id,
                'ppdb_setting_id' => $ppdbSetting->id,
                'program_id' => $request->program_id,
                'full_name' => $request->full_name,
                'nik' => $request->nik,
                'nisn' => $request->nisn,
                'gender' => $request->gender, // Keep original L/P for ppdb_registrations table
                'birth_date' => $request->birth_date,
                'birth_place' => $request->birth_place,
                'religion' => $request->religion,
                'address' => $request->address,
                'phone' => $request->phone,
                'email' => $request->email,
                'father_name' => $request->father_name,
                'father_occupation' => $request->father_occupation,
                'father_phone' => $request->father_phone,
                'mother_name' => $request->mother_name,
                'mother_occupation' => $request->mother_occupation,
                'mother_phone' => $request->mother_phone,
                'previous_school' => $request->previous_school,
                'previous_school_address' => $request->previous_school_address,
                'final_grade' => $request->final_grade,
                'status' => 'pending',
            ]);

            // Send email verification first
            \Log::info('=== EMAIL VERIFICATION START ===');
            try {
                $verificationToken = $user->generateEmailVerificationToken();
                Mail::to($user->email)->send(new EmailVerificationMail($user, $verificationToken));
                \Log::info('Email verification sent successfully to: ' . $user->email);
            } catch (\Exception $e) {
                \Log::error('Failed to send email verification: ' . $e->getMessage());
            }
            \Log::info('=== EMAIL VERIFICATION END ===');

            // Send welcome email with login credentials
            \Log::info('=== WELCOME EMAIL START ===');
            \Log::info('Attempting to send PPDB welcome email to: ' . $user->email);
            \Log::info('Registration number: ' . $registration->registration_number);
            \Log::info('Temporary password: ' . $temporaryPassword);

            try {
                $result = Mail::to($user->email)->send(new PPDBWelcomeMail($registration, $user, $temporaryPassword));
                \Log::info('Mail send result: ' . json_encode($result));
                \Log::info('PPDB welcome email sent successfully to: ' . $user->email);
            } catch (\Exception $e) {
                // Log error but don't fail the registration
                \Log::error('=== EMAIL ERROR START ===');
                \Log::error('Failed to send PPDB welcome email: ' . $e->getMessage());
                \Log::error('Error file: ' . $e->getFile() . ' line: ' . $e->getLine());
                \Log::error('Email error details: ' . $e->getTraceAsString());
                \Log::error('=== EMAIL ERROR END ===');
            }
            \Log::info('=== WELCOME EMAIL END ===');

            // Check if request is AJAX
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pendaftaran berhasil!',
                    'registration_number' => $registration->registration_number,
                    'redirect_url' => route('ppdb.success', $registration->registration_number)
                ]);
            }

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pendaftaran berhasil! Silakan cek email untuk informasi login.',
                    'registration_number' => $registration->registration_number,
                    'redirect_url' => route('ppdb.success', $registration->registration_number)
                ]);
            }

            return redirect()->route('ppdb.success', $registration->registration_number)
                ->with('success', 'Pendaftaran berhasil! Silakan cek email untuk informasi login.');

        } catch (\Exception $e) {
            \Log::error('=== REGISTRATION ERROR ===');
            \Log::error('Error: ' . $e->getMessage());
            \Log::error('File: ' . $e->getFile() . ' Line: ' . $e->getLine());
            \Log::error('Trace: ' . $e->getTraceAsString());

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.'
                ], 500);
            }

            return back()->with('error', 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.')
                ->withInput();
        }
        } catch (\Exception $globalE) {
            \Log::error('=== GLOBAL REGISTRATION ERROR ===');
            \Log::error('Error: ' . $globalE->getMessage());

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan sistem. Silakan coba lagi.'
                ], 500);
            }

            return back()->with('error', 'Terjadi kesalahan sistem. Silakan coba lagi.')
                ->withInput();
        }
    }

    public function success($registrationNumber)
    {
        $registration = PPDBRegistration::where('registration_number', $registrationNumber)->first();

        if (!$registration) {
            abort(404);
        }

        return view('ppdb.success', compact('registration'));
    }

    public function status($registrationNumber)
    {
        $registration = PPDBRegistration::where('registration_number', $registrationNumber)
            ->with(['program', 'documents'])
            ->first();

        if (!$registration) {
            abort(404);
        }

        return view('ppdb.status', compact('registration'));
    }

    public function checkStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'registration_number' => 'required|string',
            'nik' => 'required|string|size:16',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $registration = PPDBRegistration::where('registration_number', $request->registration_number)
            ->where('nik', $request->nik)
            ->with('program')
            ->first();

        if (!$registration) {
            return back()->with('error', 'Data pendaftaran tidak ditemukan.')->withInput();
        }

        // Check if request is AJAX
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'redirect_url' => route('ppdb.status', $registration->registration_number)
            ]);
        }

        return view('ppdb.status', compact('registration'));
    }
}
