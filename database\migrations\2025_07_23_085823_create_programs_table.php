<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->string('image')->nullable();
            $table->enum('level', ['TK', 'SD', 'SMP', 'SMA', 'SMK'])->default('SMA');
            $table->enum('type', ['regular', 'unggulan', 'internasional'])->default('regular');
            $table->text('curriculum')->nullable();
            $table->text('requirements')->nullable();
            $table->json('subjects')->nullable(); // Store subjects as JSON array
            $table->json('extracurricular')->nullable(); // Store extracurricular as JSON array
            $table->integer('duration_years')->default(3);
            $table->decimal('fee', 15, 2)->nullable();
            $table->integer('capacity')->nullable();
            $table->integer('current_students')->default(0);
            $table->boolean('is_active')->default(true);
            $table->integer('order')->default(0);
            $table->timestamps();

            $table->index(['level', 'is_active']);
            $table->index(['type', 'is_active']);
            $table->index('order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};
