<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CleanupExpiredTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:cleanup-tokens';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup expired email verification and password reset tokens';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired tokens...');

        // Cleanup expired email verification tokens
        $expiredEmailTokens = \App\Models\User::whereNotNull('email_verification_token')
            ->where('email_verification_expires_at', '<', now())
            ->count();

        if ($expiredEmailTokens > 0) {
            \App\Models\User::whereNotNull('email_verification_token')
                ->where('email_verification_expires_at', '<', now())
                ->update([
                    'email_verification_token' => null,
                    'email_verification_sent_at' => null,
                    'email_verification_expires_at' => null,
                ]);

            $this->info("Cleaned up {$expiredEmailTokens} expired email verification tokens.");
        }

        // Cleanup expired password reset tokens
        $expiredPasswordTokens = \App\Models\User::whereNotNull('password_reset_token')
            ->where('password_reset_expires_at', '<', now())
            ->count();

        if ($expiredPasswordTokens > 0) {
            \App\Models\User::whereNotNull('password_reset_token')
                ->where('password_reset_expires_at', '<', now())
                ->update([
                    'password_reset_token' => null,
                    'password_reset_sent_at' => null,
                    'password_reset_expires_at' => null,
                ]);

            $this->info("Cleaned up {$expiredPasswordTokens} expired password reset tokens.");
        }

        // Cleanup old security logs (older than 90 days)
        $oldLogs = \App\Models\SecurityLog::where('created_at', '<', now()->subDays(90))->count();

        if ($oldLogs > 0) {
            \App\Models\SecurityLog::where('created_at', '<', now()->subDays(90))->delete();
            $this->info("Cleaned up {$oldLogs} old security logs.");
        }

        // Cleanup expired sessions
        $expiredSessions = \DB::table('sessions')
            ->where('last_activity', '<', now()->subMinutes(config('session.lifetime', 120))->timestamp)
            ->count();

        if ($expiredSessions > 0) {
            \DB::table('sessions')
                ->where('last_activity', '<', now()->subMinutes(config('session.lifetime', 120))->timestamp)
                ->delete();

            $this->info("Cleaned up {$expiredSessions} expired sessions.");
        }

        $this->info('Token cleanup completed successfully!');

        // Log the cleanup activity
        \App\Models\SecurityLog::logEvent('system_maintenance', null, [
            'type' => 'token_cleanup',
            'expired_email_tokens' => $expiredEmailTokens,
            'expired_password_tokens' => $expiredPasswordTokens,
            'old_security_logs' => $oldLogs,
            'expired_sessions' => $expiredSessions,
        ]);

        return 0;
    }
}
