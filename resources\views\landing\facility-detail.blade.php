@extends('layouts.landing')

@section('title', $facility->name . ' - Fasilitas')

@section('content')
<!-- Hero Section -->
<section class="hero-section bg-gradient" style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 100px 0 50px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}" class="text-white-50">Beranda</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('landing') }}#facilities" class="text-white-50">Fasilitas</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ $facility->name }}</li>
                    </ol>
                </nav>
                <h1 class="display-4 fw-bold mb-3">{{ $facility->name }}</h1>
                <p class="lead mb-0">{{ $facility->description }}</p>
            </div>
        </div>
    </div>
</section>

<!-- Facility Content -->
<section class="section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Featured Image -->
                @if($facility->image)
                <div class="mb-5" data-aos="fade-up">
                    <img src="{{ asset('storage/' . $facility->image) }}" 
                         alt="{{ $facility->name }}" 
                         class="img-fluid rounded shadow-lg w-100"
                         style="max-height: 500px; object-fit: cover;">
                </div>
                @endif
                
                <div class="row">
                    <!-- Main Content -->
                    <div class="col-lg-8">
                        <!-- Facility Description -->
                        <div class="facility-content mb-5" data-aos="fade-up">
                            <h3 class="mb-4">Tentang {{ $facility->name }}</h3>
                            <div class="content">
                                {!! nl2br(e($facility->description)) !!}
                            </div>
                        </div>

                        <!-- Specifications -->
                        @if($facility->specifications)
                        <div class="facility-specs mb-5" data-aos="fade-up" data-aos-delay="100">
                            <h3 class="mb-4">Spesifikasi</h3>
                            <div class="content">
                                {!! nl2br(e($facility->specifications)) !!}
                            </div>
                        </div>
                        @endif

                        <!-- Features -->
                        @if($facility->features && is_array($facility->features) && count($facility->features) > 0)
                        <div class="facility-features mb-5" data-aos="fade-up" data-aos-delay="200">
                            <h3 class="mb-4">Fitur-fitur</h3>
                            <div class="row">
                                @foreach($facility->features as $feature)
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3">
                                            <i class="fas fa-check-circle text-success"></i>
                                        </div>
                                        <span>{{ $feature }}</span>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Facility Info -->
                        <div class="facility-info-card mb-4" data-aos="fade-up" data-aos-delay="300">
                            <h4 class="mb-4">Informasi Fasilitas</h4>
                            
                            <div class="info-item mb-3">
                                <div class="info-label">Kategori</div>
                                <div class="info-value">
                                    @switch($facility->category)
                                        @case('academic')
                                            <span class="badge bg-primary">Akademik</span>
                                            @break
                                        @case('sports')
                                            <span class="badge bg-success">Olahraga</span>
                                            @break
                                        @case('library')
                                            <span class="badge bg-info">Perpustakaan</span>
                                            @break
                                        @case('laboratory')
                                            <span class="badge bg-warning">Laboratorium</span>
                                            @break
                                        @case('dormitory')
                                            <span class="badge bg-secondary">Asrama</span>
                                            @break
                                        @case('cafeteria')
                                            <span class="badge bg-danger">Kantin</span>
                                            @break
                                        @case('mosque')
                                            <span class="badge bg-dark">Masjid</span>
                                            @break
                                        @default
                                            <span class="badge bg-light text-dark">Lainnya</span>
                                    @endswitch
                                </div>
                            </div>

                            @if($facility->capacity)
                            <div class="info-item mb-3">
                                <div class="info-label">Kapasitas</div>
                                <div class="info-value">{{ number_format($facility->capacity) }} orang</div>
                            </div>
                            @endif

                            <div class="info-item mb-3">
                                <div class="info-label">Status</div>
                                <div class="info-value">
                                    @if($facility->is_available)
                                        <span class="badge bg-success">Tersedia</span>
                                    @else
                                        <span class="badge bg-danger">Tidak Tersedia</span>
                                    @endif
                                </div>
                            </div>

                            @if($facility->is_featured)
                            <div class="info-item">
                                <div class="info-label">Status</div>
                                <div class="info-value">
                                    <span class="badge bg-warning text-dark">Fasilitas Unggulan</span>
                                </div>
                            </div>
                            @endif
                        </div>

                        <!-- Contact Info -->
                        <div class="contact-info-card" data-aos="fade-up" data-aos-delay="400">
                            <h4 class="mb-4">Informasi Lebih Lanjut</h4>
                            <p class="mb-3">Untuk informasi lebih lanjut tentang fasilitas ini, silakan hubungi kami:</p>
                            
                            <div class="contact-item mb-3">
                                <i class="fas fa-phone text-primary me-2"></i>
                                <span>(*************</span>
                            </div>
                            
                            <div class="contact-item mb-3">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span><EMAIL></span>
                            </div>
                            
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <span>Jl. Pendidikan No. 123, Jakarta</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Facilities -->
<section class="section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">Fasilitas Lainnya</h2>
                <p class="section-subtitle">Jelajahi fasilitas unggulan lainnya yang kami miliki</p>
            </div>
        </div>
        
        <div class="row g-4">
            @php
                $relatedFacilities = App\Models\Facility::where('is_available', true)
                    ->where('is_featured', true)
                    ->where('id', '!=', $facility->id)
                    ->take(3)
                    ->get();
            @endphp
            
            @forelse($relatedFacilities as $related)
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="facility-card">
                    @if($related->image)
                        <img src="{{ asset('storage/' . $related->image) }}" alt="{{ $related->name }}" class="facility-bg">
                    @else
                        <div class="facility-bg placeholder-container placeholder-facility">
                            <div>
                                <i class="fas fa-building fa-2x placeholder-icon"></i>
                                <div>{{ $related->name }}</div>
                            </div>
                        </div>
                    @endif
                    <div class="facility-overlay">
                        <div>
                            <h4 class="facility-title">{{ $related->name }}</h4>
                            <p class="mb-3">{{ Str::limit($related->description, 80) }}</p>
                            <a href="{{ route('landing.facility', $related->slug) }}" class="btn btn-outline-light btn-sm">
                                Lihat Detail <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12 text-center">
                <p class="text-muted">Tidak ada fasilitas lainnya untuk ditampilkan.</p>
            </div>
            @endforelse
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.facility-content .content,
.facility-specs .content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
}

.facility-info-card,
.contact-info-card {
    background: #fff;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.info-item {
    border-bottom: 1px solid #f8f9fa;
    padding-bottom: 0.75rem;
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-value {
    font-weight: 600;
    color: #495057;
}

.contact-item {
    display: flex;
    align-items: center;
}

.feature-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.facility-card {
    position: relative;
    height: 300px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.facility-card:hover {
    transform: translateY(-5px);
}

.facility-bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.facility-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    color: white;
    padding: 2rem;
    display: flex;
    align-items: end;
}

.facility-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.placeholder-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #6c757d;
    text-align: center;
}

.placeholder-facility {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}
</style>
@endpush
