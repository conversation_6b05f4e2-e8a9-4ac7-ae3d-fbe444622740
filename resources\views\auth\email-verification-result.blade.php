@extends('layouts.landing')

@section('title', 'Verifikasi Email')
@section('description', 'Hasil verifikasi email')

@push('styles')
<style>
    .verification-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }
    
    .verification-card {
        background: white;
        border-radius: 15px;
        padding: 40px;
        max-width: 500px;
        width: 100%;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    .verification-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: white;
    }
    
    .success-icon {
        background: linear-gradient(135deg, #4CAF50, #45a049);
    }
    
    .error-icon {
        background: linear-gradient(135deg, #f44336, #d32f2f);
    }
    
    .verification-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #2c3e50;
    }
    
    .verification-message {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 30px;
        line-height: 1.6;
    }
    
    .user-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        text-align: left;
    }
    
    .user-info h5 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .user-info p {
        margin: 5px 0;
        color: #495057;
    }
    
    .action-buttons {
        margin-top: 30px;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        margin: 5px;
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        margin: 5px;
    }
    
    .resend-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .resend-form {
        margin-top: 15px;
    }
    
    .form-control {
        border-radius: 25px;
        padding: 12px 20px;
        border: 2px solid #e9ecef;
        margin-bottom: 15px;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    @media (max-width: 768px) {
        .verification-card {
            padding: 30px 20px;
            margin: 10px;
        }
        
        .verification-title {
            font-size: 1.5rem;
        }
        
        .verification-message {
            font-size: 1rem;
        }
    }
</style>
@endpush

@section('content')
<div class="verification-container">
    <div class="verification-card">
        <!-- Icon -->
        <div class="verification-icon {{ $success ? 'success-icon' : 'error-icon' }}">
            @if($success)
                <i class="fas fa-check"></i>
            @else
                <i class="fas fa-times"></i>
            @endif
        </div>
        
        <!-- Title -->
        <h1 class="verification-title">
            @if($success)
                Email Berhasil Diverifikasi!
            @else
                Verifikasi Email Gagal
            @endif
        </h1>
        
        <!-- Message -->
        <p class="verification-message">
            {{ $message }}
        </p>
        
        <!-- User Info (if available) -->
        @if($user)
        <div class="user-info">
            <h5><i class="fas fa-user me-2"></i>Informasi Akun</h5>
            <p><strong>Nama:</strong> {{ $user->name }}</p>
            <p><strong>Email:</strong> {{ $user->email }}</p>
            <p><strong>Tipe Akun:</strong> {{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</p>
            @if($success)
                <p><strong>Status Email:</strong> <span class="text-success">✅ Terverifikasi</span></p>
            @else
                <p><strong>Status Email:</strong> <span class="text-danger">❌ Belum Terverifikasi</span></p>
            @endif
        </div>
        @endif
        
        <!-- Resend Section (if verification failed and can resend) -->
        @if(!$success && isset($can_resend) && $can_resend && $user)
        <div class="resend-section">
            <h5><i class="fas fa-envelope me-2"></i>Kirim Ulang Email Verifikasi</h5>
            <p>Jika Anda tidak menerima email verifikasi atau link sudah kedaluwarsa, Anda dapat mengirim ulang email verifikasi.</p>
            
            <form class="resend-form" id="resendForm">
                @csrf
                <input type="email" class="form-control" name="email" value="{{ $user->email }}" readonly>
                <button type="submit" class="btn btn-primary" id="resendBtn">
                    <i class="fas fa-paper-plane me-2"></i>Kirim Ulang Email Verifikasi
                </button>
            </form>
        </div>
        @endif
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            @if($success)
                <a href="{{ route('login') }}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Login Sekarang
                </a>
            @endif
            
            <a href="{{ route('landing') }}" class="btn btn-secondary">
                <i class="fas fa-home me-2"></i>Kembali ke Beranda
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const resendForm = document.getElementById('resendForm');
    const resendBtn = document.getElementById('resendBtn');
    
    if (resendForm) {
        resendForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Disable button
            resendBtn.disabled = true;
            resendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mengirim...';
            
            // Get form data
            const formData = new FormData(resendForm);
            
            // Send request
            fetch('{{ route("email.verification.resend") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: data.message,
                        confirmButtonColor: '#667eea'
                    });
                    
                    // Update button text
                    resendBtn.innerHTML = '<i class="fas fa-check me-2"></i>Email Terkirim';
                    
                    // Disable for 5 minutes
                    setTimeout(() => {
                        resendBtn.disabled = false;
                        resendBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Kirim Ulang Email Verifikasi';
                    }, 300000); // 5 minutes
                    
                } else {
                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal!',
                        text: data.message,
                        confirmButtonColor: '#667eea'
                    });
                    
                    // Re-enable button
                    resendBtn.disabled = false;
                    resendBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Kirim Ulang Email Verifikasi';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                
                Swal.fire({
                    icon: 'error',
                    title: 'Terjadi Kesalahan!',
                    text: 'Gagal mengirim email verifikasi. Silakan coba lagi.',
                    confirmButtonColor: '#667eea'
                });
                
                // Re-enable button
                resendBtn.disabled = false;
                resendBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Kirim Ulang Email Verifikasi';
            });
        });
    }
});
</script>
@endpush
