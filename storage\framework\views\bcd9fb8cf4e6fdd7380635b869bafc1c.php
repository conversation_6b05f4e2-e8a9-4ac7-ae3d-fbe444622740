<?php $__env->startSection('title', 'Detail User'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Detail User</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.users.index')); ?>">Manajemen User</a></li>
                <li class="breadcrumb-item active"><?php echo e($user->name); ?></li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="btn btn-warning me-2">
            <i class="fas fa-edit me-2"></i>Edit User
        </a>
        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Kembali
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <!-- User Profile Card -->
        <div class="card mb-4">
            <div class="card-body text-center">
                <div class="avatar-xl mx-auto mb-3">
                    <?php if($user->avatar_url): ?>
                        <img src="<?php echo e($user->avatar_url); ?>" alt="<?php echo e($user->name); ?>" class="rounded-circle">
                    <?php else: ?>
                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center mx-auto">
                            <?php echo e($user->avatar_initials); ?>

                        </div>
                    <?php endif; ?>
                </div>
                
                <h4><?php echo e($user->name); ?></h4>
                <p class="text-muted"><?php echo e($user->email); ?></p>
                
                <span class="badge bg-<?php echo e($user->user_type == 'super_admin' ? 'danger' : ($user->user_type == 'admin' ? 'warning' : ($user->user_type == 'guru' ? 'info' : ($user->user_type == 'siswa' ? 'success' : 'secondary')))); ?> fs-6 mb-3">
                    <?php echo e(ucfirst(str_replace('_', ' ', $user->user_type))); ?>

                </span>
                
                <div class="d-flex justify-content-center mb-3">
                    <?php if($user->is_active): ?>
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-1"></i>Aktif
                        </span>
                    <?php else: ?>
                        <span class="badge bg-danger">
                            <i class="fas fa-times-circle me-1"></i>Nonaktif
                        </span>
                    <?php endif; ?>
                </div>

                <?php if($user->id !== Auth::id()): ?>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#resetPasswordModal">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </button>
                        <button type="button" class="btn btn-outline-<?php echo e($user->is_active ? 'danger' : 'success'); ?> toggle-status"
                                data-user-id="<?php echo e($user->id); ?>"
                                data-user-name="<?php echo e($user->name); ?>">
                            <i class="fas fa-<?php echo e($user->is_active ? 'ban' : 'check'); ?> me-2"></i>
                            <?php echo e($user->is_active ? 'Nonaktifkan' : 'Aktifkan'); ?>

                        </button>
                        <?php if($user->isLocked()): ?>
                            <button type="button" class="btn btn-outline-success unlock-account"
                                    data-user-id="<?php echo e($user->id); ?>"
                                    data-user-name="<?php echo e($user->name); ?>">
                                <i class="fas fa-unlock me-2"></i>Buka Kunci Akun
                            </button>
                        <?php else: ?>
                            <button type="button" class="btn btn-outline-secondary lock-account"
                                    data-user-id="<?php echo e($user->id); ?>"
                                    data-user-name="<?php echo e($user->name); ?>">
                                <i class="fas fa-lock me-2"></i>Kunci Akun
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Statistik</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo e($user->created_at->diffInDays()); ?></h4>
                            <small class="text-muted">Hari Bergabung</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success"><?php echo e($user->last_login_at ? $user->last_login_at->diffForHumans() : 'Belum pernah'); ?></h4>
                        <small class="text-muted">Login Terakhir</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <!-- User Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Personal</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Nama Lengkap</label>
                        <p class="fw-bold"><?php echo e($user->name); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Email</label>
                        <p class="fw-bold"><?php echo e($user->email); ?></p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Username</label>
                        <p class="fw-bold"><?php echo e($user->username ?? '-'); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tipe User</label>
                        <p class="fw-bold"><?php echo e(ucfirst(str_replace('_', ' ', $user->user_type))); ?></p>
                    </div>
                </div>

                <?php if($user->nisn || $user->nip): ?>
                <div class="row">
                    <?php if($user->nisn): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">NISN</label>
                        <p class="fw-bold"><?php echo e($user->nisn); ?></p>
                    </div>
                    <?php endif; ?>
                    <?php if($user->nip): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">NIP</label>
                        <p class="fw-bold"><?php echo e($user->nip); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Nomor Telepon</label>
                        <p class="fw-bold"><?php echo e($user->phone ?? '-'); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Jenis Kelamin</label>
                        <p class="fw-bold"><?php echo e($user->gender ? ($user->gender == 'male' ? 'Laki-laki' : 'Perempuan') : '-'); ?></p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tanggal Lahir</label>
                        <p class="fw-bold"><?php echo e($user->birth_date ? $user->birth_date->format('d F Y') : '-'); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Status Email</label>
                        <p class="fw-bold">
                            <?php if($user->email_verified_at): ?>
                                <span class="badge bg-success">Terverifikasi</span>
                            <?php else: ?>
                                <span class="badge bg-warning">Belum Terverifikasi</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>

                <?php if($user->address): ?>
                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">Alamat</label>
                        <p class="fw-bold"><?php echo e($user->address); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Account Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Akun</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tanggal Bergabung</label>
                        <p class="fw-bold"><?php echo e($user->created_at->format('d F Y H:i')); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Terakhir Diperbarui</label>
                        <p class="fw-bold"><?php echo e($user->updated_at->format('d F Y H:i')); ?></p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Login Terakhir</label>
                        <p class="fw-bold"><?php echo e($user->last_login_at ? $user->last_login_at->format('d F Y H:i') : 'Belum pernah login'); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">IP Login Terakhir</label>
                        <p class="fw-bold"><?php echo e($user->last_login_ip ?? '-'); ?></p>
                    </div>
                </div>

                <?php if($user->role_changed_at): ?>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Role Diubah</label>
                        <p class="fw-bold"><?php echo e($user->role_changed_at->format('d F Y H:i')); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Role Sebelumnya</label>
                        <p class="fw-bold"><?php echo e($user->previous_user_type ? ucfirst(str_replace('_', ' ', $user->previous_user_type)) : '-'); ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <hr class="my-4">

                <h6 class="mb-3">Status Keamanan Akun</h6>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Status Kunci</label>
                        <p class="fw-bold">
                            <?php if($user->isLocked()): ?>
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-lock me-1"></i>Terkunci
                                </span>
                                <br><small class="text-muted">Hingga: <?php echo e($user->locked_until->format('d F Y H:i')); ?></small>
                            <?php else: ?>
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-unlock me-1"></i>Normal
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Percobaan Login Gagal</label>
                        <p class="fw-bold">
                            <?php if($user->hasFailedAttempts()): ?>
                                <span class="badge bg-warning fs-6"><?php echo e($user->failed_login_attempts); ?> kali</span>
                            <?php else: ?>
                                <span class="badge bg-success fs-6">0 kali</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Information -->
        <?php if($user->staffProfile || $user->studentProfile || $user->parentProfile): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Profil</h5>
            </div>
            <div class="card-body">
                <?php if($user->staffProfile): ?>
                    <h6 class="text-primary">Profil Staff</h6>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <strong>Posisi:</strong> <?php echo e($user->staffProfile->position ?? '-'); ?>

                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>Mata Pelajaran:</strong> <?php echo e($user->staffProfile->subject ?? '-'); ?>

                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>Kualifikasi:</strong> <?php echo e($user->staffProfile->qualifications ?? '-'); ?>

                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>Pengalaman:</strong> <?php echo e($user->staffProfile->experience ?? '-'); ?>

                        </div>
                    </div>
                <?php endif; ?>

                <?php if($user->studentProfile): ?>
                    <h6 class="text-success">Profil Siswa</h6>
                    <p>Informasi profil siswa akan ditampilkan di sini.</p>
                <?php endif; ?>

                <?php if($user->parentProfile): ?>
                    <h6 class="text-info">Profil Orang Tua</h6>
                    <p>Informasi profil orang tua akan ditampilkan di sini.</p>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('admin.users.reset-password', $user)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="password" class="form-label">Password Baru</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Reset Password</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.avatar-xl {
    width: 120px;
    height: 120px;
}

.avatar-xl img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    background-color: #6c757d;
    color: white;
    font-weight: bold;
    font-size: 36px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Toggle user status with SweetAlert
    $('.toggle-status').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');
        const currentStatus = <?php echo e($user->is_active ? 'true' : 'false'); ?>;
        const newStatus = currentStatus ? 'nonaktifkan' : 'aktifkan';
        const button = $(this);

        console.log('Detail Toggle Status:', {
            userId: userId,
            userName: userName,
            currentStatus: currentStatus,
            newStatus: newStatus
        });

        Swal.fire({
            title: 'Konfirmasi',
            text: `Apakah Anda yakin ingin ${newStatus} user ${userName}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `Ya, ${newStatus}!`,
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('Making AJAX request to:', `/admin/users/${userId}/toggle-status`);

                $.ajax({
                    url: `/admin/users/${userId}/toggle-status`,
                    method: 'POST',
                    beforeSend: function() {
                        console.log('Detail AJAX request started');
                    },
                    success: function(response) {
                        console.log('Detail AJAX Success Response:', response);

                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Detail AJAX Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat mengubah status user.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Lock account
    $('.lock-account').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');

        Swal.fire({
            title: 'Konfirmasi Kunci Akun',
            text: `Apakah Anda yakin ingin mengunci akun ${userName}? Akun akan terkunci selama 24 jam.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#6c757d',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Kunci Akun!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/users/${userId}/lock-account`,
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 3000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        let message = 'Terjadi kesalahan saat mengunci akun.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Unlock account
    $('.unlock-account').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');

        Swal.fire({
            title: 'Konfirmasi Buka Kunci',
            text: `Apakah Anda yakin ingin membuka kunci akun ${userName}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Buka Kunci!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/users/${userId}/unlock-account`,
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 3000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        let message = 'Terjadi kesalahan saat membuka kunci akun.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/users/show.blade.php ENDPATH**/ ?>