<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\SecurityLog;
use App\Http\Requests\LoginRequest;
use Carbon\Carbon;

class CustomLoginController extends Controller
{
    public function showLoginForm()
    {
        // Redirect if user is already authenticated
        if (Auth::check()) {
            $user = Auth::user();

            // Log the redirect attempt
            SecurityLog::logEvent('login_success', $user->id, [
                'type' => 'already_authenticated_redirect',
                'user_type' => $user->user_type,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            // Redirect based on user type
            return $this->redirectToDashboard($user);
        }

        return view('auth.login');
    }

    public function login(LoginRequest $request)
    {

        $loginField = $request->input('login');
        $password = $request->input('password');

        // Find user by multiple login methods
        $user = $this->findUserByLogin($loginField);

        if (!$user) {
            SecurityLog::logEvent('login_failed', null, [
                'login_attempt' => $loginField,
                'reason' => 'User not found',
            ]);

            return back()->withErrors([
                'login' => 'Kredensial yang Anda masukkan tidak cocok dengan data kami.',
            ])->withInput();
        }

        // Check if account is locked
        if ($user->isLocked()) {
            SecurityLog::logEvent('login_failed', $user->id, [
                'login_attempt' => $loginField,
                'reason' => 'Account locked',
            ]);

            return back()->withErrors([
                'login' => 'Akun Anda dikunci karena terlalu banyak percobaan login yang gagal. Silakan coba lagi nanti.',
            ])->withInput();
        }

        // Check for active sessions (prevent double login)
        if ($this->hasActiveSessions($user)) {
            SecurityLog::logEvent('login_failed', $user->id, [
                'login_attempt' => $loginField,
                'reason' => 'Active session exists',
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->withErrors([
                'login' => 'Akun Anda sedang aktif di perangkat lain. Silakan logout terlebih dahulu atau tunggu sesi berakhir.',
            ])->withInput();
        }

        // Check if email is verified (except for admin and super_admin)
        if (!in_array($user->user_type, ['admin', 'super_admin']) && !$user->hasVerifiedEmail()) {
            SecurityLog::logEvent('login_failed', $user->id, [
                'login_attempt' => $loginField,
                'reason' => 'Email not verified',
            ]);

            return back()->withErrors([
                'login' => 'Email Anda belum diverifikasi. Silakan cek email dan klik link verifikasi yang telah dikirimkan.',
            ])->withInput();
        }

        // Check if account is active
        if (!$user->is_active) {
            SecurityLog::logEvent('login_failed', $user->id, [
                'login_attempt' => $loginField,
                'reason' => 'Account inactive',
            ]);

            return back()->withErrors([
                'login' => 'Akun Anda tidak aktif. Silakan hubungi administrator.',
            ])->withInput();
        }

        // Verify password
        if (!Hash::check($password, $user->password)) {
            $user->incrementFailedAttempts();

            SecurityLog::logEvent('login_failed', $user->id, [
                'login_attempt' => $loginField,
                'reason' => 'Invalid password',
                'failed_attempts' => $user->failed_login_attempts,
            ]);

            return back()->withErrors([
                'login' => 'Kredensial yang Anda masukkan tidak cocok dengan data kami.',
            ])->withInput();
        }

        // Successful login
        $user->resetFailedAttempts();
        $user->update([
            'last_login_at' => Carbon::now(),
            'last_login_ip' => $request->ip(),
        ]);

        Auth::login($user, $request->filled('remember'));

        SecurityLog::logEvent('login_success', $user->id, [
            'login_method' => $this->getLoginMethod($loginField),
        ]);

        return $this->redirectAfterLogin($user);
    }

    public function logout(Request $request)
    {
        $user = Auth::user();

        if ($user) {
            SecurityLog::logEvent('logout', $user->id);
        }

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')->with('success', 'Anda telah berhasil logout.');
    }

    private function findUserByLogin(string $login): ?User
    {
        // Try different login methods based on input format
        if (filter_var($login, FILTER_VALIDATE_EMAIL)) {
            // Email login
            return User::where('email', $login)->first();
        } elseif (is_numeric($login)) {
            // NISN login (for students)
            return User::where('nisn', $login)->first();
        } elseif (preg_match('/^\d{18}$/', $login)) {
            // NIP login (18 digits for teachers/staff)
            return User::where('nip', $login)->first();
        } else {
            // Username login (for admin/staff)
            return User::where('username', $login)->first();
        }
    }

    private function getLoginMethod(string $login): string
    {
        if (filter_var($login, FILTER_VALIDATE_EMAIL)) {
            return 'email';
        } elseif (is_numeric($login) && strlen($login) <= 10) {
            return 'nisn';
        } elseif (preg_match('/^\d{18}$/', $login)) {
            return 'nip';
        } else {
            return 'username';
        }
    }

    private function redirectAfterLogin(User $user): \Illuminate\Http\RedirectResponse
    {
        // All users redirect to the same dashboard route
        // The DashboardController will handle role-based routing
        return redirect()->intended('/dashboard');
    }

    private function redirectToDashboard(User $user): \Illuminate\Http\RedirectResponse
    {
        // Redirect to dashboard based on user type
        return redirect()->route('dashboard')->with('info', 'Anda sudah login. Selamat datang kembali!');
    }

    private function hasActiveSessions(User $user): bool
    {
        // Check for active sessions in database
        $activeSessions = \Illuminate\Support\Facades\DB::table('sessions')
            ->where('user_id', $user->id)
            ->where('last_activity', '>', now()->subMinutes(config('session.lifetime', 120))->timestamp)
            ->count();

        return $activeSessions > 0;
    }

    public function forceLogin(LoginRequest $request)
    {
        $loginField = $request->input('login');
        $password = $request->input('password');

        // Find user by multiple login methods
        $user = $this->findUserByLogin($loginField);

        if (!$user || !Hash::check($password, $user->password)) {
            SecurityLog::logEvent('login_failed', $user?->id, [
                'login_attempt' => $loginField,
                'reason' => 'Invalid credentials for force login',
            ]);

            return back()->withErrors([
                'login' => 'Kredensial yang Anda masukkan tidak cocok dengan data kami.',
            ])->withInput();
        }

        // Terminate all existing sessions for this user
        \Illuminate\Support\Facades\DB::table('sessions')
            ->where('user_id', $user->id)
            ->delete();

        // Log the force login
        SecurityLog::logEvent('force_login', $user->id, [
            'login_field' => $loginField,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'terminated_sessions' => true,
        ]);

        // Reset failed attempts
        $user->resetFailedAttempts();

        // Login the user
        Auth::login($user, $request->boolean('remember'));

        // Regenerate session
        $request->session()->regenerate();

        return $this->redirectAfterLogin($user)->with('success', 'Login berhasil. Sesi sebelumnya telah diakhiri.');
    }
}
