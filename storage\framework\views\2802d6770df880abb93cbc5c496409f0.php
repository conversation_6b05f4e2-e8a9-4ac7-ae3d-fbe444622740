<?php $__env->startSection('title', 'Manajemen Program'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Manajemen Program Pendidikan</h1>
    <a href="<?php echo e(route('admin.content.programs.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Tambah Program
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_programs']); ?></div>
            <div class="stats-label">Total Program</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['active']); ?></div>
            <div class="stats-label">Program Aktif</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-pause-circle"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['inactive']); ?></div>
            <div class="stats-label">Program Nonaktif</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.content.programs.index')); ?>" class="row g-3">
            <div class="col-md-2">
                <label for="level" class="form-label">Level</label>
                <select name="level" id="level" class="form-select">
                    <option value="">Semua Level</option>
                    <option value="TK" <?php echo e(request('level') == 'TK' ? 'selected' : ''); ?>>TK</option>
                    <option value="SD" <?php echo e(request('level') == 'SD' ? 'selected' : ''); ?>>SD</option>
                    <option value="SMP" <?php echo e(request('level') == 'SMP' ? 'selected' : ''); ?>>SMP</option>
                    <option value="SMA" <?php echo e(request('level') == 'SMA' ? 'selected' : ''); ?>>SMA</option>
                    <option value="SMK" <?php echo e(request('level') == 'SMK' ? 'selected' : ''); ?>>SMK</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="type" class="form-label">Tipe</label>
                <select name="type" id="type" class="form-select">
                    <option value="">Semua Tipe</option>
                    <option value="regular" <?php echo e(request('type') == 'regular' ? 'selected' : ''); ?>>Regular</option>
                    <option value="unggulan" <?php echo e(request('type') == 'unggulan' ? 'selected' : ''); ?>>Unggulan</option>
                    <option value="internasional" <?php echo e(request('type') == 'internasional' ? 'selected' : ''); ?>>Internasional</option>
                </select>
            </div>
            <div class="col-md-5">
                <label for="search" class="form-label">Pencarian</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari nama program..." 
                       value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="<?php echo e(route('admin.content.programs.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Programs Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Daftar Program</h5>
    </div>
    <div class="card-body">
        <?php if($programs->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Gambar</th>
                            <th>Nama Program</th>
                            <th>Level</th>
                            <th>Tipe</th>
                            <th>Kapasitas</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <div class="program-image">
                                    <?php if($program->image): ?>
                                        <img src="<?php echo e(Storage::url($program->image)); ?>" alt="<?php echo e($program->name); ?>" class="rounded">
                                    <?php else: ?>
                                        <div class="image-placeholder rounded d-flex align-items-center justify-content-center">
                                            <i class="fas fa-graduation-cap text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo e($program->name); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo e(Str::limit($program->description, 60)); ?></small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($program->level); ?></span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($program->type == 'regular' ? 'secondary' : ($program->type == 'unggulan' ? 'warning' : 'primary')); ?>">
                                    <?php echo e(ucfirst($program->type)); ?>

                                </span>
                            </td>
                            <td>
                                <small>
                                    <?php echo e($program->current_students ?? 0); ?>/<?php echo e($program->capacity ?? 0); ?>

                                    <?php if($program->capacity): ?>
                                        <br><span class="text-muted"><?php echo e(round(($program->current_students ?? 0) / $program->capacity * 100)); ?>%</span>
                                    <?php endif; ?>
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($program->is_active ? 'success' : 'danger'); ?>">
                                    <?php echo e($program->is_active ? 'Aktif' : 'Nonaktif'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.content.programs.show', $program)); ?>" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.content.programs.edit', $program)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?php echo e(route('landing.program', $program->slug)); ?>" class="btn btn-sm btn-outline-success" title="Lihat di Website" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteProgram('<?php echo e($program->slug); ?>', '<?php echo e(addslashes($program->name)); ?>')"
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan <?php echo e($programs->firstItem()); ?> - <?php echo e($programs->lastItem()); ?> dari <?php echo e($programs->total()); ?> program
                </div>
                <?php echo e($programs->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada program ditemukan</h5>
                <p class="text-muted">Belum ada program yang sesuai dengan filter yang dipilih.</p>
                <a href="<?php echo e(route('admin.content.programs.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Tambah Program Pertama
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.program-image {
    width: 60px;
    height: 40px;
}

.program-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-placeholder {
    width: 60px;
    height: 40px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteProgram(slug, name) {
    Swal.fire({
        title: 'Hapus Program?',
        text: `Apakah Anda yakin ingin menghapus program "${name}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus program',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send AJAX DELETE request
            $.ajax({
                url: `/admin/content/programs/${slug}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Program berhasil dihapus.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    console.error('Delete error:', xhr);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus program: ' + (xhr.responseJSON?.message || 'Unknown error'),
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/content/programs/index.blade.php ENDPATH**/ ?>