<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ParentRegistration extends Model
{
    protected $fillable = [
        'registration_number',
        'parent_name',
        'parent_email',
        'parent_phone',
        'parent_type',
        'parent_nik',
        'parent_birth_date',
        'parent_occupation',
        'parent_address',
        'student_name',
        'student_nisn',
        'student_class',
        'relationship',
        'student_verification_code',
        'status',
        'rejection_reason',
        'verified_at',
        'approved_at',
        'student_id',
        'user_id',
        'approved_by',
    ];

    protected function casts(): array
    {
        return [
            'parent_birth_date' => 'date',
            'verified_at' => 'datetime',
            'approved_at' => 'datetime',
        ];
    }

    /**
     * Generate unique registration number
     */
    public static function generateRegistrationNumber(): string
    {
        $year = date('Y');
        $month = date('m');

        // Get last registration number for this month
        $lastRegistration = self::where('registration_number', 'like', "PARENT{$year}{$month}%")
            ->orderBy('registration_number', 'desc')
            ->first();

        if ($lastRegistration) {
            $lastNumber = (int) substr($lastRegistration->registration_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return "PARENT{$year}{$month}" . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Relationships
     */
    public function student()
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Status Methods
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isVerified(): bool
    {
        return $this->status === 'verified';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Verify student connection
     */
    public function verifyStudent(User $student): bool
    {
        // Check if student verification code matches
        if ($this->student_verification_code && $student->student_verification_code === $this->student_verification_code) {
            $this->update([
                'student_id' => $student->id,
                'status' => 'verified',
                'verified_at' => now(),
            ]);
            return true;
        }

        // Check by NISN and name match
        if ($this->student_nisn && $student->nisn === $this->student_nisn) {
            // Additional name similarity check
            $similarity = 0;
            similar_text(strtolower($this->student_name), strtolower($student->name), $similarity);

            if ($similarity >= 80) { // 80% similarity
                $this->update([
                    'student_id' => $student->id,
                    'status' => 'verified',
                    'verified_at' => now(),
                ]);
                return true;
            }
        }

        return false;
    }

    /**
     * Approve registration and create user account
     */
    public function approve(User $approver): bool
    {
        if (!$this->isVerified()) {
            return false;
        }

        try {
            // Generate temporary password
            $temporaryPassword = Str::random(8);

            // Create user account
            $user = User::create([
                'name' => $this->parent_name,
                'email' => $this->parent_email,
                'user_type' => 'orang_tua',
                'phone' => $this->parent_phone,
                'birth_date' => $this->parent_birth_date,
                'address' => $this->parent_address,
                'password' => bcrypt($temporaryPassword),
                'is_active' => true,
            ]);

            // Link parent to student
            if ($this->student_id) {
                $user->update(['parent_id' => $this->student_id]);
            }

            // Update registration
            $this->update([
                'user_id' => $user->id,
                'status' => 'approved',
                'approved_at' => now(),
                'approved_by' => $approver->id,
            ]);

            // Send welcome email with credentials
            // TODO: Implement email sending

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to approve parent registration: ' . $e->getMessage());
            return false;
        }
    }
}
