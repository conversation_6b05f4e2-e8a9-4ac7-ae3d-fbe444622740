<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SchoolSetting;
use App\Models\HeroSection;
use App\Models\News;
use App\Models\Program;
use App\Models\Facility;
use App\Models\User;
use App\Models\StaffProfile;
use Illuminate\Support\Facades\Hash;

class SchoolDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create School Settings
        SchoolSetting::create([
            'school_name' => 'SMA Negeri 1 Sekolahku',
            'school_address' => 'Jl. Pendidikan No. 123, Jakarta Selatan, DKI Jakarta 12345',
            'school_phone' => '(021) 1234-5678',
            'school_email' => '<EMAIL>',
            'school_website' => 'https://sekolahku.sch.id',
            'school_description' => 'Sekolah unggulan yang berkomitmen menghasilkan generasi berkarakter, berprestasi, dan siap menghadapi tantangan global dengan pendidikan berkualitas tinggi.',
            'vision' => 'Menjadi sekolah unggul yang menghasilkan generasi berkarakter, berprestasi, dan siap menghadapi tantangan global.',
            'mission' => 'Menyelenggarakan pendidikan berkualitas tinggi, mengembangkan karakter dan akhlak mulia, memfasilitasi pengembangan bakat dan minat siswa, serta menciptakan lingkungan belajar yang kondusif.',
            'values' => 'Integritas, Inovasi, Kolaborasi, Keunggulan',
            'history' => 'Didirikan pada tahun 2000, SMA Negeri 1 Sekolahku telah menjadi salah satu sekolah terbaik di Jakarta dengan berbagai prestasi akademik dan non-akademik.',
            'principal_name' => 'Dr. Ahmad Wijaya, S.Pd., M.Pd.',
            'principal_message' => 'Selamat datang di SMA Negeri 1 Sekolahku. Kami berkomitmen memberikan pendidikan terbaik untuk mempersiapkan siswa menghadapi masa depan yang penuh tantangan.',
            'accreditation' => 'A',
            'established_year' => 2000,
            'total_students' => 1200,
            'total_teachers' => 75,
            'social_media' => json_encode([
                'facebook' => 'https://facebook.com/sekolahku',
                'instagram' => 'https://instagram.com/sekolahku',
                'youtube' => 'https://youtube.com/sekolahku',
                'twitter' => 'https://twitter.com/sekolahku'
            ]),
            'contact_info' => json_encode([
                'whatsapp' => '08123456789',
                'fax' => '(021) 8765-4321'
            ]),
            'is_active' => true,
        ]);

        // Create Hero Sections
        HeroSection::create([
            'title' => 'Selamat Datang di SMA Negeri 1 Sekolahku',
            'subtitle' => 'Membangun Generasi Unggul dengan Pendidikan Berkualitas',
            'description' => 'Bergabunglah dengan kami untuk meraih prestasi terbaik dan mempersiapkan masa depan yang cerah',
            'button_text' => 'Daftar Sekarang',
            'button_link' => '/login',
            'order' => 1,
            'is_active' => true,
        ]);

        HeroSection::create([
            'title' => 'Fasilitas Modern & Lengkap',
            'subtitle' => 'Mendukung Proses Pembelajaran Optimal',
            'description' => 'Dilengkapi dengan laboratorium, perpustakaan digital, dan fasilitas olahraga terbaik',
            'button_text' => 'Lihat Fasilitas',
            'button_link' => '#facilities',
            'order' => 2,
            'is_active' => true,
        ]);

        // Create Programs
        Program::create([
            'name' => 'Program IPA',
            'slug' => 'program-ipa',
            'description' => 'Program Ilmu Pengetahuan Alam dengan fokus pada Matematika, Fisika, Kimia, dan Biologi. Mempersiapkan siswa untuk melanjutkan ke perguruan tinggi jurusan sains dan teknologi.',
            'level' => 'SMA',
            'type' => 'regular',
            'curriculum' => 'Kurikulum Merdeka dengan pendalaman materi sains',
            'requirements' => 'Nilai rata-rata minimal 80, minat pada bidang sains',
            'subjects' => json_encode(['Matematika', 'Fisika', 'Kimia', 'Biologi', 'Bahasa Indonesia', 'Bahasa Inggris']),
            'extracurricular' => json_encode(['Olimpiade Sains', 'Robotika', 'Penelitian Ilmiah']),
            'duration_years' => 3,
            'capacity' => 120,
            'current_students' => 108,
            'is_active' => true,
            'order' => 1,
        ]);

        Program::create([
            'name' => 'Program IPS',
            'slug' => 'program-ips',
            'description' => 'Program Ilmu Pengetahuan Sosial dengan fokus pada Sejarah, Geografi, Ekonomi, dan Sosiologi. Mempersiapkan siswa untuk bidang sosial, ekonomi, dan humaniora.',
            'level' => 'SMA',
            'type' => 'regular',
            'curriculum' => 'Kurikulum Merdeka dengan pendalaman ilmu sosial',
            'requirements' => 'Nilai rata-rata minimal 75, minat pada bidang sosial',
            'subjects' => json_encode(['Sejarah', 'Geografi', 'Ekonomi', 'Sosiologi', 'Bahasa Indonesia', 'Bahasa Inggris']),
            'extracurricular' => json_encode(['Debat', 'Jurnalistik', 'Kewirausahaan']),
            'duration_years' => 3,
            'capacity' => 90,
            'current_students' => 85,
            'is_active' => true,
            'order' => 2,
        ]);

        Program::create([
            'name' => 'Program Bahasa',
            'slug' => 'program-bahasa',
            'description' => 'Program Bahasa dengan fokus pada Bahasa Indonesia, Bahasa Inggris, dan Bahasa Asing lainnya. Mempersiapkan siswa untuk bidang komunikasi dan linguistik.',
            'level' => 'SMA',
            'type' => 'unggulan',
            'curriculum' => 'Kurikulum Merdeka dengan pendalaman bahasa',
            'requirements' => 'Nilai rata-rata minimal 78, kemampuan bahasa yang baik',
            'subjects' => json_encode(['Bahasa Indonesia', 'Bahasa Inggris', 'Bahasa Mandarin', 'Sastra', 'Linguistik']),
            'extracurricular' => json_encode(['English Club', 'Teater', 'Jurnalistik']),
            'duration_years' => 3,
            'capacity' => 60,
            'current_students' => 55,
            'is_active' => true,
            'order' => 3,
        ]);

        // Create Facilities
        Facility::create([
            'name' => 'Laboratorium Komputer',
            'slug' => 'laboratorium-komputer',
            'description' => 'Laboratorium komputer modern dengan 40 unit PC terbaru, dilengkapi software pembelajaran dan akses internet berkecepatan tinggi.',
            'category' => 'laboratory',
            'capacity' => 40,
            'specifications' => 'PC Intel Core i5, RAM 8GB, SSD 256GB, Monitor 22 inch',
            'features' => json_encode(['AC', 'Proyektor', 'Sound System', 'CCTV', 'WiFi']),
            'is_available' => true,
            'is_featured' => true,
            'order' => 1,
        ]);

        Facility::create([
            'name' => 'Perpustakaan Digital',
            'slug' => 'perpustakaan-digital',
            'description' => 'Perpustakaan modern dengan koleksi buku fisik dan digital, ruang baca yang nyaman, dan akses ke database jurnal internasional.',
            'category' => 'library',
            'capacity' => 100,
            'specifications' => 'Koleksi 10.000+ buku, 50 komputer, ruang diskusi',
            'features' => json_encode(['AC', 'WiFi', 'Ruang Baca', 'Ruang Diskusi', 'Database Online']),
            'is_available' => true,
            'is_featured' => true,
            'order' => 2,
        ]);

        Facility::create([
            'name' => 'Lapangan Olahraga',
            'slug' => 'lapangan-olahraga',
            'description' => 'Lapangan serbaguna untuk berbagai cabang olahraga seperti basket, voli, futsal, dan badminton.',
            'category' => 'sports',
            'capacity' => 200,
            'specifications' => 'Lapangan indoor 40x20m, lantai parket',
            'features' => json_encode(['Tribun', 'Lampu LED', 'Sound System', 'Ruang Ganti']),
            'is_available' => true,
            'is_featured' => true,
            'order' => 3,
        ]);

        // Create some staff profiles
        $principalUser = User::create([
            'name' => 'Dr. Ahmad Wijaya, S.Pd., M.Pd.',
            'email' => '<EMAIL>',
            'nip' => '196501011990031001',
            'user_type' => 'guru',
            'phone' => '08123456789',
            'gender' => 'L',
            'password' => Hash::make('principal123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $principalUser->assignRole('guru');

        StaffProfile::create([
            'user_id' => $principalUser->id,
            'employee_id' => 'KS001',
            'position' => 'kepala_sekolah',
            'qualifications' => 'S3 Pendidikan, S2 Manajemen Pendidikan',
            'degree' => 'Dr., S.Pd., M.Pd.',
            'experience' => '25 tahun pengalaman dalam bidang pendidikan',
            'join_date' => '2010-07-01',
            'employment_status' => 'tetap',
            'bio' => 'Kepala sekolah berpengalaman dengan dedikasi tinggi untuk kemajuan pendidikan.',
            'is_featured' => true,
            'order' => 1,
        ]);

        $teacher1 = User::create([
            'name' => 'Dra. Siti Nurhaliza, M.Pd.',
            'email' => '<EMAIL>',
            'nip' => '197203151998032001',
            'user_type' => 'guru',
            'phone' => '08123456790',
            'gender' => 'P',
            'password' => Hash::make('teacher123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $teacher1->assignRole('guru');

        StaffProfile::create([
            'user_id' => $teacher1->id,
            'employee_id' => 'GR001',
            'position' => 'guru',
            'subject' => 'Matematika',
            'qualifications' => 'S2 Pendidikan Matematika',
            'degree' => 'Dra., M.Pd.',
            'experience' => '15 tahun mengajar Matematika',
            'join_date' => '2008-08-01',
            'employment_status' => 'tetap',
            'bio' => 'Guru matematika berpengalaman dengan metode pembelajaran inovatif.',
            'is_featured' => true,
            'order' => 2,
        ]);

        $teacher2 = User::create([
            'name' => 'Budi Santoso, S.Pd., M.Si.',
            'email' => '<EMAIL>',
            'nip' => '198005102005011002',
            'user_type' => 'guru',
            'phone' => '08123456791',
            'gender' => 'L',
            'password' => Hash::make('teacher123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $teacher2->assignRole('guru');

        StaffProfile::create([
            'user_id' => $teacher2->id,
            'employee_id' => 'GR002',
            'position' => 'guru',
            'subject' => 'Fisika',
            'qualifications' => 'S2 Fisika',
            'degree' => 'S.Pd., M.Si.',
            'experience' => '12 tahun mengajar Fisika',
            'join_date' => '2012-07-01',
            'employment_status' => 'tetap',
            'bio' => 'Guru fisika yang passionate dalam eksperimen dan penelitian.',
            'is_featured' => true,
            'order' => 3,
        ]);

        $teacher3 = User::create([
            'name' => 'Rina Kartika, S.Pd.',
            'email' => '<EMAIL>',
            'nip' => '198507252010012001',
            'user_type' => 'guru',
            'phone' => '08123456792',
            'gender' => 'P',
            'password' => Hash::make('teacher123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $teacher3->assignRole('guru');

        StaffProfile::create([
            'user_id' => $teacher3->id,
            'employee_id' => 'GR003',
            'position' => 'guru',
            'subject' => 'Bahasa Inggris',
            'qualifications' => 'S1 Pendidikan Bahasa Inggris',
            'degree' => 'S.Pd.',
            'experience' => '8 tahun mengajar Bahasa Inggris',
            'join_date' => '2016-07-01',
            'employment_status' => 'tetap',
            'bio' => 'Guru bahasa Inggris dengan sertifikasi internasional TOEFL.',
            'is_featured' => true,
            'order' => 4,
        ]);
    }
}
