@extends('layouts.landing')

@section('title', 'Pendaftaran Berhasil')
@section('description', 'Pendaftaran orang tua berhasil disubmit')

@push('styles')
<style>
    .success-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }
    
    .success-card {
        background: white;
        border-radius: 15px;
        padding: 40px;
        max-width: 600px;
        width: 100%;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    .success-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        background: linear-gradient(135deg, #4CAF50, #45a049);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2.5rem;
    }
    
    .success-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #2c3e50;
    }
    
    .success-message {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 30px;
        line-height: 1.6;
    }
    
    .registration-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin: 30px 0;
        text-align: left;
    }
    
    .registration-info h5 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-weight: 600;
        text-align: center;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
    }
    
    .info-value {
        color: #2c3e50;
    }
    
    .status-badge {
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-verified {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .status-approved {
        background: #d4edda;
        color: #155724;
    }
    
    .next-steps {
        background: #e3f2fd;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        text-align: left;
    }
    
    .next-steps h6 {
        color: #1976d2;
        font-weight: 600;
        margin-bottom: 15px;
    }
    
    .next-steps ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .next-steps li {
        margin-bottom: 8px;
        color: #495057;
    }
    
    .action-buttons {
        margin-top: 30px;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        margin: 5px;
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        margin: 5px;
    }
    
    @media (max-width: 768px) {
        .success-card {
            padding: 30px 20px;
            margin: 10px;
        }
        
        .success-title {
            font-size: 1.5rem;
        }
        
        .success-message {
            font-size: 1rem;
        }
        
        .info-row {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .info-value {
            margin-top: 5px;
        }
    }
</style>
@endpush

@section('content')
<div class="success-container">
    <div class="success-card">
        <!-- Success Icon -->
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>
        
        <!-- Title -->
        <h1 class="success-title">
            Pendaftaran Berhasil!
        </h1>
        
        <!-- Message -->
        <p class="success-message">
            Terima kasih telah mendaftar sebagai orang tua siswa. Pendaftaran Anda telah berhasil disubmit dan sedang dalam proses verifikasi.
            @if($registration->status === 'verified')
                <br><strong>Kabar baik!</strong> Data siswa Anda sudah terverifikasi otomatis. Tinggal menunggu persetujuan admin.
            @endif
        </p>
        
        <!-- Registration Info -->
        <div class="registration-info">
            <h5><i class="fas fa-clipboard-list me-2"></i>Informasi Pendaftaran</h5>
            
            <div class="info-row">
                <span class="info-label">Nomor Pendaftaran:</span>
                <span class="info-value"><strong>{{ $registration->registration_number }}</strong></span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Nama Orang Tua:</span>
                <span class="info-value">{{ $registration->parent_name }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ $registration->parent_email }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Nama Siswa:</span>
                <span class="info-value">{{ $registration->student_name }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Hubungan:</span>
                <span class="info-value">{{ ucfirst($registration->relationship) }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Status:</span>
                <span class="info-value">
                    @if($registration->status === 'pending')
                        <span class="status-badge status-pending">Menunggu Verifikasi</span>
                    @elseif($registration->status === 'verified')
                        <span class="status-badge status-verified">Terverifikasi</span>
                    @elseif($registration->status === 'approved')
                        <span class="status-badge status-approved">Disetujui</span>
                    @endif
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Tanggal Daftar:</span>
                <span class="info-value">{{ $registration->created_at->format('d F Y, H:i') }} WIB</span>
            </div>
        </div>
        
        <!-- Next Steps -->
        <div class="next-steps">
            <h6><i class="fas fa-list-ol me-2"></i>Langkah Selanjutnya</h6>
            <ul>
                @if($registration->status === 'pending')
                    <li>Admin akan memverifikasi data Anda dalam 1-2 hari kerja</li>
                    <li>Anda akan menerima email notifikasi setelah verifikasi selesai</li>
                    <li>Jika disetujui, Anda akan mendapat email dengan informasi login</li>
                @elseif($registration->status === 'verified')
                    <li>Data Anda sudah terverifikasi dengan siswa</li>
                    <li>Menunggu persetujuan admin untuk pembuatan akun</li>
                    <li>Anda akan menerima email dengan informasi login setelah disetujui</li>
                @elseif($registration->status === 'approved')
                    <li>Akun Anda sudah dibuat dan siap digunakan</li>
                    <li>Cek email untuk informasi login</li>
                    <li>Login ke portal orang tua untuk mengakses informasi siswa</li>
                @endif
                <li>Simpan nomor pendaftaran ini untuk referensi</li>
                <li>Hubungi admin jika ada pertanyaan</li>
            </ul>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            @if($registration->status === 'approved')
                <a href="{{ route('login') }}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Login Sekarang
                </a>
            @endif
            
            <a href="{{ route('landing') }}" class="btn btn-secondary">
                <i class="fas fa-home me-2"></i>Kembali ke Beranda
            </a>
        </div>
        
        <!-- Contact Info -->
        <div class="mt-4">
            <small class="text-muted">
                <strong>Butuh bantuan?</strong><br>
                Email: {{ config('mail.from.address') }}<br>
                Website: {{ config('app.url') }}
            </small>
        </div>
    </div>
</div>
@endsection
