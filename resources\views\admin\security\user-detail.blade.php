@extends('layouts.dashboard')

@section('title', 'Detail Aktivitas User - ' . $user->name)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Detail Aktivitas User</h1>
    <div>
        <a href="{{ route('admin.security.user-activity') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-2"></i>Kembali
        </a>
        <a href="{{ route('admin.security.export', request()->query()) }}" class="btn btn-outline-success">
            <i class="fas fa-download me-2"></i>Export Data
        </a>
    </div>
</div>

<!-- User Info Card -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                        <i class="fas fa-user text-white fa-2x"></i>
                    </div>
                    <div>
                        <h4 class="mb-1">{{ $user->name }}</h4>
                        <p class="text-muted mb-1">{{ $user->email }}</p>
                        <div>
                            <span class="badge bg-info me-2">{{ ucfirst($user->user_type) }}</span>
                            @if($user->email_verified_at)
                                <span class="badge bg-success">Verified</span>
                            @else
                                <span class="badge bg-warning">Unverified</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="fw-bold text-primary fs-4">{{ number_format($stats['total_activities']) }}</div>
                        <small class="text-muted">Total Aktivitas</small>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold text-info fs-4">{{ number_format($stats['today_activities']) }}</div>
                        <small class="text-muted">Hari Ini</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['failed_logins']) }}</div>
            <div class="stats-label">Failed Logins</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-sign-in-alt"></i>
            </div>
            <div class="stats-number">{{ $stats['last_login'] ? $stats['last_login']->created_at->diffForHumans() : 'Never' }}</div>
            <div class="stats-label">Last Login</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['high_risk_activities']) }}</div>
            <div class="stats-label">High Risk</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-info">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ number_format($stats['this_week_activities']) }}</div>
            <div class="stats-label">Minggu Ini</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.security.user-activity.detail', $user) }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="event_type" class="form-label">Event Type</label>
                    <select class="form-select" id="event_type" name="event_type">
                        <option value="">Semua Event</option>
                        @foreach($eventTypes as $type)
                            <option value="{{ $type }}" {{ request('event_type') == $type ? 'selected' : '' }}>
                                {{ $type }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">Dari Tanggal</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">Sampai Tanggal</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Activity Logs -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>Log Aktivitas
        </h5>
    </div>
    <div class="card-body">
        @if($logs->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Tanggal & Waktu</th>
                            <th>Event Type</th>
                            <th>Deskripsi</th>
                            <th>Risk Level</th>
                            <th>IP Address</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($logs as $log)
                        <tr>
                            <td>
                                <div>{{ $log->created_at->format('d M Y') }}</div>
                                <small class="text-muted">{{ $log->created_at->format('H:i:s') }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ $log->event_type }}</span>
                            </td>
                            <td>{{ Str::limit($log->description, 50) }}</td>
                            <td>
                                @php
                                    $riskColors = [
                                        'low' => 'success',
                                        'medium' => 'warning',
                                        'high' => 'danger',
                                        'critical' => 'dark'
                                    ];
                                    $color = $riskColors[$log->risk_level] ?? 'secondary';
                                @endphp
                                <span class="badge bg-{{ $color }}">{{ ucfirst($log->risk_level) }}</span>
                            </td>
                            <td><code>{{ $log->ip_address }}</code></td>
                            <td>
                                @if($log->is_resolved)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Resolved
                                    </span>
                                @else
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>Unresolved
                                    </span>
                                @endif
                            </td>
                            <td>
                                <a href="{{ route('admin.security.show', $log) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @if(!$log->is_resolved)
                                <button type="button" class="btn btn-sm btn-outline-success ms-1" onclick="resolveLog({{ $log->id }})">
                                    <i class="fas fa-check"></i>
                                </button>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Menampilkan {{ $logs->firstItem() }} sampai {{ $logs->lastItem() }} dari {{ $logs->total() }} hasil
                </div>
                <div class="pagination-wrapper">
                    {{ $logs->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada log aktivitas</h5>
                <p class="text-muted">Belum ada aktivitas yang tercatat untuk user ini.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-lg {
    width: 60px;
    height: 60px;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e3e6f0;
    position: relative;
    overflow: hidden;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    color: white;
}

.stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary { background: linear-gradient(45deg, #007bff, #0056b3); }
.bg-gradient-success { background: linear-gradient(45deg, #28a745, #1e7e34); }
.bg-gradient-danger { background: linear-gradient(45deg, #dc3545, #bd2130); }
.bg-gradient-warning { background: linear-gradient(45deg, #ffc107, #d39e00); }
.bg-gradient-info { background: linear-gradient(45deg, #17a2b8, #117a8b); }

/* Fix for pagination SVG icons that are too large */
.pagination-wrapper .pagination svg {
    width: 16px !important;
    height: 16px !important;
}

.pagination-wrapper .pagination .page-link svg {
    width: 14px !important;
    height: 14px !important;
}

/* Override Tailwind classes that might cause large icons */
.pagination-wrapper .w-5 {
    width: 16px !important;
}

.pagination-wrapper .h-5 {
    height: 16px !important;
}
</style>
@endpush

@push('scripts')
<script>
function resolveLog(logId) {
    Swal.fire({
        title: 'Tandai sebagai Resolved?',
        text: 'Log ini akan ditandai sebagai resolved.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Resolve!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Use relative URL to avoid protocol issues
            fetch(`/admin/security/logs/${logId}/resolve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: data.message,
                        timer: 3000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    throw new Error(data.message || 'Terjadi kesalahan');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal!',
                    text: error.message || 'Terjadi kesalahan saat resolve log.',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}
</script>
@endpush
