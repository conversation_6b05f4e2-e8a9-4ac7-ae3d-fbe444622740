<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Beranda') - {{ $schoolSettings->school_name ?? 'Sekolahku' }}</title>
    <meta name="description" content="@yield('description', $schoolSettings->school_description ?? 'Website resmi sekolah')">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ $schoolSettings->school_logo ?? '/favicon.ico' }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --dark-color: #2d3748;
            --light-color: #f7fafc;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --danger-color: #f56565;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }

        /* Navigation */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            padding: 1rem 0;
            width: 100%;
            margin: 0;
            left: 0;
            right: 0;
            z-index: 1050 !important;
        }

        .navbar.scrolled {
            padding: 0.5rem 0;
            background: rgba(255, 255, 255, 0.98) !important;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }

        .nav-link {
            font-weight: 500;
            color: var(--dark-color) !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        /* Section Styling */
        .section {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .section-title p {
            font-size: 1.1rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, var(--dark-color), #1a202c);
            color: white;
            padding: 60px 0 20px;
            width: 100%;
            margin: 0;
            left: 0;
            right: 0;
            z-index: 1040 !important;
            position: relative;
        }

        /* Ensure full-width containers */
        .navbar .container-fluid,
        .footer .container-fluid {
            max-width: 100%;
            margin: 0;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        /* Remove any potential margins */
        .navbar-nav {
            margin: 0;
        }

        /* Fix dropdown positioning */
        .navbar .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-radius: 10px;
            padding: 10px 0;
            margin-top: 10px;
            min-width: 200px;
        }

        .navbar .dropdown-menu-end {
            right: 0;
            left: auto;
        }

        .navbar .dropdown-item {
            padding: 10px 20px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .navbar .dropdown-item:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateX(5px);
        }

        .navbar .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        .navbar .dropdown-divider {
            margin: 8px 0;
            opacity: 0.2;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .navbar .container-fluid,
            .footer .container-fluid {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }

        /* Text Visibility Fix */
        .section h1, .section h2, .section h3, .section h4, .section h5, .section h6 {
            color: #333 !important;
        }

        .section p, .section .lead {
            color: #555 !important;
        }

        .card-body h1, .card-body h2, .card-body h3, .card-body h4, .card-body h5, .card-body h6 {
            color: #333 !important;
        }

        .card-body p, .card-body .card-text {
            color: #555 !important;
        }

        /* Breadcrumb fix */
        .breadcrumb-item a {
            color: #007bff !important;
        }

        .breadcrumb-item.active {
            color: #6c757d !important;
        }

        /* SweetAlert z-index fix */
        .swal2-container {
            z-index: 2000 !important;
        }

        .swal2-backdrop-show {
            z-index: 1999 !important;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .section {
                padding: 60px 0;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="{{ route('landing') }}">
                @if(file_exists(public_path('images/Logo.png')))
                    <img src="{{ asset('images/Logo.png') }}" alt="Logo" height="40" class="me-2">
                @elseif($schoolSettings && $schoolSettings->school_logo)
                    <img src="{{ asset('storage/' . $schoolSettings->school_logo) }}" alt="Logo" height="40" class="me-2">
                @else
                    <div class="logo-placeholder me-2">
                        <i class="fas fa-graduation-cap" style="font-size: 2rem; color: var(--primary-color);"></i>
                    </div>
                @endif
                <span>{{ $schoolSettings->school_name ?? 'Sekolahku' }}</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing') }}">Beranda</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Profil
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ Request::is('/') ? '#about' : route('landing') . '#about' }}">Tentang Kami</a></li>
                            <li><a class="dropdown-item" href="{{ Request::is('/') ? '#vision-mission' : route('landing') . '#vision-mission' }}">Visi & Misi</a></li>
                            <li><a class="dropdown-item" href="{{ Request::is('/') ? '#staff' : route('landing') . '#staff' }}">Tenaga Pendidik</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ Request::is('/') ? '#programs' : route('landing') . '#programs' }}">Program</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ Request::is('/') ? '#facilities' : route('landing') . '#facilities' }}">Fasilitas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ Request::is('/') ? '#ppdb' : route('landing') . '#ppdb' }}">PPDB Online</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ Request::is('/') ? '#news' : route('landing') . '#news' }}">Berita</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ Request::is('/') ? '#gallery' : route('landing') . '#gallery' }}">Galeri</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ Request::is('/') ? '#contact' : route('landing') . '#contact' }}">Kontak</a>
                    </li>
                    <li class="nav-item">
                        @auth
                            <!-- Menu untuk user yang sudah login -->
                            <div class="btn-group ms-2">
                                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-check me-1"></i>{{ Auth::user()->name }}
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li class="dropdown-header">
                                        <small class="text-muted">{{ ucfirst(Auth::user()->user_type) }}</small>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ route('profile.show') }}">
                                        <i class="fas fa-user-edit me-2"></i>Profil Saya
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('ppdb.check-status') }}">
                                        <i class="fas fa-search me-2"></i>Cek Status PPDB
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form id="landingLogoutForm" method="POST" action="{{ route('logout') }}" class="d-inline">
                                            @csrf
                                            <button type="button" class="dropdown-item text-danger" onclick="confirmLandingLogout()">
                                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        @else
                            <!-- Menu untuk guest/pengunjung -->
                            <div class="btn-group ms-2">
                                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-1"></i>Portal
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="{{ route('parent-registration.index') }}">
                                        <i class="fas fa-users me-2"></i>Pendaftaran Orang Tua
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('login') }}">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login Portal
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ route('ppdb.check-status') }}">
                                        <i class="fas fa-search me-2"></i>Cek Status PPDB
                                    </a></li>
                                </ul>
                            </div>
                        @endauth
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container-fluid px-4">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">{{ $schoolSettings->school_name ?? 'Sekolahku' }}</h5>
                    <p>{{ $schoolSettings->school_description ?? 'Sekolah terbaik untuk masa depan yang cerah.' }}</p>
                    @if($schoolSettings && $schoolSettings->social_media)
                        <div class="social-links">
                            @foreach(json_decode($schoolSettings->social_media, true) as $platform => $url)
                                <a href="{{ $url }}" class="text-white me-3" target="_blank">
                                    <i class="fab fa-{{ $platform }}"></i>
                                </a>
                            @endforeach
                        </div>
                    @endif
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Menu</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ route('landing') }}" class="text-white-50">Beranda</a></li>
                        <li><a href="#about" class="text-white-50">Tentang</a></li>
                        <li><a href="#programs" class="text-white-50">Program</a></li>
                        <li><a href="#facilities" class="text-white-50">Fasilitas</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Informasi</h6>
                    <ul class="list-unstyled">
                        <li><a href="#news" class="text-white-50">Berita</a></li>
                        <li><a href="{{ route('landing.gallery') }}" class="text-white-50">Galeri</a></li>
                        <li><a href="{{ route('landing.contact') }}" class="text-white-50">Kontak</a></li>
                        @guest
                            <li><a href="{{ route('login') }}" class="text-white-50">Login</a></li>
                        @else
                            <li><a href="{{ route('dashboard') }}" class="text-white-50">Dashboard</a></li>
                        @endguest
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="mb-3">Kontak</h6>
                    @if($schoolSettings)
                        <p><i class="fas fa-map-marker-alt me-2"></i>{{ $schoolSettings->school_address }}</p>
                        <p><i class="fas fa-phone me-2"></i>{{ $schoolSettings->school_phone }}</p>
                        <p><i class="fas fa-envelope me-2"></i>{{ $schoolSettings->school_email }}</p>
                    @endif
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; {{ date('Y') }} {{ $schoolSettings->school_name ?? 'Sekolahku' }}. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Dibuat dengan <i class="fas fa-heart text-danger"></i> untuk pendidikan Indonesia</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const href = this.getAttribute('href');
                const target = document.querySelector(href);

                if (target) {
                    // Target exists on current page - smooth scroll
                    e.preventDefault();
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else {
                    // Target doesn't exist - check if we need to redirect to landing page
                    const isLandingPage = window.location.pathname === '/';
                    if (!isLandingPage) {
                        // We're on a separate page, redirect to landing page with anchor
                        e.preventDefault();
                        window.location.href = '/' + href;
                    }
                    // If we're already on landing page but target doesn't exist, let default behavior happen
                }
            });
        });

        // SweetAlert logout confirmation for landing page
        function confirmLandingLogout() {
            Swal.fire({
                title: 'Konfirmasi Logout',
                text: 'Apakah Anda yakin ingin keluar dari sistem?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Logout',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Logging out...',
                        text: 'Mohon tunggu sebentar',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit logout form
                    document.getElementById('landingLogoutForm').submit();
                }
            });
        }
    </script>

    @stack('scripts')
</body>
</html>
