<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PPDBRegistration extends Model
{
    use HasFactory;

    protected $table = 'ppdb_registrations';

    protected $fillable = [
        'registration_number',
        'user_id',
        'ppdb_setting_id',
        'program_id',
        'full_name',
        'nik',
        'nisn',
        'gender',
        'birth_date',
        'birth_place',
        'religion',
        'address',
        'phone',
        'email',
        'father_name',
        'father_occupation',
        'father_phone',
        'mother_name',
        'mother_occupation',
        'mother_phone',
        'previous_school',
        'previous_school_address',
        'final_grade',
        'status',
        'notes',
        'test_score',
        'payment_verified',
        'verified_at',
        'approved_at',
    ];

    protected function casts(): array
    {
        return [
            'birth_date' => 'date',
            'final_grade' => 'decimal:2',
            'test_score' => 'decimal:2',
            'payment_verified' => 'boolean',
            'verified_at' => 'datetime',
            'approved_at' => 'datetime',
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function ppdbSetting()
    {
        return $this->belongsTo(PPDBSetting::class);
    }

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function documents()
    {
        return $this->hasMany(PPDBDocument::class, 'ppdb_registration_id');
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'warning',
            'verified' => 'info',
            'approved' => 'success',
            'rejected' => 'danger',
            'enrolled' => 'primary',
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Menunggu Verifikasi',
            'verified' => 'Terverifikasi',
            'approved' => 'Diterima',
            'rejected' => 'Ditolak',
            'enrolled' => 'Terdaftar',
        ];

        return $labels[$this->status] ?? 'Unknown';
    }

    public static function generateRegistrationNumber($year = null)
    {
        $year = $year ?? date('Y');
        $lastNumber = self::where('registration_number', 'like', "PPDB{$year}%")
                         ->orderBy('registration_number', 'desc')
                         ->first();

        if ($lastNumber) {
            $lastNum = (int) substr($lastNumber->registration_number, -4);
            $newNum = str_pad($lastNum + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNum = '0001';
        }

        return "PPDB{$year}{$newNum}";
    }
}
