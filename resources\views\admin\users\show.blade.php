@extends('layouts.dashboard')

@section('title', 'Detail User')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Detail User</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Manajemen User</a></li>
                <li class="breadcrumb-item active">{{ $user->name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-2"></i>Edit User
        </a>
        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i><PERSON><PERSON><PERSON>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <!-- User Profile Card -->
        <div class="card mb-4">
            <div class="card-body text-center">
                <div class="avatar-xl mx-auto mb-3">
                    @if($user->avatar_url)
                        <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="rounded-circle">
                    @else
                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center mx-auto">
                            {{ $user->avatar_initials }}
                        </div>
                    @endif
                </div>
                
                <h4>{{ $user->name }}</h4>
                <p class="text-muted">{{ $user->email }}</p>
                
                <span class="badge bg-{{ $user->user_type == 'super_admin' ? 'danger' : ($user->user_type == 'admin' ? 'warning' : ($user->user_type == 'guru' ? 'info' : ($user->user_type == 'siswa' ? 'success' : 'secondary'))) }} fs-6 mb-3">
                    {{ ucfirst(str_replace('_', ' ', $user->user_type)) }}
                </span>
                
                <div class="d-flex justify-content-center mb-3">
                    @if($user->is_active)
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-1"></i>Aktif
                        </span>
                    @else
                        <span class="badge bg-danger">
                            <i class="fas fa-times-circle me-1"></i>Nonaktif
                        </span>
                    @endif
                </div>

                @if($user->id !== Auth::id())
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#resetPasswordModal">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </button>
                        <button type="button" class="btn btn-outline-{{ $user->is_active ? 'danger' : 'success' }} toggle-status"
                                data-user-id="{{ $user->id }}"
                                data-user-name="{{ $user->name }}">
                            <i class="fas fa-{{ $user->is_active ? 'ban' : 'check' }} me-2"></i>
                            {{ $user->is_active ? 'Nonaktifkan' : 'Aktifkan' }}
                        </button>
                        @if($user->isLocked())
                            <button type="button" class="btn btn-outline-success unlock-account"
                                    data-user-id="{{ $user->id }}"
                                    data-user-name="{{ $user->name }}">
                                <i class="fas fa-unlock me-2"></i>Buka Kunci Akun
                            </button>
                        @else
                            <button type="button" class="btn btn-outline-secondary lock-account"
                                    data-user-id="{{ $user->id }}"
                                    data-user-name="{{ $user->name }}">
                                <i class="fas fa-lock me-2"></i>Kunci Akun
                            </button>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Statistik</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ $user->created_at->diffInDays() }}</h4>
                            <small class="text-muted">Hari Bergabung</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Belum pernah' }}</h4>
                        <small class="text-muted">Login Terakhir</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <!-- User Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Personal</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Nama Lengkap</label>
                        <p class="fw-bold">{{ $user->name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Email</label>
                        <p class="fw-bold">{{ $user->email }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Username</label>
                        <p class="fw-bold">{{ $user->username ?? '-' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tipe User</label>
                        <p class="fw-bold">{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</p>
                    </div>
                </div>

                @if($user->nisn || $user->nip)
                <div class="row">
                    @if($user->nisn)
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">NISN</label>
                        <p class="fw-bold">{{ $user->nisn }}</p>
                    </div>
                    @endif
                    @if($user->nip)
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">NIP</label>
                        <p class="fw-bold">{{ $user->nip }}</p>
                    </div>
                    @endif
                </div>
                @endif

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Nomor Telepon</label>
                        <p class="fw-bold">{{ $user->phone ?? '-' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Jenis Kelamin</label>
                        <p class="fw-bold">{{ $user->gender ? ($user->gender == 'male' ? 'Laki-laki' : 'Perempuan') : '-' }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tanggal Lahir</label>
                        <p class="fw-bold">{{ $user->birth_date ? $user->birth_date->format('d F Y') : '-' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Status Email</label>
                        <p class="fw-bold">
                            @if($user->email_verified_at)
                                <span class="badge bg-success">Terverifikasi</span>
                            @else
                                <span class="badge bg-warning">Belum Terverifikasi</span>
                            @endif
                        </p>
                    </div>
                </div>

                @if($user->address)
                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">Alamat</label>
                        <p class="fw-bold">{{ $user->address }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Account Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Akun</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tanggal Bergabung</label>
                        <p class="fw-bold">{{ $user->created_at->format('d F Y H:i') }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Terakhir Diperbarui</label>
                        <p class="fw-bold">{{ $user->updated_at->format('d F Y H:i') }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Login Terakhir</label>
                        <p class="fw-bold">{{ $user->last_login_at ? $user->last_login_at->format('d F Y H:i') : 'Belum pernah login' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">IP Login Terakhir</label>
                        <p class="fw-bold">{{ $user->last_login_ip ?? '-' }}</p>
                    </div>
                </div>

                @if($user->role_changed_at)
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Role Diubah</label>
                        <p class="fw-bold">{{ $user->role_changed_at->format('d F Y H:i') }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Role Sebelumnya</label>
                        <p class="fw-bold">{{ $user->previous_user_type ? ucfirst(str_replace('_', ' ', $user->previous_user_type)) : '-' }}</p>
                    </div>
                </div>
                @endif

                <hr class="my-4">

                <h6 class="mb-3">Status Keamanan Akun</h6>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Status Kunci</label>
                        <p class="fw-bold">
                            @if($user->isLocked())
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-lock me-1"></i>Terkunci
                                </span>
                                <br><small class="text-muted">Hingga: {{ $user->locked_until->format('d F Y H:i') }}</small>
                            @else
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-unlock me-1"></i>Normal
                                </span>
                            @endif
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Percobaan Login Gagal</label>
                        <p class="fw-bold">
                            @if($user->hasFailedAttempts())
                                <span class="badge bg-warning fs-6">{{ $user->failed_login_attempts }} kali</span>
                            @else
                                <span class="badge bg-success fs-6">0 kali</span>
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Information -->
        @if($user->staffProfile || $user->studentProfile || $user->parentProfile)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Profil</h5>
            </div>
            <div class="card-body">
                @if($user->staffProfile)
                    <h6 class="text-primary">Profil Staff</h6>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <strong>Posisi:</strong> {{ $user->staffProfile->position ?? '-' }}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>Mata Pelajaran:</strong> {{ $user->staffProfile->subject ?? '-' }}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>Kualifikasi:</strong> {{ $user->staffProfile->qualifications ?? '-' }}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>Pengalaman:</strong> {{ $user->staffProfile->experience ?? '-' }}
                        </div>
                    </div>
                @endif

                @if($user->studentProfile)
                    <h6 class="text-success">Profil Siswa</h6>
                    <p>Informasi profil siswa akan ditampilkan di sini.</p>
                @endif

                @if($user->parentProfile)
                    <h6 class="text-info">Profil Orang Tua</h6>
                    <p>Informasi profil orang tua akan ditampilkan di sini.</p>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.users.reset-password', $user) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="password" class="form-label">Password Baru</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Reset Password</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-xl {
    width: 120px;
    height: 120px;
}

.avatar-xl img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    background-color: #6c757d;
    color: white;
    font-weight: bold;
    font-size: 36px;
}
</style>
@endpush

@push('scripts')
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Toggle user status with SweetAlert
    $('.toggle-status').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');
        const currentStatus = {{ $user->is_active ? 'true' : 'false' }};
        const newStatus = currentStatus ? 'nonaktifkan' : 'aktifkan';
        const button = $(this);

        console.log('Detail Toggle Status:', {
            userId: userId,
            userName: userName,
            currentStatus: currentStatus,
            newStatus: newStatus
        });

        Swal.fire({
            title: 'Konfirmasi',
            text: `Apakah Anda yakin ingin ${newStatus} user ${userName}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `Ya, ${newStatus}!`,
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('Making AJAX request to:', `/admin/users/${userId}/toggle-status`);

                $.ajax({
                    url: `/admin/users/${userId}/toggle-status`,
                    method: 'POST',
                    beforeSend: function() {
                        console.log('Detail AJAX request started');
                    },
                    success: function(response) {
                        console.log('Detail AJAX Success Response:', response);

                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Detail AJAX Error:', {
                            xhr: xhr,
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });

                        let message = 'Terjadi kesalahan saat mengubah status user.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Lock account
    $('.lock-account').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');

        Swal.fire({
            title: 'Konfirmasi Kunci Akun',
            text: `Apakah Anda yakin ingin mengunci akun ${userName}? Akun akan terkunci selama 24 jam.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#6c757d',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Kunci Akun!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/users/${userId}/lock-account`,
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 3000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        let message = 'Terjadi kesalahan saat mengunci akun.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Unlock account
    $('.unlock-account').click(function() {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');

        Swal.fire({
            title: 'Konfirmasi Buka Kunci',
            text: `Apakah Anda yakin ingin membuka kunci akun ${userName}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Buka Kunci!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/admin/users/${userId}/unlock-account`,
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                timer: 3000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        let message = 'Terjadi kesalahan saat membuka kunci akun.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
});
</script>
@endpush
