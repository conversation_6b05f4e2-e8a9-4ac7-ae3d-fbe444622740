<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('student_id')->unique();
            $table->string('class')->nullable();
            $table->year('admission_year')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->string('previous_school')->nullable();
            $table->text('address')->nullable();
            $table->string('blood_type')->nullable();
            $table->text('health_info')->nullable();
            $table->string('emergency_contact')->nullable();
            $table->json('achievements')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('parent_id')->references('id')->on('parent_profiles')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_profiles');
    }
};
