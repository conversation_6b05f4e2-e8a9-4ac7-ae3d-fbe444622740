<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Http\UploadedFile;
use App\Models\SecurityLog;

class SecureFileUpload
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if request has files
        if (!empty($request->allFiles())) {
            foreach ($request->allFiles() as $key => $files) {
                if (is_array($files)) {
                    foreach ($files as $file) {
                        $this->validateFile($file, $key, $request);
                    }
                } else {
                    $this->validateFile($files, $key, $request);
                }
            }
        }

        return $next($request);
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file, string $fieldName, Request $request): void
    {
        // Check if file is valid
        if (!$file->isValid()) {
            $this->logSuspiciousActivity($request, 'invalid_file_upload', [
                'field' => $fieldName,
                'error' => $file->getErrorMessage(),
            ]);
            abort(422, 'File upload tidak valid.');
        }

        // Check file size (max 10MB)
        $maxSize = 10 * 1024 * 1024; // 10MB in bytes
        if ($file->getSize() > $maxSize) {
            $this->logSuspiciousActivity($request, 'oversized_file_upload', [
                'field' => $fieldName,
                'size' => $file->getSize(),
                'max_size' => $maxSize,
            ]);
            abort(422, 'Ukuran file terlalu besar. Maksimal 10MB.');
        }

        // Get file info
        $originalName = $file->getClientOriginalName();
        $extension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();

        // Check for dangerous file extensions
        $this->checkDangerousExtensions($extension, $originalName, $fieldName, $request);

        // Check MIME type
        $this->checkMimeType($mimeType, $extension, $fieldName, $request);

        // Check file content for malicious patterns
        $this->checkFileContent($file, $fieldName, $request);

        // Check for path traversal in filename
        $this->checkPathTraversal($originalName, $fieldName, $request);

        // Check for executable content
        $this->checkExecutableContent($file, $fieldName, $request);
    }

    /**
     * Check for dangerous file extensions
     */
    private function checkDangerousExtensions(string $extension, string $filename, string $fieldName, Request $request): void
    {
        $dangerousExtensions = [
            // Executable files
            'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar',
            // Script files
            'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp', 'pl', 'py', 'rb',
            // Shell scripts
            'sh', 'bash', 'csh', 'ksh', 'zsh',
            // Configuration files
            'htaccess', 'htpasswd', 'ini', 'conf',
            // Database files
            'sql', 'db', 'sqlite', 'mdb',
            // Archive files that might contain malicious content
            'zip', 'rar', '7z', 'tar', 'gz',
        ];

        if (in_array($extension, $dangerousExtensions)) {
            $this->logSuspiciousActivity($request, 'dangerous_file_extension', [
                'field' => $fieldName,
                'filename' => $filename,
                'extension' => $extension,
            ]);
            abort(422, 'Tipe file tidak diizinkan.');
        }

        // Check for double extensions (e.g., image.jpg.php)
        if (preg_match('/\.(php|asp|jsp|pl|py|rb|sh|exe|bat|cmd)\./i', $filename)) {
            $this->logSuspiciousActivity($request, 'double_extension_detected', [
                'field' => $fieldName,
                'filename' => $filename,
            ]);
            abort(422, 'Nama file tidak valid.');
        }
    }

    /**
     * Check MIME type against extension
     */
    private function checkMimeType(string $mimeType, string $extension, string $fieldName, Request $request): void
    {
        $allowedMimes = [
            // Images
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'bmp' => ['image/bmp'],
            'webp' => ['image/webp'],
            // Videos
            'mp4' => ['video/mp4'],
            'avi' => ['video/avi', 'video/x-msvideo'],
            'mov' => ['video/quicktime'],
            'wmv' => ['video/x-ms-wmv'],
            'flv' => ['video/x-flv'],
            'webm' => ['video/webm'],
            // Documents
            'pdf' => ['application/pdf'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'xls' => ['application/vnd.ms-excel'],
            'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            'ppt' => ['application/vnd.ms-powerpoint'],
            'pptx' => ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
            // Text files
            'txt' => ['text/plain'],
            'csv' => ['text/csv', 'application/csv'],
        ];

        if (isset($allowedMimes[$extension])) {
            if (!in_array($mimeType, $allowedMimes[$extension])) {
                $this->logSuspiciousActivity($request, 'mime_type_mismatch', [
                    'field' => $fieldName,
                    'extension' => $extension,
                    'mime_type' => $mimeType,
                    'expected_mimes' => $allowedMimes[$extension],
                ]);
                abort(422, 'Tipe file tidak sesuai dengan ekstensi.');
            }
        }
    }

    /**
     * Check file content for malicious patterns
     */
    private function checkFileContent(UploadedFile $file, string $fieldName, Request $request): void
    {
        // Skip content check for image files to avoid false positives
        $mimeType = $file->getMimeType();
        $imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];

        if (in_array($mimeType, $imageTypes)) {
            // For images, only check for obvious script tags in the beginning
            $handle = fopen($file->getPathname(), 'r');
            if ($handle) {
                $content = fread($handle, 512); // Read less content for images
                fclose($handle);

                // Only check for obvious script tags at the beginning of file
                if (preg_match('/^<\?php|^<script|^<%/', $content)) {
                    $this->logSuspiciousActivity($request, 'script_in_image_detected', [
                        'field' => $fieldName,
                        'filename' => $file->getClientOriginalName(),
                    ]);
                    abort(422, 'File gambar mengandung kode yang tidak diizinkan.');
                }
            }
            return; // Skip other checks for images
        }

        // For non-image files, perform full content check
        $handle = fopen($file->getPathname(), 'r');
        if ($handle) {
            $content = fread($handle, 1024);
            fclose($handle);

            // Check for PHP tags
            if (preg_match('/<\?php|<\?=|<\?|\?>/', $content)) {
                $this->logSuspiciousActivity($request, 'php_code_detected', [
                    'field' => $fieldName,
                    'filename' => $file->getClientOriginalName(),
                ]);
                abort(422, 'File mengandung kode yang tidak diizinkan.');
            }

            // Check for script tags
            if (preg_match('/<script|<\/script>/i', $content)) {
                $this->logSuspiciousActivity($request, 'script_tag_detected', [
                    'field' => $fieldName,
                    'filename' => $file->getClientOriginalName(),
                ]);
                abort(422, 'File mengandung script yang tidak diizinkan.');
            }

            // Check for ASP/JSP tags
            if (preg_match('/<%|%>|<jsp:|<\/jsp:/', $content)) {
                $this->logSuspiciousActivity($request, 'server_script_detected', [
                    'field' => $fieldName,
                    'filename' => $file->getClientOriginalName(),
                ]);
                abort(422, 'File mengandung kode server yang tidak diizinkan.');
            }

            // Check for shell commands
            if (preg_match('/system\(|exec\(|shell_exec\(|passthru\(|eval\(/i', $content)) {
                $this->logSuspiciousActivity($request, 'shell_command_detected', [
                    'field' => $fieldName,
                    'filename' => $file->getClientOriginalName(),
                ]);
                abort(422, 'File mengandung perintah sistem yang tidak diizinkan.');
            }
        }
    }

    /**
     * Check for path traversal in filename
     */
    private function checkPathTraversal(string $filename, string $fieldName, Request $request): void
    {
        // Check for path traversal patterns
        if (preg_match('/\.\.\/|\.\.\\\\|%2e%2e%2f|%2e%2e%5c/i', $filename)) {
            $this->logSuspiciousActivity($request, 'path_traversal_detected', [
                'field' => $fieldName,
                'filename' => $filename,
            ]);
            abort(422, 'Nama file mengandung karakter yang tidak diizinkan.');
        }

        // Check for null bytes
        if (strpos($filename, "\0") !== false) {
            $this->logSuspiciousActivity($request, 'null_byte_detected', [
                'field' => $fieldName,
                'filename' => $filename,
            ]);
            abort(422, 'Nama file mengandung karakter yang tidak diizinkan.');
        }

        // Check for control characters
        if (preg_match('/[\x00-\x1f\x7f]/', $filename)) {
            $this->logSuspiciousActivity($request, 'control_character_detected', [
                'field' => $fieldName,
                'filename' => $filename,
            ]);
            abort(422, 'Nama file mengandung karakter yang tidak diizinkan.');
        }
    }

    /**
     * Check for executable content using file command (if available)
     */
    private function checkExecutableContent(UploadedFile $file, string $fieldName, Request $request): void
    {
        // Skip executable check for image files to avoid false positives
        $mimeType = $file->getMimeType();
        $imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];

        if (in_array($mimeType, $imageTypes)) {
            return; // Skip executable check for images
        }

        // Check file signature/magic bytes
        $handle = fopen($file->getPathname(), 'r');
        if ($handle) {
            $header = fread($handle, 16);
            fclose($handle);

            // Check for executable signatures
            $executableSignatures = [
                'MZ',           // Windows PE
                "\x7fELF",      // Linux ELF
                "\xca\xfe\xba\xbe", // Java class
                "\xfe\xed\xfa\xce", // Mach-O
                "\xfe\xed\xfa\xcf", // Mach-O
            ];

            foreach ($executableSignatures as $signature) {
                if (strpos($header, $signature) === 0) {
                    $this->logSuspiciousActivity($request, 'executable_signature_detected', [
                        'field' => $fieldName,
                        'filename' => $file->getClientOriginalName(),
                        'signature' => bin2hex(substr($header, 0, 4)),
                    ]);
                    abort(422, 'File mengandung konten yang tidak diizinkan.');
                }
            }
        }
    }

    /**
     * Log suspicious file upload activity
     */
    private function logSuspiciousActivity(Request $request, string $type, array $metadata): void
    {
        SecurityLog::logEvent('suspicious_file_upload', auth()->id(), array_merge([
            'type' => $type,
            'url' => $request->fullUrl(),
            'user_agent' => $request->userAgent(),
        ], $metadata));
    }
}
