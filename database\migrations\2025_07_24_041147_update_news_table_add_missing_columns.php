<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('news', function (Blueprint $table) {
            // Add missing columns if they don't exist
            if (!Schema::hasColumn('news', 'category')) {
                $table->string('category')->nullable()->after('status');
            }
            if (!Schema::hasColumn('news', 'tags')) {
                $table->text('tags')->nullable()->after('category');
            }
            if (!Schema::hasColumn('news', 'meta_title')) {
                $table->string('meta_title', 60)->nullable()->after('tags');
            }
            if (!Schema::hasColumn('news', 'meta_description')) {
                $table->string('meta_description', 160)->nullable()->after('meta_title');
            }
            if (!Schema::hasColumn('news', 'likes')) {
                $table->integer('likes')->default(0)->after('views');
            }
            if (!Schema::hasColumn('news', 'shares')) {
                $table->integer('shares')->default(0)->after('likes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('news', function (Blueprint $table) {
            $table->dropColumn(['category', 'tags', 'meta_title', 'meta_description', 'likes', 'shares']);
        });
    }
};
