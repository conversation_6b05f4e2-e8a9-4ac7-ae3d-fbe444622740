<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

abstract class SecureFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    abstract public function rules(): array;

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'required' => 'Field :attribute wajib diisi.',
            'email' => 'Format email tidak valid.',
            'unique' => ':attribute sudah terdaftar dalam sistem.',
            'min' => ':attribute minimal :min karakter.',
            'max' => ':attribute maksimal :max karakter.',
            'confirmed' => 'Konfirmasi :attribute tidak cocok.',
            'regex' => 'Format :attribute tidak valid.',
            'in' => ':attribute yang dipilih tidak valid.',
            'numeric' => ':attribute harus berupa angka.',
            'integer' => ':attribute harus berupa bilangan bulat.',
            'date' => ':attribute harus berupa tanggal yang valid.',
            'image' => ':attribute harus berupa gambar.',
            'mimes' => ':attribute harus berupa file dengan format: :values.',
            'size' => ':attribute harus berukuran :size karakter.',
            'between' => ':attribute harus antara :min dan :max.',
            'alpha' => ':attribute hanya boleh mengandung huruf.',
            'alpha_num' => ':attribute hanya boleh mengandung huruf dan angka.',
            'alpha_dash' => ':attribute hanya boleh mengandung huruf, angka, tanda hubung, dan garis bawah.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'nama',
            'email' => 'email',
            'password' => 'password',
            'password_confirmation' => 'konfirmasi password',
            'phone' => 'nomor telepon',
            'address' => 'alamat',
            'birth_date' => 'tanggal lahir',
            'gender' => 'jenis kelamin',
            'username' => 'username',
            'nisn' => 'NISN',
            'nip' => 'NIP',
            'user_type' => 'tipe pengguna',
            'avatar' => 'foto profil',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $input = $this->all();
        
        // Sanitize all string inputs
        $sanitized = $this->sanitizeInput($input);
        
        $this->replace($sanitized);
    }

    /**
     * Sanitize input data
     */
    protected function sanitizeInput(array $input): array
    {
        $sanitized = [];
        
        foreach ($input as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeInput($value);
            } elseif (is_string($value)) {
                $sanitized[$key] = $this->sanitizeString($value, $key);
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }

    /**
     * Sanitize string input
     */
    protected function sanitizeString(string $value, string $key): string
    {
        // Remove null bytes
        $value = str_replace("\0", '', $value);
        
        // Trim whitespace
        $value = trim($value);
        
        // Special handling for different field types
        switch ($key) {
            case 'email':
                return filter_var($value, FILTER_SANITIZE_EMAIL);
                
            case 'phone':
                // Remove non-numeric characters except + and -
                return preg_replace('/[^0-9+\-\s]/', '', $value);
                
            case 'name':
            case 'full_name':
            case 'father_name':
            case 'mother_name':
                // Only allow letters, spaces, apostrophes, and hyphens
                return preg_replace('/[^a-zA-Z\s\'\-]/', '', $value);
                
            case 'username':
                // Only allow alphanumeric, underscore, and hyphen
                return preg_replace('/[^a-zA-Z0-9_\-]/', '', $value);
                
            case 'nisn':
            case 'nip':
            case 'nik':
                // Only allow numeric
                return preg_replace('/[^0-9]/', '', $value);
                
            case 'password':
            case 'password_confirmation':
                // Don't sanitize passwords, just remove null bytes
                return str_replace("\0", '', $value);
                
            default:
                // General sanitization - remove potentially dangerous characters
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                
                // Remove script tags and other dangerous HTML
                $value = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $value);
                $value = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $value);
                $value = preg_replace('/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/mi', '', $value);
                $value = preg_replace('/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/mi', '', $value);
                
                // Remove javascript: and data: URLs
                $value = preg_replace('/javascript:/i', '', $value);
                $value = preg_replace('/data:/i', '', $value);
                
                return $value;
        }
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator): void
    {
        if ($this->expectsJson()) {
            throw new HttpResponseException(
                response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $validator->errors()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }

    /**
     * Get common validation rules
     */
    protected function getCommonRules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s\'\-]+$/'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s]+$/'],
            'address' => ['nullable', 'string', 'max:500'],
            'birth_date' => ['nullable', 'date', 'before:today'],
            'gender' => ['nullable', 'in:L,P'],
            'username' => ['nullable', 'string', 'max:50', 'regex:/^[a-zA-Z0-9_\-]+$/'],
            'nisn' => ['nullable', 'string', 'size:10', 'regex:/^[0-9]+$/'],
            'nip' => ['nullable', 'string', 'size:18', 'regex:/^[0-9]+$/'],
            'nik' => ['nullable', 'string', 'size:16', 'regex:/^[0-9]+$/'],
        ];
    }

    /**
     * Check for SQL injection patterns
     */
    protected function hasSqlInjection(string $value): bool
    {
        $patterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bcreate\b.*\btable\b)/i',
            '/(\balter\b.*\btable\b)/i',
            '/(\bexec\b|\bexecute\b)/i',
            '/(\bsp_\w+)/i',
            '/(\bxp_\w+)/i',
            '/(\'|\"|;|--|\#|\*|\/\*|\*\/)/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for XSS patterns
     */
    protected function hasXss(string $value): bool
    {
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
            '/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/mi',
            '/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/mi',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onclick\s*=/i',
            '/onerror\s*=/i',
            '/onmouseover\s*=/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for command injection patterns
     */
    protected function hasCommandInjection(string $value): bool
    {
        $patterns = [
            '/(\||&|;|`|\$\(|\$\{)/i',
            '/(rm\s|del\s|format\s|fdisk\s)/i',
            '/(wget\s|curl\s|nc\s|netcat\s)/i',
            '/(chmod\s|chown\s|sudo\s)/i',
            '/(cat\s|type\s|more\s|less\s)/i',
            '/(echo\s|print\s|printf\s)/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }
}
