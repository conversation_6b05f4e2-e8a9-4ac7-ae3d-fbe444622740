<?php $__env->startSection('title', 'Edit Fasilitas - ' . $facility->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Fasilitas</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="#">Manajemen Konten</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.content.facilities.index')); ?>">Fasilitas</a></li>
                    <li class="breadcrumb-item active">Edit Fasilitas</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?php echo e(route('admin.content.facilities.show', $facility)); ?>" class="btn btn-info me-2">
                <i class="fas fa-eye me-2"></i>Lihat Fasilitas
            </a>
            <a href="<?php echo e(route('admin.content.facilities.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Form Edit Fasilitas
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.content.facilities.update', $facility)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nama Fasilitas <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="name" name="name" value="<?php echo e(old('name', $facility->name)); ?>" required>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi <span class="text-danger">*</span></label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="description" name="description" rows="4" required><?php echo e(old('description', $facility->description)); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Kategori <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="category" name="category" required>
                                        <option value="">Pilih Kategori</option>
                                        <option value="academic" <?php echo e(old('category', $facility->category) == 'academic' ? 'selected' : ''); ?>>Akademik</option>
                                        <option value="sports" <?php echo e(old('category', $facility->category) == 'sports' ? 'selected' : ''); ?>>Olahraga</option>
                                        <option value="library" <?php echo e(old('category', $facility->category) == 'library' ? 'selected' : ''); ?>>Perpustakaan</option>
                                        <option value="laboratory" <?php echo e(old('category', $facility->category) == 'laboratory' ? 'selected' : ''); ?>>Laboratorium</option>
                                        <option value="dormitory" <?php echo e(old('category', $facility->category) == 'dormitory' ? 'selected' : ''); ?>>Asrama</option>
                                        <option value="cafeteria" <?php echo e(old('category', $facility->category) == 'cafeteria' ? 'selected' : ''); ?>>Kantin</option>
                                        <option value="mosque" <?php echo e(old('category', $facility->category) == 'mosque' ? 'selected' : ''); ?>>Masjid</option>
                                        <option value="other" <?php echo e(old('category', $facility->category) == 'other' ? 'selected' : ''); ?>>Lainnya</option>
                                    </select>
                                    <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Kapasitas</label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['capacity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="capacity" name="capacity" 
                                           value="<?php echo e(old('capacity', $facility->capacity)); ?>" 
                                           min="1" placeholder="Contoh: 50">
                                    <?php $__errorArgs = ['capacity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="specifications" class="form-label">Spesifikasi</label>
                            <textarea class="form-control <?php $__errorArgs = ['specifications'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="specifications" name="specifications" rows="4"><?php echo e(old('specifications', $facility->specifications)); ?></textarea>
                            <?php $__errorArgs = ['specifications'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="features" class="form-label">Fitur-fitur</label>
                            <div id="features-container">
                                <?php if($facility->features && is_array($facility->features) && count($facility->features) > 0): ?>
                                    <?php $__currentLoopData = $facility->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="input-group mb-2 feature-item">
                                            <input type="text" class="form-control" name="features[]" value="<?php echo e($feature); ?>" placeholder="Masukkan fitur">
                                            <button type="button" class="btn btn-outline-danger remove-feature">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php elseif($facility->features && is_string($facility->features) && !empty($facility->features)): ?>
                                    <div class="input-group mb-2 feature-item">
                                        <input type="text" class="form-control" name="features[]" value="<?php echo e($facility->features); ?>" placeholder="Masukkan fitur">
                                        <button type="button" class="btn btn-outline-danger remove-feature">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="input-group mb-2 feature-item">
                                        <input type="text" class="form-control" name="features[]" placeholder="Masukkan fitur">
                                        <button type="button" class="btn btn-outline-danger remove-feature">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="add-feature">
                                <i class="fas fa-plus me-1"></i>Tambah Fitur
                            </button>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Gambar Fasilitas</label>
                            <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="image" name="image" accept="image/*">
                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="form-text text-muted">Format: JPG, PNG, GIF. Maksimal 2MB.</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_available" name="is_available" value="1" 
                                               <?php echo e(old('is_available', $facility->is_available) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_available">
                                            Fasilitas Tersedia
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" 
                                               <?php echo e(old('is_featured', $facility->is_featured) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_featured">
                                            Fasilitas Unggulan
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="<?php echo e(route('admin.content.facilities.index')); ?>" class="btn btn-secondary me-2">Batal</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Fasilitas
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current Image Preview -->
        <div class="col-lg-4">
            <?php if($facility->image): ?>
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-image me-2"></i>Gambar Saat Ini
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="position-relative d-inline-block">
                            <img src="<?php echo e(Storage::url($facility->image)); ?>" 
                                 alt="<?php echo e($facility->name); ?>" 
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 200px;">
                            <button type="button" 
                                    class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                                    onclick="removeImage('<?php echo e($facility->slug); ?>')"
                                    title="Hapus Gambar">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Klik X untuk menghapus gambar</small>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Help Card -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Bantuan
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="mb-0 ps-3">
                            <li>Nama fasilitas harus unik dan deskriptif</li>
                            <li>Pilih kategori yang sesuai untuk memudahkan pencarian</li>
                            <li>Kapasitas menunjukkan jumlah maksimal pengguna</li>
                            <li>Spesifikasi berisi detail teknis fasilitas</li>
                            <li>Fitur-fitur adalah keunggulan yang dimiliki</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Add feature functionality
    $('#add-feature').click(function() {
        const newFeature = `
            <div class="input-group mb-2 feature-item">
                <input type="text" class="form-control" name="features[]" placeholder="Masukkan fitur">
                <button type="button" class="btn btn-outline-danger remove-feature">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        $('#features-container').append(newFeature);
    });

    // Remove feature functionality
    $(document).on('click', '.remove-feature', function() {
        if ($('.feature-item').length > 1) {
            $(this).closest('.feature-item').remove();
        }
    });
});

function removeImage(facilitySlug) {
    Swal.fire({
        title: 'Hapus Gambar?',
        text: 'Apakah Anda yakin ingin menghapus gambar ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus gambar',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // AJAX call to remove image
            $.ajax({
                url: `/admin/content/facilities/${facilitySlug}/remove-image`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });

                        // Remove the current image section
                        $('.card:has(img)').fadeOut(300, function() {
                            $(this).remove();
                        });
                    } else {
                        Swal.fire({
                            title: 'Gagal!',
                            text: response.message,
                            icon: 'error'
                        });
                    }
                },
                error: function(xhr) {
                    let message = 'Terjadi kesalahan saat menghapus gambar.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        title: 'Error!',
                        text: message,
                        icon: 'error'
                    });
                }
            });
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/content/facilities/edit.blade.php ENDPATH**/ ?>