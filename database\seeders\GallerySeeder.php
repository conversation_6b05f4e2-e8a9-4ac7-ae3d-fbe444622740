<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Gallery;
use App\Models\User;

class GallerySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUser = User::where('email', '<EMAIL>')->first();

        if (!$adminUser) {
            return;
        }

        // Sample gallery data with different categories
        $galleryData = [
            // Kegiatan Sekolah
            [
                'title' => 'Upacara Bendera Senin',
                'description' => 'Kegiatan rutin upacara bendera setiap hari Senin',
                'file_path' => 'gallery/upacara-bendera.jpg',
                'file_name' => 'upacara-bendera.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'kegiatan',
                'album' => 'Kegiatan Rutin',
                'is_featured' => true,
                'order' => 1,
            ],
            [
                'title' => 'Pembelajaran di Kelas',
                'description' => 'Suasana pembelajaran aktif di dalam kelas',
                'file_path' => 'gallery/pembelajaran-kelas.jpg',
                'file_name' => 'pembelajaran-kelas.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'kegiatan',
                'album' => 'Kegiatan Belajar',
                'is_featured' => true,
                'order' => 2,
            ],
            [
                'title' => 'Praktikum Laboratorium',
                'description' => 'Siswa melakukan praktikum di laboratorium sains',
                'file_path' => 'gallery/praktikum-lab.jpg',
                'file_name' => 'praktikum-lab.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'kegiatan',
                'album' => 'Kegiatan Belajar',
                'is_featured' => true,
                'order' => 3,
            ],

            // Fasilitas Sekolah
            [
                'title' => 'Perpustakaan Modern',
                'description' => 'Fasilitas perpustakaan dengan koleksi buku lengkap',
                'file_path' => 'gallery/perpustakaan.jpg',
                'file_name' => 'perpustakaan.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'fasilitas',
                'album' => 'Fasilitas Akademik',
                'is_featured' => true,
                'order' => 4,
            ],
            [
                'title' => 'Laboratorium Komputer',
                'description' => 'Lab komputer dengan perangkat terbaru',
                'file_path' => 'gallery/lab-komputer.jpg',
                'file_name' => 'lab-komputer.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'fasilitas',
                'album' => 'Fasilitas Akademik',
                'is_featured' => true,
                'order' => 5,
            ],
            [
                'title' => 'Lapangan Olahraga',
                'description' => 'Lapangan serbaguna untuk berbagai cabang olahraga',
                'file_path' => 'gallery/lapangan-olahraga.jpg',
                'file_name' => 'lapangan-olahraga.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'fasilitas',
                'album' => 'Fasilitas Olahraga',
                'is_featured' => true,
                'order' => 6,
            ],

            // Event & Prestasi
            [
                'title' => 'Penerimaan Siswa Baru',
                'description' => 'Kegiatan penerimaan siswa baru tahun ajaran 2024/2025',
                'file_path' => 'gallery/psb-2024.jpg',
                'file_name' => 'psb-2024.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'event',
                'album' => 'Event 2024',
                'is_featured' => true,
                'order' => 7,
            ],
            [
                'title' => 'Juara Olimpiade Sains',
                'description' => 'Siswa meraih juara dalam olimpiade sains nasional',
                'file_path' => 'gallery/juara-olimpiade.jpg',
                'file_name' => 'juara-olimpiade.jpg',
                'file_type' => 'image',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024000,
                'category' => 'prestasi',
                'album' => 'Prestasi 2024',
                'is_featured' => true,
                'order' => 8,
            ],
        ];

        foreach ($galleryData as $data) {
            $data['uploaded_by'] = $adminUser->id;
            Gallery::create($data);
        }
    }
}
