<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\News;
use App\Models\Program;
use App\Models\Facility;
use App\Models\Gallery;
use App\Models\StaffProfile;
use App\Models\StudentProfile;
use App\Models\ParentProfile;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $user = Auth::user();

        // Debug logging
        \Log::info('Dashboard access attempt', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_type' => $user->user_type,
            'roles' => $user->roles->pluck('name')->toArray()
        ]);

        // Redirect based on user role (using Spatie Permission roles)
        if ($user->hasRole('super_admin')) {
            \Log::info('Redirecting to superadmin dashboard');
            return $this->superadminDashboard();
        } elseif ($user->hasRole('admin')) {
            \Log::info('Redirecting to admin dashboard');
            return $this->adminDashboardPrivate();
        } elseif ($user->hasRole('guru')) {
            \Log::info('Redirecting to guru dashboard');
            return $this->guruDashboard();
        } elseif ($user->hasRole('siswa')) {
            \Log::info('Redirecting to siswa dashboard');
            return $this->siswaDashboard();
        } elseif ($user->hasRole('orang_tua')) {
            \Log::info('Redirecting to orangtua dashboard');
            return $this->orangtuaDashboard();
        } elseif ($user->hasRole('calon_siswa')) {
            \Log::info('Redirecting to calon siswa dashboard');
            return $this->calonSiswaDashboard();
        }

        // Default fallback
        \Log::warning('No matching role found, redirecting to landing', [
            'user_roles' => $user->roles->pluck('name')->toArray()
        ]);
        return redirect()->route('landing');
    }

    private function superadminDashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'total_students' => User::where('user_type', 'siswa')->count(),
            'total_teachers' => User::where('user_type', 'guru')->count(),
            'total_parents' => User::where('user_type', 'orang_tua')->count(),
            'total_news' => News::count(),
            'total_programs' => Program::count(),
            'total_facilities' => Facility::count(),
            'total_gallery' => Gallery::count(),
        ];

        $recentUsers = User::latest()->take(5)->get();
        $recentNews = News::latest()->take(5)->get();

        return view('dashboard.superadmin', compact('stats', 'recentUsers', 'recentNews'));
    }

    public function adminDashboard()
    {
        $stats = [
            'total_students' => User::where('user_type', 'siswa')->count(),
            'total_teachers' => User::where('user_type', 'guru')->count(),
            'total_news' => News::count(),
            'total_programs' => Program::count(),
        ];

        $recentNews = News::latest()->take(5)->get();
        $recentStudents = User::where('user_type', 'siswa')->latest()->take(5)->get();

        return view('dashboard.admin', compact('stats', 'recentNews', 'recentStudents'));
    }

    private function adminDashboardPrivate()
    {
        return $this->adminDashboard();
    }

    private function guruDashboard()
    {
        $user = Auth::user();
        $staffProfile = StaffProfile::where('user_id', $user->id)->first();

        // Stats specific to teacher portal
        $stats = [
            'my_students' => User::where('user_type', 'siswa')->where('is_active', true)->count(),
            'my_classes' => 0, // Will be implemented with class management
            'pending_assignments' => 0, // Will be implemented with assignment system
            'my_subjects' => $staffProfile ? ($staffProfile->subject ? 1 : 0) : 0,
            'my_news' => News::where('author_id', $user->id)->count(),
            'total_programs' => Program::count(),
        ];

        $myNews = News::where('author_id', $user->id)->latest()->take(5)->get();
        $recentStudents = User::where('user_type', 'siswa')->where('is_active', true)->latest()->take(5)->get();

        // Teacher-specific data
        $todaySchedule = []; // Will be implemented with schedule system
        $pendingGrades = []; // Will be implemented with grading system

        return view('dashboard.guru', compact('stats', 'myNews', 'recentStudents', 'staffProfile', 'todaySchedule', 'pendingGrades'));
    }

    private function siswaDashboard()
    {
        $user = Auth::user();
        $studentProfile = StudentProfile::where('user_id', $user->id)->first();

        // Stats specific to student portal
        $stats = [
            'my_subjects' => 0, // Will be implemented with subject enrollment
            'pending_assignments' => 0, // Will be implemented with assignment system
            'completed_assignments' => 0, // Will be implemented with assignment system
            'average_grade' => 0, // Will be implemented with grading system
            'attendance_rate' => 0, // Will be implemented with attendance system
            'total_teachers' => User::where('user_type', 'guru')->where('is_active', true)->count(),
        ];

        $latestNews = News::where('status', 'published')->latest()->take(5)->get();
        $myPrograms = Program::where('is_active', true)->get();

        // Student-specific data
        $todaySchedule = []; // Will be implemented with schedule system
        $recentGrades = []; // Will be implemented with grading system
        $upcomingAssignments = []; // Will be implemented with assignment system

        return view('dashboard.siswa', compact('stats', 'latestNews', 'myPrograms', 'studentProfile', 'todaySchedule', 'recentGrades', 'upcomingAssignments'));
    }

    private function orangtuaDashboard()
    {
        $user = Auth::user();
        $parentProfile = ParentProfile::where('user_id', $user->id)->first();

        // Get children data if parent profile exists
        $children = [];
        if ($parentProfile) {
            $children = User::where('user_type', 'siswa')
                          ->whereHas('studentProfile', function($query) use ($parentProfile) {
                              $query->where('parent_id', $parentProfile->id);
                          })
                          ->get();
        }

        // Stats specific to parent portal
        $stats = [
            'my_children' => count($children),
            'total_teachers' => User::where('user_type', 'guru')->where('is_active', true)->count(),
            'unread_messages' => 0, // Will be implemented with messaging system
            'upcoming_events' => 0, // Will be implemented with event system
            'pending_payments' => 0, // Will be implemented with payment system
            'total_news' => News::where('status', 'published')->count(),
        ];

        $latestNews = News::where('status', 'published')->latest()->take(5)->get();

        // Parent-specific data
        $childrenProgress = []; // Will be implemented with progress tracking
        $upcomingEvents = []; // Will be implemented with event system
        $recentMessages = []; // Will be implemented with messaging system

        return view('dashboard.orangtua', compact('stats', 'latestNews', 'children', 'parentProfile', 'childrenProgress', 'upcomingEvents', 'recentMessages'));
    }

    private function calonSiswaDashboard()
    {
        $user = Auth::user();

        // Stats specific to admission portal
        $stats = [
            'application_status' => 'pending', // Will be implemented with admission system
            'required_documents' => 5, // Will be implemented with document system
            'uploaded_documents' => 0, // Will be implemented with document system
            'test_schedule' => null, // Will be implemented with test scheduling
            'admission_phase' => 'registration', // Will be implemented with admission phases
            'total_programs' => Program::where('is_active', true)->count(),
        ];

        $availablePrograms = Program::where('is_active', true)->get();
        $admissionNews = News::where('status', 'published')
                            ->where('type', 'announcement')
                            ->latest()
                            ->take(5)
                            ->get();

        // Admission-specific data
        $admissionSteps = [
            ['step' => 1, 'title' => 'Registrasi Online', 'status' => 'completed'],
            ['step' => 2, 'title' => 'Upload Dokumen', 'status' => 'pending'],
            ['step' => 3, 'title' => 'Tes Masuk', 'status' => 'waiting'],
            ['step' => 4, 'title' => 'Pengumuman', 'status' => 'waiting'],
            ['step' => 5, 'title' => 'Daftar Ulang', 'status' => 'waiting'],
        ];

        $requiredDocuments = [
            ['name' => 'Ijazah/SKHUN', 'uploaded' => false],
            ['name' => 'Kartu Keluarga', 'uploaded' => false],
            ['name' => 'Akta Kelahiran', 'uploaded' => false],
            ['name' => 'Pas Foto 3x4', 'uploaded' => false],
            ['name' => 'Surat Keterangan Sehat', 'uploaded' => false],
        ];

        return view('dashboard.calon-siswa', compact('stats', 'availablePrograms', 'admissionNews', 'admissionSteps', 'requiredDocuments'));
    }
}
