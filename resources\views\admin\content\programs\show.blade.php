@extends('layouts.dashboard')

@section('title', 'Detail Program - ' . $program->name)

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Detail Program</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="#">Manajemen Konten</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.content.programs.index') }}">Program Pendidikan</a></li>
                    <li class="breadcrumb-item active">{{ $program->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.content.programs.edit', $program) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-2"></i>Edit Program
            </a>
            <a href="{{ route('admin.content.programs.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Program Details -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>{{ $program->name }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($program->image)
                        <div class="mb-4">
                            <img src="{{ Storage::url($program->image) }}" 
                                 alt="{{ $program->name }}" 
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 400px; width: 100%; object-fit: cover;">
                        </div>
                    @endif

                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Deskripsi Program</h6>
                        <div class="content-text">
                            {!! nl2br(e($program->description)) !!}
                        </div>
                    </div>

                    @if($program->curriculum)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Kurikulum</h6>
                            <div class="content-text">
                                {!! nl2br(e($program->curriculum)) !!}
                            </div>
                        </div>
                    @endif

                    @if($program->requirements)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Persyaratan</h6>
                            <div class="content-text">
                                {!! nl2br(e($program->requirements)) !!}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Program Info -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informasi Program
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Nama Program</small>
                        <div class="fw-bold">{{ $program->name }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Durasi</small>
                        <div class="fw-bold">{{ $program->duration ?? 'Tidak ditentukan' }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Level</small>
                        <div class="fw-bold">{{ $program->level ?? 'Tidak ditentukan' }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Status</small>
                        <div>
                            @if($program->is_active)
                                <span class="badge bg-success">Aktif</span>
                            @else
                                <span class="badge bg-secondary">Tidak Aktif</span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Dibuat</small>
                        <div class="fw-bold">{{ $program->created_at->format('d M Y, H:i') }}</div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Terakhir Diperbarui</small>
                        <div class="fw-bold">{{ $program->updated_at->format('d M Y, H:i') }}</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Aksi
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.content.programs.edit', $program) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit Program
                        </a>
                        <button type="button" class="btn btn-danger" onclick="deleteProgram({{ $program->id }}, '{{ addslashes($program->name) }}')">
                            <i class="fas fa-trash me-2"></i>Hapus Program
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
@endsection

@push('styles')
<style>
.content-text {
    line-height: 1.6;
    color: #495057;
}

.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteProgram(id, name) {
    Swal.fire({
        title: 'Hapus Program?',
        text: `Apakah Anda yakin ingin menghapus program "${name}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.getElementById('deleteForm');
            form.action = `/admin/content/programs/${id}`;
            form.submit();
        }
    });
}
</script>
@endpush
