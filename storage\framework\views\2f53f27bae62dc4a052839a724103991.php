<?php $__env->startSection('title', 'Manajemen Galeri'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Manajemen Galeri</h1>
    <a href="<?php echo e(route('admin.content.gallery.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Upload File
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-primary">
                <i class="fas fa-images"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_images']); ?></div>
            <div class="stats-label">Total Gambar</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-success">
                <i class="fas fa-video"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['total_videos']); ?></div>
            <div class="stats-label">Total Video</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon bg-gradient-warning">
                <i class="fas fa-star"></i>
            </div>
            <div class="stats-number"><?php echo e($stats['featured']); ?></div>
            <div class="stats-label">Featured</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.content.gallery.index')); ?>" class="row g-3">
            <div class="col-md-2">
                <label for="category" class="form-label">Kategori</label>
                <input type="text" name="category" id="category" class="form-control" 
                       placeholder="Kategori..." 
                       value="<?php echo e(request('category')); ?>">
            </div>
            <div class="col-md-2">
                <label for="album" class="form-label">Album</label>
                <select name="album" id="album" class="form-select">
                    <option value="">Semua Album</option>
                    <?php $__currentLoopData = $albums; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $album): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($album); ?>" <?php echo e(request('album') == $album ? 'selected' : ''); ?>><?php echo e($album); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="col-md-5">
                <label for="search" class="form-label">Pencarian</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Cari judul atau deskripsi..." 
                       value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="<?php echo e(route('admin.content.gallery.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Gallery Grid -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Galeri Media</h5>
    </div>
    <div class="card-body">
        <?php if($gallery->count() > 0): ?>
            <div class="row">
                <?php $__currentLoopData = $gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="gallery-item">
                        <div class="gallery-media">
                            <?php if($item->file_type == 'image'): ?>
                                <img src="<?php echo e(Storage::url($item->file_path)); ?>" alt="<?php echo e($item->title); ?>" class="img-fluid">
                                <div class="media-overlay">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php else: ?>
                                <div class="video-placeholder">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                    <p class="mt-2"><?php echo e($item->file_name); ?></p>
                                </div>
                                <div class="media-overlay">
                                    <i class="fas fa-video"></i>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($item->is_featured): ?>
                                <div class="featured-badge">
                                    <i class="fas fa-star"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="gallery-info">
                            <h6 class="gallery-title"><?php echo e(Str::limit($item->title, 30)); ?></h6>
                            <p class="gallery-description"><?php echo e(Str::limit($item->description, 50)); ?></p>
                            
                            <div class="gallery-meta">
                                <?php if($item->category): ?>
                                    <span class="badge bg-secondary"><?php echo e($item->category); ?></span>
                                <?php endif; ?>
                                <?php if($item->album): ?>
                                    <span class="badge bg-info"><?php echo e($item->album); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="gallery-actions mt-2">
                                <div class="btn-group w-100" role="group">
                                    <a href="<?php echo e(route('admin.content.gallery.show', $item)); ?>" class="btn btn-sm btn-outline-info" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.content.gallery.edit', $item)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteGallery('<?php echo e($item->id); ?>', '<?php echo e(addslashes($item->title)); ?>')"
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="gallery-footer">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i><?php echo e($item->uploader->name ?? 'Unknown'); ?>

                                    <br>
                                    <i class="fas fa-calendar me-1"></i><?php echo e($item->created_at->format('d M Y')); ?>

                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Menampilkan <?php echo e($gallery->firstItem()); ?> - <?php echo e($gallery->lastItem()); ?> dari <?php echo e($gallery->total()); ?> file
                </div>
                <?php echo e($gallery->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada file ditemukan</h5>
                <p class="text-muted">Belum ada file yang sesuai dengan filter yang dipilih.</p>
                <a href="<?php echo e(route('admin.content.gallery.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Upload File Pertama
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.gallery-item {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-media {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.gallery-media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.media-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 8px;
    border-radius: 5px;
    font-size: 12px;
}

.featured-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ffc107;
    color: white;
    padding: 5px 8px;
    border-radius: 5px;
    font-size: 12px;
}

.gallery-info {
    padding: 15px;
}

.gallery-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
}

.gallery-description {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 10px;
}

.gallery-meta .badge {
    font-size: 10px;
    margin-right: 5px;
}

.gallery-footer {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9rem;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteGallery(id, title) {
    Swal.fire({
        title: 'Hapus Galeri?',
        text: `Apakah Anda yakin ingin menghapus galeri "${title}"? Tindakan ini tidak dapat dibatalkan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Sedang menghapus galeri',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send AJAX DELETE request
            $.ajax({
                url: `/admin/content/gallery/${id}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Galeri berhasil dihapus.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    console.error('Delete error:', xhr);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Gagal menghapus galeri: ' + (xhr.responseJSON?.message || 'Unknown error'),
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\sekolahku\resources\views/admin/content/gallery/index.blade.php ENDPATH**/ ?>