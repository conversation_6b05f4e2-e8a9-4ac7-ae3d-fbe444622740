<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Content Management
            'view content',
            'create content',
            'edit content',
            'delete content',
            'publish content',

            // News Management
            'view news',
            'create news',
            'edit news',
            'delete news',
            'publish news',

            // Program Management
            'view programs',
            'create programs',
            'edit programs',
            'delete programs',

            // Facility Management
            'view facilities',
            'create facilities',
            'edit facilities',
            'delete facilities',

            // Gallery Management
            'view gallery',
            'create gallery',
            'edit gallery',
            'delete gallery',

            // Admission Management
            'view admissions',
            'create admissions',
            'edit admissions',
            'delete admissions',
            'review admissions',
            'approve admissions',

            // Staff Management
            'view staff',
            'create staff',
            'edit staff',
            'delete staff',

            // Settings Management
            'view settings',
            'edit settings',

            // Security Management
            'view security logs',
            'manage security',

            // Dashboard Access
            'access admin dashboard',
            'access teacher dashboard',
            'access student dashboard',
            'access parent dashboard',
            'access admission dashboard',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - Full access
        $superAdmin = Role::create(['name' => 'super_admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - Content and user management
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo([
            'view users', 'create users', 'edit users',
            'view content', 'create content', 'edit content', 'delete content', 'publish content',
            'view news', 'create news', 'edit news', 'delete news', 'publish news',
            'view programs', 'create programs', 'edit programs', 'delete programs',
            'view facilities', 'create facilities', 'edit facilities', 'delete facilities',
            'view gallery', 'create gallery', 'edit gallery', 'delete gallery',
            'view admissions', 'edit admissions', 'review admissions', 'approve admissions',
            'view staff', 'create staff', 'edit staff',
            'view settings', 'edit settings',
            'access admin dashboard',
        ]);

        // Guru - Limited content management
        $guru = Role::create(['name' => 'guru']);
        $guru->givePermissionTo([
            'view content', 'create content', 'edit content',
            'view news', 'create news', 'edit news',
            'view programs',
            'view facilities',
            'view gallery', 'create gallery',
            'access teacher dashboard',
        ]);

        // Admission Officer - Admission management
        $admissionOfficer = Role::create(['name' => 'admission_officer']);
        $admissionOfficer->givePermissionTo([
            'view admissions', 'create admissions', 'edit admissions', 'review admissions', 'approve admissions',
            'view programs',
            'access admin dashboard',
        ]);

        // Siswa - View only
        $siswa = Role::create(['name' => 'siswa']);
        $siswa->givePermissionTo([
            'view content',
            'view news',
            'view programs',
            'view facilities',
            'view gallery',
            'access student dashboard',
        ]);

        // Orang Tua - View student related content
        $orangTua = Role::create(['name' => 'orang_tua']);
        $orangTua->givePermissionTo([
            'view content',
            'view news',
            'view programs',
            'access parent dashboard',
        ]);

        // Calon Siswa - Admission related
        $calonSiswa = Role::create(['name' => 'calon_siswa']);
        $calonSiswa->givePermissionTo([
            'view content',
            'view news',
            'view programs',
            'view facilities',
            'create admissions',
            'edit admissions',
            'access admission dashboard',
        ]);

        // Create default super admin user
        $superAdminUser = User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'username' => 'superadmin',
            'user_type' => 'super_admin',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $superAdminUser->assignRole('super_admin');

        // Create default admin user
        $adminUser = User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'username' => 'admin',
            'user_type' => 'admin',
            'password' => Hash::make('admin123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $adminUser->assignRole('admin');
    }
}
