<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SchoolSetting;
use App\Models\HeroSection;
use Illuminate\Support\Facades\Storage;

class SchoolSettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:super_admin');
    }

    public function index()
    {
        $schoolSetting = SchoolSetting::first();
        $heroSections = HeroSection::orderBy('order')->get();

        return view('admin.settings.index', compact('schoolSetting', 'heroSections'));
    }

    public function updateSchoolInfo(Request $request)
    {
        $request->validate([
            'school_name' => 'required|string|max:255',
            'school_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'school_address' => 'required|string',
            'school_phone' => 'required|string|max:20',
            'school_email' => 'required|email|max:255',
            'school_website' => 'nullable|url|max:255',
            'school_description' => 'required|string',
            'vision' => 'required|string',
            'mission' => 'required|string',
            'values' => 'nullable|string',
            'history' => 'nullable|string',
            'principal_name' => 'required|string|max:255',
            'principal_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'principal_message' => 'nullable|string',
            'accreditation' => 'nullable|string|max:10',
            'established_year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'total_students' => 'nullable|integer|min:0',
            'total_teachers' => 'nullable|integer|min:0',
            'social_media' => 'nullable|array',
            'contact_info' => 'nullable|array',
        ]);

        $schoolSetting = SchoolSetting::first();
        if (!$schoolSetting) {
            $schoolSetting = new SchoolSetting();
        }

        $data = $request->except(['school_logo', 'principal_photo']);

        // Handle school logo upload
        if ($request->hasFile('school_logo')) {
            if ($schoolSetting->school_logo) {
                Storage::disk('public')->delete($schoolSetting->school_logo);
            }
            $data['school_logo'] = $request->file('school_logo')->store('school', 'public');
        }

        // Handle principal photo upload
        if ($request->hasFile('principal_photo')) {
            if ($schoolSetting->principal_photo) {
                Storage::disk('public')->delete($schoolSetting->principal_photo);
            }
            $data['principal_photo'] = $request->file('principal_photo')->store('school', 'public');
        }

        $data['is_active'] = true;

        if ($schoolSetting->exists) {
            $schoolSetting->update($data);
        } else {
            SchoolSetting::create($data);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Informasi sekolah berhasil diperbarui.');
    }

    public function heroSections()
    {
        $heroSections = HeroSection::orderBy('order')->get();
        return view('admin.settings.hero-sections', compact('heroSections'));
    }

    public function createHeroSection()
    {
        return view('admin.settings.hero-create');
    }

    public function storeHeroSection(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video_url' => 'nullable|url',
            'button_text' => 'nullable|string|max:50',
            'button_link' => 'nullable|string|max:255',
            'order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        $data['is_active'] = $request->boolean('is_active', true);

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('hero', 'public');
        }

        HeroSection::create($data);

        return redirect()->route('admin.settings.hero-sections')
            ->with('success', 'Hero section berhasil ditambahkan.');
    }

    public function editHeroSection(HeroSection $heroSection)
    {
        return view('admin.settings.hero-edit', compact('heroSection'));
    }

    public function updateHeroSection(Request $request, HeroSection $heroSection)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video_url' => 'nullable|url',
            'button_text' => 'nullable|string|max:50',
            'button_link' => 'nullable|string|max:255',
            'order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->except('image');
        $data['is_active'] = $request->boolean('is_active', true);

        if ($request->hasFile('image')) {
            if ($heroSection->image) {
                Storage::disk('public')->delete($heroSection->image);
            }
            $data['image'] = $request->file('image')->store('hero', 'public');
        }

        $heroSection->update($data);

        return redirect()->route('admin.settings.hero-sections')
            ->with('success', 'Hero section berhasil diperbarui.');
    }

    public function destroyHeroSection(HeroSection $heroSection)
    {
        if ($heroSection->image) {
            Storage::disk('public')->delete($heroSection->image);
        }

        $heroSection->delete();

        return redirect()->route('admin.settings.hero-sections')
            ->with('success', 'Hero section berhasil dihapus.');
    }

    public function updateHeroOrder(Request $request)
    {
        $request->validate([
            'hero_sections' => 'required|array',
            'hero_sections.*.id' => 'required|exists:hero_sections,id',
            'hero_sections.*.order' => 'required|integer|min:0',
        ]);

        foreach ($request->hero_sections as $heroData) {
            HeroSection::where('id', $heroData['id'])
                ->update(['order' => $heroData['order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Urutan hero section berhasil diperbarui.'
        ]);
    }

    public function toggleHeroStatus(HeroSection $heroSection)
    {
        $heroSection->update(['is_active' => !$heroSection->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Status hero section berhasil diubah.',
            'is_active' => $heroSection->is_active
        ]);
    }

    public function removeHeroImage(HeroSection $heroSection)
    {
        if ($heroSection->image) {
            Storage::disk('public')->delete($heroSection->image);
            $heroSection->update(['image' => null]);

            return response()->json([
                'success' => true,
                'message' => 'Gambar hero section berhasil dihapus.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Tidak ada gambar untuk dihapus.'
        ]);
    }

    public function systemSettings()
    {
        $schoolSetting = SchoolSetting::first();
        return view('admin.settings.system', compact('schoolSetting'));
    }

    public function updateSystemSettings(Request $request)
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'timezone' => 'required|string|in:Asia/Jakarta,Asia/Makassar,Asia/Jayapura',
            'maintenance_mode' => 'boolean',
            'maintenance_message' => 'nullable|string|max:1000',
            'registration_enabled' => 'boolean',
            'email_verification_required' => 'boolean',
        ]);

        // Get or create school settings
        $schoolSetting = SchoolSetting::first();
        if (!$schoolSetting) {
            $schoolSetting = new SchoolSetting();
        }

        // Update maintenance mode settings
        $schoolSetting->maintenance_mode = $request->has('maintenance_mode');
        $schoolSetting->maintenance_message = $request->maintenance_message;
        $schoolSetting->save();

        // Update timezone in config if changed
        if ($request->timezone !== config('app.timezone')) {
            $this->updateEnvFile('APP_TIMEZONE', $request->timezone);

            // Set timezone for current request
            config(['app.timezone' => $request->timezone]);
            date_default_timezone_set($request->timezone);
        }

        // Update other env settings if needed
        if ($request->app_name !== config('app.name')) {
            $this->updateEnvFile('APP_NAME', '"' . $request->app_name . '"');
        }

        if ($request->app_url !== config('app.url')) {
            $this->updateEnvFile('APP_URL', $request->app_url);
        }

        // Log system settings update
        \App\Models\SecurityLog::logEvent('system_settings_updated', auth()->id(), [
            'changes' => [
                'timezone' => $request->timezone,
                'maintenance_mode' => $request->has('maintenance_mode'),
                'app_name' => $request->app_name,
                'app_url' => $request->app_url,
            ],
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        $message = $request->has('maintenance_mode')
            ? 'Mode maintenance diaktifkan. Website sekarang dalam mode maintenance.'
            : 'Pengaturan sistem berhasil diperbarui.';

        return redirect()->route('admin.settings.system')
            ->with('success', $message);
    }

    private function updateEnvFile($key, $value)
    {
        $envFile = base_path('.env');

        if (file_exists($envFile)) {
            $envContent = file_get_contents($envFile);

            // Check if key exists
            if (preg_match("/^{$key}=.*/m", $envContent)) {
                // Update existing key
                $envContent = preg_replace("/^{$key}=.*/m", "{$key}={$value}", $envContent);
            } else {
                // Add new key
                $envContent .= "\n{$key}={$value}";
            }

            file_put_contents($envFile, $envContent);
        }
    }

    public function toggleMaintenance(Request $request)
    {
        $schoolSetting = SchoolSetting::first();
        if (!$schoolSetting) {
            $schoolSetting = new SchoolSetting();
        }

        $schoolSetting->maintenance_mode = !$schoolSetting->maintenance_mode;
        $schoolSetting->save();

        $status = $schoolSetting->maintenance_mode ? 'diaktifkan' : 'dinonaktifkan';

        return response()->json([
            'success' => true,
            'message' => "Mode maintenance berhasil {$status}.",
            'maintenance_mode' => $schoolSetting->maintenance_mode
        ]);
    }

    public function clearCache(Request $request)
    {
        $request->validate([
            'type' => 'required|in:config,route,view,all,application'
        ]);

        $type = $request->type;
        $success = false;
        $message = '';

        try {
            switch ($type) {
                case 'config':
                    \Artisan::call('config:clear');
                    \Artisan::call('config:cache');
                    $message = 'Config cache berhasil dibersihkan dan di-cache ulang.';
                    $success = true;
                    break;

                case 'route':
                    \Artisan::call('route:clear');
                    \Artisan::call('route:cache');
                    $message = 'Route cache berhasil dibersihkan dan di-cache ulang.';
                    $success = true;
                    break;

                case 'view':
                    \Artisan::call('view:clear');
                    $message = 'View cache berhasil dibersihkan.';
                    $success = true;
                    break;

                case 'application':
                    \Artisan::call('cache:clear');
                    $message = 'Application cache berhasil dibersihkan.';
                    $success = true;
                    break;

                case 'all':
                    \Artisan::call('config:clear');
                    \Artisan::call('route:clear');
                    \Artisan::call('view:clear');
                    \Artisan::call('cache:clear');
                    \Artisan::call('optimize:clear');
                    $message = 'Semua cache berhasil dibersihkan.';
                    $success = true;
                    break;
            }

            // Log cache clearing activity
            \App\Models\SecurityLog::logEvent('cache_cleared', auth()->id(), [
                'cache_type' => $type,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

        } catch (\Exception $e) {
            $message = 'Gagal membersihkan cache: ' . $e->getMessage();
            $success = false;
        }

        return response()->json([
            'success' => $success,
            'message' => $message,
            'type' => $type
        ]);
    }

    public function optimizeApplication(Request $request)
    {
        try {
            // Clear all caches first
            \Artisan::call('optimize:clear');

            // Then optimize
            \Artisan::call('optimize');

            // Log optimization activity
            \App\Models\SecurityLog::logEvent('application_optimized', auth()->id(), [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Aplikasi berhasil dioptimasi. Cache config, route, dan view telah di-generate ulang.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengoptimasi aplikasi: ' . $e->getMessage()
            ]);
        }
    }

    public function createBackup(Request $request)
    {
        try {
            // Run backup command
            \Artisan::call('backup:database');
            $output = \Artisan::output();

            // Get backup file info
            $backupPath = storage_path('app/backups');
            $files = glob($backupPath . '/backup_database_*.sql');
            $latestFile = '';
            $fileSize = 0;

            if (!empty($files)) {
                $latestFile = basename(end($files));
                $fileSize = filesize(end($files));
            }

            // Log backup activity
            \App\Models\SecurityLog::logEvent('database_backup_created', auth()->id(), [
                'backup_file' => $latestFile,
                'file_size' => $fileSize,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Backup database berhasil dibuat.',
                'backup_file' => $latestFile,
                'file_size' => $this->formatBytes($fileSize)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat backup: ' . $e->getMessage()
            ]);
        }
    }

    public function runMaintenance(Request $request)
    {
        $request->validate([
            'type' => 'required|in:basic,full'
        ]);

        try {
            $type = $request->type;
            $command = $type === 'full' ? 'maintenance:run --full' : 'maintenance:run';

            // Run maintenance command
            \Artisan::call($command);
            $output = \Artisan::output();

            // Log maintenance activity
            \App\Models\SecurityLog::logEvent('maintenance_executed', auth()->id(), [
                'maintenance_type' => $type,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'message' => "Maintenance {$type} berhasil dijalankan.",
                'output' => $output
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menjalankan maintenance: ' . $e->getMessage()
            ]);
        }
    }

    public function getBackupList()
    {
        try {
            $backupPath = storage_path('app/backups');
            $backups = [];

            if (is_dir($backupPath)) {
                $files = glob($backupPath . '/backup_database_*.sql');

                foreach ($files as $file) {
                    $backups[] = [
                        'name' => basename($file),
                        'size' => $this->formatBytes(filesize($file)),
                        'date' => date('d M Y H:i:s', filemtime($file)),
                        'path' => $file
                    ];
                }

                // Sort by date (newest first)
                usort($backups, function($a, $b) {
                    return filemtime($b['path']) - filemtime($a['path']);
                });
            }

            return response()->json([
                'success' => true,
                'backups' => $backups
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil daftar backup: ' . $e->getMessage()
            ]);
        }
    }

    public function downloadBackup(Request $request)
    {
        $request->validate([
            'filename' => 'required|string'
        ]);

        try {
            $filename = $request->filename;
            $filePath = storage_path('app/backups/' . $filename);

            if (!file_exists($filePath) || !str_contains($filename, 'backup_database_')) {
                return response()->json([
                    'success' => false,
                    'message' => 'File backup tidak ditemukan.'
                ], 404);
            }

            // Log download activity
            \App\Models\SecurityLog::logEvent('backup_downloaded', auth()->id(), [
                'backup_file' => $filename,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->download($filePath);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mendownload backup: ' . $e->getMessage()
            ]);
        }
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
