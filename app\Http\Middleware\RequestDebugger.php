<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RequestDebugger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only debug news store requests
        if ($request->is('admin/content/news') && $request->isMethod('POST')) {
            Log::info('=== REQUEST DEBUGGER START ===', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'headers' => $request->headers->all(),
                'session_id' => $request->session()->getId(),
                'user_id' => auth()->id(),
                'csrf_token' => $request->header('X-CSRF-TOKEN'),
                'content_length' => $request->header('Content-Length'),
                'content_type' => $request->header('Content-Type'),
                'memory_before' => memory_get_usage(true),
                'time_start' => microtime(true)
            ]);

            // Log request data (excluding sensitive info)
            Log::info('Request Data', [
                'all_except_token' => $request->except(['_token', 'featured_image']),
                'has_featured_image' => $request->hasFile('featured_image'),
                'featured_image_info' => $request->hasFile('featured_image') ? [
                    'name' => $request->file('featured_image')->getClientOriginalName(),
                    'size' => $request->file('featured_image')->getSize(),
                    'mime' => $request->file('featured_image')->getMimeType()
                ] : null
            ]);
        }

        $response = $next($request);

        // Log response for news store requests
        if ($request->is('admin/content/news') && $request->isMethod('POST')) {
            Log::info('=== REQUEST DEBUGGER END ===', [
                'status_code' => $response->getStatusCode(),
                'response_headers' => $response->headers->all(),
                'memory_after' => memory_get_usage(true),
                'time_end' => microtime(true),
                'execution_time' => microtime(true) - (request()->attributes->get('start_time') ?? microtime(true))
            ]);

            // Log redirect information if it's a redirect
            if ($response->isRedirect()) {
                Log::info('Response is redirect', [
                    'redirect_to' => $response->headers->get('Location'),
                    'status_code' => $response->getStatusCode()
                ]);
            }
        }

        return $response;
    }
}
